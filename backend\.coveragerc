# backend/.coveragerc
# Coverage configuration for Ultimate Electrical Designer Backend

[run]
# Source code directories to measure
source = core, api

# Files to include in coverage
include = 
    core/*
    api/*

# Files to exclude from coverage
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */alembic/*
    */venv/*
    */.venv/*
    */node_modules/*
    */conftest.py
    */setup.py
    */manage.py
    */wsgi.py
    */asgi.py
    */.pytest_cache/*
    */htmlcov/*

# Enable branch coverage
branch = True

# Parallel processing
parallel = True

# Data file location
data_file = .coverage

[report]
# Reporting options
precision = 2
show_missing = True
skip_covered = False
skip_empty = False

# Exclude lines from coverage
exclude_lines =
    # Have to re-enable the standard pragma
    pragma: no cover
    
    # Don't complain about missing debug-only code:
    def __repr__
    if self\.debug
    
    # Don't complain if tests don't hit defensive assertion code:
    raise AssertionError
    raise NotImplementedError
    
    # Don't complain if non-runnable code isn't run:
    if 0:
    if __name__ == .__main__.:
    if TYPE_CHECKING:
    
    # Don't complain about abstract methods
    @(abc\.)?abstractmethod
    
    # Don't complain about platform specific code
    if sys.platform
    
    # Don't complain about logger statements
    logger\.debug
    logger\.info
    
    # Don't complain about pass statements
    pass

# Ignore errors
ignore_errors = True

# Minimum coverage percentage
fail_under = 85

# Sort results
sort = Cover

[html]
# HTML report configuration
directory = htmlcov
title = Ultimate Electrical Designer Backend Coverage Report
show_contexts = True

[xml]
# XML report configuration
output = coverage.xml

[json]
# JSON report configuration
output = coverage.json
show_contexts = True

[paths]
# Path mapping for different environments
source =
    core/
    api/
    /app/core/
    /app/api/
    C:\*\core\
    C:\*\api\
