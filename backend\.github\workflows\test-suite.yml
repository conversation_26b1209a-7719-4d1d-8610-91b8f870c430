# .github/workflows/test-suite.yml
name: Ultimate Electrical Designer - Test Suite

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - '.github/workflows/test-suite.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - '.github/workflows/test-suite.yml'
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.11'
  POETRY_VERSION: '1.6.1'

jobs:
  test-matrix:
    name: Test Suite (${{ matrix.test-category }})
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        test-category:
          - critical
          - unit
          - integration
          - api
          - edge_cases
          - benchmarks
          - security
          - phase2
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        virtualenvs-create: true
        virtualenvs-in-project: true
        
    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: backend/.venv
        key: venv-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}
        
    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      working-directory: backend
      run: poetry install --no-interaction --no-root
      
    - name: Install project
      working-directory: backend
      run: poetry install --no-interaction
      
    - name: Set up test database
      working-directory: backend
      run: |
        poetry run python -c "
        from core.database import engine
        from core.models import Base
        Base.metadata.create_all(bind=engine)
        print('Test database created successfully')
        "
        
    - name: Run ${{ matrix.test-category }} tests
      working-directory: backend
      run: |
        poetry run python scripts/test_runner.py ${{ matrix.test-category }} --coverage
        
    - name: Generate coverage report
      if: matrix.test-category == 'phase2'
      working-directory: backend
      run: |
        poetry run coverage xml
        poetry run coverage html
        
    - name: Upload coverage to Codecov
      if: matrix.test-category == 'phase2'
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage.xml
        directory: backend
        flags: backend
        name: backend-coverage
        
    - name: Upload coverage artifacts
      if: matrix.test-category == 'phase2'
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: backend/htmlcov/
        
    - name: Performance benchmark results
      if: matrix.test-category == 'benchmarks'
      working-directory: backend
      run: |
        echo "## Performance Benchmark Results" >> $GITHUB_STEP_SUMMARY
        echo "Test category: ${{ matrix.test-category }}" >> $GITHUB_STEP_SUMMARY
        echo "Python version: ${{ env.PYTHON_VERSION }}" >> $GITHUB_STEP_SUMMARY
        echo "Runner: ${{ runner.os }}" >> $GITHUB_STEP_SUMMARY

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test-matrix
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python ${{ env.PYTHON_VERSION }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        
    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}
        
    - name: Install dependencies
      working-directory: backend
      run: poetry install --no-interaction
      
    - name: Run Bandit security scan
      working-directory: backend
      run: |
        poetry add bandit[toml] --group dev
        poetry run bandit -r . -f json -o bandit-report.json || true
        poetry run bandit -r . -f txt
        
    - name: Run Safety security scan
      working-directory: backend
      run: |
        poetry add safety --group dev
        poetry run safety check --json --output safety-report.json || true
        poetry run safety check
        
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          backend/bandit-report.json
          backend/safety-report.json

  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [test-matrix, security-scan]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download coverage report
      uses: actions/download-artifact@v3
      with:
        name: coverage-report
        path: coverage-report/
        
    - name: Quality Gate Check
      run: |
        echo "## Quality Gate Results" >> $GITHUB_STEP_SUMMARY
        echo "### Test Results" >> $GITHUB_STEP_SUMMARY
        
        # Check if critical tests passed
        if [[ "${{ needs.test-matrix.result }}" == "success" ]]; then
          echo "✅ All test categories passed" >> $GITHUB_STEP_SUMMARY
        else
          echo "❌ Some test categories failed" >> $GITHUB_STEP_SUMMARY
          exit 1
        fi
        
        # Check security scan
        if [[ "${{ needs.security-scan.result }}" == "success" ]]; then
          echo "✅ Security scan completed" >> $GITHUB_STEP_SUMMARY
        else
          echo "⚠️ Security scan had issues" >> $GITHUB_STEP_SUMMARY
        fi
        
        echo "### Quality Metrics" >> $GITHUB_STEP_SUMMARY
        echo "- Test execution: ${{ needs.test-matrix.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Security scan: ${{ needs.security-scan.result }}" >> $GITHUB_STEP_SUMMARY
        echo "- Coverage report: Available in artifacts" >> $GITHUB_STEP_SUMMARY

  notification:
    name: Notification
    runs-on: ubuntu-latest
    needs: [quality-gate]
    if: always() && github.event_name == 'push'
    
    steps:
    - name: Notify on success
      if: needs.quality-gate.result == 'success'
      run: |
        echo "🎉 All tests passed successfully!"
        echo "Quality gate: PASSED ✅"
        
    - name: Notify on failure
      if: needs.quality-gate.result != 'success'
      run: |
        echo "❌ Quality gate failed!"
        echo "Please check the test results and fix any issues."
        exit 1
