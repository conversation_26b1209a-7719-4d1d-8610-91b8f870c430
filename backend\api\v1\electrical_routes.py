# backend/api/v1/electrical_routes.py
"""
Electrical API Routes

This module provides REST API endpoints for electrical design operations including:
- CRUD operations for electrical entities
- Cable sizing and voltage drop calculations
- Electrical standards validation
- Design workflow orchestration
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

try:
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        CalculationError,
        DatabaseError,
        InvalidInputError,
        NotFoundError,
        StandardComplianceError,
    )
    from core.repositories.electrical_repository import (
        CableRouteRepository,
        CableSegmentRepository,
        ElectricalNodeRepository,
        LoadCalculationRepository,
        VoltageDropCalculationRepository,
    )
    from core.schemas.electrical_schemas import (
        CableSizingCalculationInputSchema,
        CableSizingCalculationResultSchema,
        CableRouteCreateSchema,
        CableRouteListResponseSchema,
        CableRouteReadSchema,
        CableRouteSummarySchema,
        CableRouteUpdateSchema,
        CableSegmentCreateSchema,
        CableSegmentListResponseSchema,
        CableSegmentReadSchema,
        CableSegmentSummarySchema,
        CableSegmentUpdateSchema,
        ElectricalDesignInputSchema,
        ElectricalDesignResultSchema,
        ElectricalNodeCreateSchema,
        ElectricalNodeListResponseSchema,
        ElectricalNodeReadSchema,
        ElectricalNodeSummarySchema,
        ElectricalNodeUpdateSchema,
        ElectricalStandardsValidationInputSchema,
        ElectricalStandardsValidationResultSchema,
        LoadCalculationCreateSchema,
        LoadCalculationListResponseSchema,
        LoadCalculationReadSchema,
        LoadCalculationSummarySchema,
        LoadCalculationUpdateSchema,
        VoltageDropCalculationCreateSchema,
        VoltageDropCalculationInputSchema,
        VoltageDropCalculationListResponseSchema,
        VoltageDropCalculationReadSchema,
        VoltageDropCalculationResultSchema,
        VoltageDropCalculationSummarySchema,
        VoltageDropCalculationUpdateSchema,
    )
    from core.services.electrical_service import ElectricalService
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.database.session import get_db_session
    from core.errors.exceptions import (
        BaseApplicationException,
        CalculationError,
        DatabaseError,
        InvalidInputError,
        NotFoundError,
        StandardComplianceError,
    )
    from core.repositories.electrical_repository import (
        CableRouteRepository,
        CableSegmentRepository,
        ElectricalNodeRepository,
        LoadCalculationRepository,
        VoltageDropCalculationRepository,
    )
    from core.schemas.electrical_schemas import (
        CableSizingCalculationInputSchema,
        CableSizingCalculationResultSchema,
        CableRouteCreateSchema,
        CableRouteListResponseSchema,
        CableRouteReadSchema,
        CableRouteSummarySchema,
        CableRouteUpdateSchema,
        CableSegmentCreateSchema,
        CableSegmentListResponseSchema,
        CableSegmentReadSchema,
        CableSegmentSummarySchema,
        CableSegmentUpdateSchema,
        ElectricalDesignInputSchema,
        ElectricalDesignResultSchema,
        ElectricalNodeCreateSchema,
        ElectricalNodeListResponseSchema,
        ElectricalNodeReadSchema,
        ElectricalNodeSummarySchema,
        ElectricalNodeUpdateSchema,
        ElectricalStandardsValidationInputSchema,
        ElectricalStandardsValidationResultSchema,
        LoadCalculationCreateSchema,
        LoadCalculationListResponseSchema,
        LoadCalculationReadSchema,
        LoadCalculationSummarySchema,
        LoadCalculationUpdateSchema,
        VoltageDropCalculationCreateSchema,
        VoltageDropCalculationInputSchema,
        VoltageDropCalculationListResponseSchema,
        VoltageDropCalculationReadSchema,
        VoltageDropCalculationResultSchema,
        VoltageDropCalculationSummarySchema,
        VoltageDropCalculationUpdateSchema,
    )
    from core.services.electrical_service import ElectricalService

# Initialize logger for this module
logger = get_logger(__name__)

# Create router
router = APIRouter(tags=["electrical"])


def handle_electrical_exceptions(e: Exception) -> HTTPException:
    """
    Handle electrical-specific exceptions and convert to HTTP responses.

    Args:
        e: Exception to handle

    Returns:
        HTTPException: Appropriate HTTP exception
    """
    if isinstance(e, NotFoundError):
        logger.warning(f"Resource not found: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.detail,
        )
    elif isinstance(e, InvalidInputError):
        logger.warning(f"Invalid input: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.detail,
        )
    elif isinstance(e, CalculationError):
        logger.error(f"Calculation error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.detail,
        )
    elif isinstance(e, StandardComplianceError):
        logger.error(f"Standards compliance error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.detail,
        )
    elif isinstance(e, DatabaseError):
        logger.error(f"Database error: {e.detail}")
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
    elif isinstance(e, BaseApplicationException):
        logger.error(f"Application error: {e.detail}")
        return HTTPException(
            status_code=e.status_code,
            detail=e.detail,
        )
    else:
        logger.error(f"Unexpected error: {str(e)}", exc_info=True)
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# ============================================================================
# ELECTRICAL NODE ENDPOINTS
# ============================================================================


@router.post(
    "/nodes",
    response_model=ElectricalNodeReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create electrical node",
    description="Create a new electrical node with specified properties",
)
async def create_electrical_node(
    node_data: ElectricalNodeCreateSchema,
    db: Session = Depends(get_db_session),
) -> ElectricalNodeReadSchema:
    """Create a new electrical node."""
    logger.info(f"Creating electrical node: {node_data.name}")

    try:
        electrical_node_repo = ElectricalNodeRepository(db)
        electrical_node = electrical_node_repo.create(node_data.model_dump())
        db.commit()

        logger.info(
            f"Created electrical node {electrical_node.id}: {electrical_node.name}"
        )
        return ElectricalNodeReadSchema.model_validate(electrical_node)

    except Exception as e:
        db.rollback()
        raise handle_electrical_exceptions(e)


@router.get(
    "/nodes/{node_id}",
    response_model=ElectricalNodeReadSchema,
    summary="Get electrical node",
    description="Retrieve a specific electrical node by ID",
)
async def get_electrical_node(
    node_id: int,
    db: Session = Depends(get_db_session),
) -> ElectricalNodeReadSchema:
    """Get a specific electrical node by ID."""
    logger.debug(f"Retrieving electrical node {node_id}")

    try:
        electrical_node_repo = ElectricalNodeRepository(db)
        electrical_node = electrical_node_repo.get_by_id(node_id)

        if not electrical_node:
            raise NotFoundError(
                code="ELECTRICAL_NODE_NOT_FOUND",
                detail=f"Electrical node {node_id} not found",
            )

        return ElectricalNodeReadSchema.model_validate(electrical_node)

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.get(
    "/nodes",
    response_model=ElectricalNodeListResponseSchema,
    summary="List electrical nodes",
    description="Retrieve a paginated list of electrical nodes for a project",
)
async def list_electrical_nodes(
    project_id: int = Query(..., description="Project ID to filter by"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    node_type: Optional[str] = Query(None, description="Filter by node type"),
    db: Session = Depends(get_db_session),
) -> ElectricalNodeListResponseSchema:
    """List electrical nodes with optional filtering."""
    logger.debug(f"Listing electrical nodes for project {project_id}")

    try:
        electrical_node_repo = ElectricalNodeRepository(db)

        if node_type:
            electrical_nodes = electrical_node_repo.get_by_node_type(
                project_id, node_type, skip, limit
            )
        else:
            electrical_nodes = electrical_node_repo.get_by_project_id(
                project_id, skip, limit
            )

        total = electrical_node_repo.count_by_project(project_id)

        electrical_node_summaries = [
            ElectricalNodeSummarySchema.model_validate(node)
            for node in electrical_nodes
        ]

        return ElectricalNodeListResponseSchema(
            electrical_nodes=electrical_node_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=(total + limit - 1) // limit,
        )

    except Exception as e:
        raise handle_electrical_exceptions(e)


# ============================================================================
# CABLE ROUTE ENDPOINTS
# ============================================================================


@router.post(
    "/cable-routes",
    response_model=CableRouteReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create cable route",
    description="Create a new cable route between electrical nodes",
)
async def create_cable_route(
    route_data: CableRouteCreateSchema,
    db: Session = Depends(get_db_session),
) -> CableRouteReadSchema:
    """Create a new cable route."""
    logger.info(f"Creating cable route: {route_data.name}")

    try:
        cable_route_repo = CableRouteRepository(db)
        cable_route = cable_route_repo.create(route_data.model_dump())
        db.commit()

        logger.info(f"Created cable route {cable_route.id}: {cable_route.name}")
        return CableRouteReadSchema.model_validate(cable_route)

    except Exception as e:
        db.rollback()
        raise handle_electrical_exceptions(e)


@router.get(
    "/cable-routes/{route_id}",
    response_model=CableRouteReadSchema,
    summary="Get cable route",
    description="Retrieve a specific cable route by ID",
)
async def get_cable_route(
    route_id: int,
    db: Session = Depends(get_db_session),
) -> CableRouteReadSchema:
    """Get a specific cable route by ID."""
    logger.debug(f"Retrieving cable route {route_id}")

    try:
        cable_route_repo = CableRouteRepository(db)
        cable_route = cable_route_repo.get_by_id(route_id)

        if not cable_route:
            raise NotFoundError(
                code="CABLE_ROUTE_NOT_FOUND", detail=f"Cable route {route_id} not found"
            )

        return CableRouteReadSchema.model_validate(cable_route)

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.get(
    "/cable-routes",
    response_model=CableRouteListResponseSchema,
    summary="List cable routes",
    description="Retrieve a paginated list of cable routes for a project",
)
async def list_cable_routes(
    project_id: int = Query(..., description="Project ID to filter by"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    node_id: Optional[int] = Query(None, description="Filter by connected node ID"),
    installation_method: Optional[str] = Query(
        None, description="Filter by installation method"
    ),
    db: Session = Depends(get_db_session),
) -> CableRouteListResponseSchema:
    """List cable routes with optional filtering."""
    logger.debug(f"Listing cable routes for project {project_id}")

    try:
        cable_route_repo = CableRouteRepository(db)

        if node_id:
            cable_routes = cable_route_repo.get_routes_by_node(node_id, "both")
        elif installation_method:
            cable_routes = cable_route_repo.get_routes_by_installation_method(
                project_id, installation_method
            )
        else:
            cable_routes = cable_route_repo.get_by_project_id(project_id, skip, limit)

        total = cable_route_repo.count_by_project(project_id)

        cable_route_summaries = [
            CableRouteSummarySchema.model_validate(route) for route in cable_routes
        ]

        return CableRouteListResponseSchema(
            cable_routes=cable_route_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=(total + limit - 1) // limit,
        )

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.put(
    "/cable-routes/{route_id}",
    response_model=CableRouteReadSchema,
    summary="Update cable route",
    description="Update an existing cable route",
)
async def update_cable_route(
    route_id: int,
    route_data: CableRouteUpdateSchema,
    db: Session = Depends(get_db_session),
) -> CableRouteReadSchema:
    """Update an existing cable route."""
    logger.info(f"Updating cable route {route_id}")

    try:
        cable_route_repo = CableRouteRepository(db)

        # Check if route exists
        existing_route = cable_route_repo.get_by_id(route_id)
        if not existing_route:
            raise NotFoundError(
                code="CABLE_ROUTE_NOT_FOUND", detail=f"Cable route {route_id} not found"
            )

        # Update route
        update_data = route_data.model_dump(exclude_unset=True)
        cable_route = cable_route_repo.update(route_id, update_data)
        db.commit()

        logger.info(f"Updated cable route {route_id}")
        return CableRouteReadSchema.model_validate(cable_route)

    except Exception as e:
        db.rollback()
        raise handle_electrical_exceptions(e)


@router.delete(
    "/cable-routes/{route_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete cable route",
    description="Soft delete a cable route",
)
async def delete_cable_route(
    route_id: int,
    db: Session = Depends(get_db_session),
) -> None:
    """Soft delete a cable route."""
    logger.info(f"Deleting cable route {route_id}")

    try:
        cable_route_repo = CableRouteRepository(db)

        # Check if route exists
        existing_route = cable_route_repo.get_by_id(route_id)
        if not existing_route:
            raise NotFoundError(
                code="CABLE_ROUTE_NOT_FOUND", detail=f"Cable route {route_id} not found"
            )

        # Soft delete
        cable_route_repo.soft_delete(route_id)
        db.commit()

        logger.info(f"Deleted cable route {route_id}")

    except Exception as e:
        db.rollback()
        raise handle_electrical_exceptions(e)


# ============================================================================
# LOAD CALCULATION ENDPOINTS
# ============================================================================


@router.post(
    "/load-calculations",
    response_model=LoadCalculationReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create load calculation",
    description="Create a new electrical load calculation",
)
async def create_load_calculation(
    load_data: LoadCalculationCreateSchema,
    db: Session = Depends(get_db_session),
) -> LoadCalculationReadSchema:
    """Create a new load calculation."""
    logger.info(f"Creating load calculation: {load_data.name}")

    try:
        load_calc_repo = LoadCalculationRepository(db)
        load_calculation = load_calc_repo.create(load_data.model_dump())
        db.commit()

        logger.info(
            f"Created load calculation {load_calculation.id}: {load_calculation.name}"
        )
        return LoadCalculationReadSchema.model_validate(load_calculation)

    except Exception as e:
        db.rollback()
        raise handle_electrical_exceptions(e)


@router.get(
    "/load-calculations/{calc_id}",
    response_model=LoadCalculationReadSchema,
    summary="Get load calculation",
    description="Retrieve a specific load calculation by ID",
)
async def get_load_calculation(
    calc_id: int,
    db: Session = Depends(get_db_session),
) -> LoadCalculationReadSchema:
    """Get a specific load calculation by ID."""
    logger.debug(f"Retrieving load calculation {calc_id}")

    try:
        load_calc_repo = LoadCalculationRepository(db)
        load_calculation = load_calc_repo.get_by_id(calc_id)

        if not load_calculation:
            raise NotFoundError(
                code="LOAD_CALCULATION_NOT_FOUND",
                detail=f"Load calculation {calc_id} not found",
            )

        return LoadCalculationReadSchema.model_validate(load_calculation)

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.get(
    "/load-calculations",
    response_model=LoadCalculationListResponseSchema,
    summary="List load calculations",
    description="Retrieve a paginated list of load calculations",
)
async def list_load_calculations(
    project_id: int = Query(..., description="Project ID to filter by"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(
        10, ge=1, le=100, description="Maximum number of records to return"
    ),
    electrical_node_id: Optional[int] = Query(
        None, description="Filter by electrical node ID"
    ),
    load_type: Optional[str] = Query(None, description="Filter by load type"),
    db: Session = Depends(get_db_session),
) -> LoadCalculationListResponseSchema:
    """List load calculations with optional filtering."""
    logger.debug(f"Listing load calculations for project {project_id}")

    try:
        load_calc_repo = LoadCalculationRepository(db)

        if electrical_node_id:
            load_calculations = load_calc_repo.get_by_electrical_node_id(
                electrical_node_id, skip, limit
            )
        elif load_type:
            load_calculations = load_calc_repo.get_by_load_type(
                project_id, load_type, skip, limit
            )
        else:
            load_calculations = load_calc_repo.get_by_project_id(
                project_id, skip, limit
            )

        # Count total for pagination
        total = len(load_calc_repo.get_by_project_id(project_id))

        load_calc_summaries = [
            LoadCalculationSummarySchema.model_validate(calc)
            for calc in load_calculations
        ]

        return LoadCalculationListResponseSchema(
            load_calculations=load_calc_summaries,
            total=total,
            page=(skip // limit) + 1,
            per_page=limit,
            total_pages=(total + limit - 1) // limit,
        )

    except Exception as e:
        raise handle_electrical_exceptions(e)


# ============================================================================
# CALCULATION ENDPOINTS
# ============================================================================


@router.post(
    "/calculations/cable-sizing",
    response_model=CableSizingCalculationResultSchema,
    summary="Perform cable sizing calculation",
    description="Calculate optimal cable size for electrical loads",
)
async def calculate_cable_sizing(
    calculation_input: CableSizingCalculationInputSchema,
    db: Session = Depends(get_db_session),
) -> CableSizingCalculationResultSchema:
    """Perform cable sizing calculation."""
    logger.info(
        f"Performing cable sizing calculation for {calculation_input.required_power_kw}kW load"
    )

    try:
        electrical_service = ElectricalService(db)
        result = electrical_service.perform_cable_sizing_calculation(calculation_input)

        logger.info(
            f"Cable sizing calculation completed: {result.recommended_cable_type}"
        )
        return result

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.post(
    "/calculations/voltage-drop",
    response_model=VoltageDropCalculationResultSchema,
    summary="Perform voltage drop calculation",
    description="Calculate voltage drop for cable routes",
)
async def calculate_voltage_drop(
    calculation_input: VoltageDropCalculationInputSchema,
    db: Session = Depends(get_db_session),
) -> VoltageDropCalculationResultSchema:
    """Perform voltage drop calculation."""
    logger.info(
        f"Performing voltage drop calculation for {calculation_input.load_current_a}A load"
    )

    try:
        electrical_service = ElectricalService(db)
        result = electrical_service.perform_voltage_drop_calculation(calculation_input)

        logger.info(
            f"Voltage drop calculation completed: {result.calculated_voltage_drop_percent:.1f}%"
        )
        return result

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.post(
    "/calculations/standards-validation",
    response_model=ElectricalStandardsValidationResultSchema,
    summary="Validate electrical design against standards",
    description="Validate electrical design against applicable engineering standards",
)
async def validate_electrical_standards(
    validation_input: ElectricalStandardsValidationInputSchema,
    db: Session = Depends(get_db_session),
) -> ElectricalStandardsValidationResultSchema:
    """Validate electrical design against standards."""
    logger.info(
        f"Validating electrical design against {len(validation_input.project_standards)} standards"
    )

    try:
        electrical_service = ElectricalService(db)
        result = electrical_service.validate_electrical_standards(validation_input)

        logger.info(f"Standards validation completed: compliant={result.is_compliant}")
        return result

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.get(
    "/nodes/{node_id}/load-summary",
    response_model=Dict[str, Any],
    summary="Get electrical node load summary",
    description="Calculate total electrical load for a specific electrical node",
)
async def get_node_load_summary(
    node_id: int,
    db: Session = Depends(get_db_session),
) -> Dict[str, Any]:
    """Get electrical node load summary."""
    logger.info(f"Calculating load summary for electrical node {node_id}")

    try:
        electrical_service = ElectricalService(db)
        result = electrical_service.calculate_load_for_electrical_node(node_id)

        logger.info(
            f"Load summary calculated for node {node_id}: {result['diversified_power_kw']:.1f}kW"
        )
        return result

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.post(
    "/cable-routes/{route_id}/optimize",
    response_model=Dict[str, Any],
    summary="Optimize cable route",
    description="Optimize cable route for cost, voltage drop, or other criteria",
)
async def optimize_cable_route(
    route_id: int,
    optimization_criteria: Dict[str, Any],
    db: Session = Depends(get_db_session),
) -> Dict[str, Any]:
    """Optimize cable route."""
    logger.info(f"Optimizing cable route {route_id}")

    try:
        electrical_service = ElectricalService(db)
        result = electrical_service.optimize_cable_route(
            route_id, optimization_criteria
        )

        logger.info(f"Cable route optimization completed for route {route_id}")
        return result

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.post(
    "/design-workflow",
    response_model=ElectricalDesignResultSchema,
    summary="Perform electrical design workflow",
    description="Execute complete electrical design workflow for a project",
)
async def perform_electrical_design_workflow(
    design_input: ElectricalDesignInputSchema,
    db: Session = Depends(get_db_session),
) -> ElectricalDesignResultSchema:
    """Perform complete electrical design workflow."""
    logger.info(
        f"Starting electrical design workflow for project {design_input.project_id}"
    )

    try:
        electrical_service = ElectricalService(db)
        result = electrical_service.perform_electrical_design_workflow(design_input)

        logger.info(
            f"Electrical design workflow completed for project {design_input.project_id}: "
            f"{len(result.warnings)} warnings, {len(result.errors)} errors"
        )
        return result

    except Exception as e:
        raise handle_electrical_exceptions(e)


@router.put(
    "/nodes/{node_id}",
    response_model=ElectricalNodeReadSchema,
    summary="Update electrical node",
    description="Update an existing electrical node",
)
async def update_electrical_node(
    node_id: int,
    node_data: ElectricalNodeUpdateSchema,
    db: Session = Depends(get_db_session),
) -> ElectricalNodeReadSchema:
    """Update an existing electrical node."""
    logger.info(f"Updating electrical node {node_id}")

    try:
        electrical_node_repo = ElectricalNodeRepository(db)

        # Check if node exists
        existing_node = electrical_node_repo.get_by_id(node_id)
        if not existing_node:
            raise NotFoundError(
                code="ELECTRICAL_NODE_NOT_FOUND",
                detail=f"Electrical node {node_id} not found",
            )

        # Update node
        update_data = node_data.model_dump(exclude_unset=True)
        electrical_node = electrical_node_repo.update(node_id, update_data)
        db.commit()

        logger.info(f"Updated electrical node {node_id}")
        return ElectricalNodeReadSchema.model_validate(electrical_node)

    except Exception as e:
        db.rollback()
        raise handle_electrical_exceptions(e)


@router.delete(
    "/nodes/{node_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete electrical node",
    description="Soft delete an electrical node",
)
async def delete_electrical_node(
    node_id: int,
    db: Session = Depends(get_db_session),
) -> None:
    """Soft delete an electrical node."""
    logger.info(f"Deleting electrical node {node_id}")

    try:
        electrical_node_repo = ElectricalNodeRepository(db)

        # Check if node exists
        existing_node = electrical_node_repo.get_by_id(node_id)
        if not existing_node:
            raise NotFoundError(
                code="ELECTRICAL_NODE_NOT_FOUND",
                detail=f"Electrical node {node_id} not found",
            )

        # Soft delete
        electrical_node_repo.soft_delete(node_id)
        db.commit()

        logger.info(f"Deleted electrical node {node_id}")

    except Exception as e:
        db.rollback()
        raise handle_electrical_exceptions(e)
