# backend/api/v1/import_export_routes.py
"""
Import/Export API Routes.

This module provides REST API endpoints for data import and export operations,
supporting both global catalog and project-specific data.
"""

import logging
from typing import Dict, Any, Optional
from pathlib import Path
import tempfile
import shutil

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field

from core.data_import import ImportService
from core.errors.exceptions import InvalidInputError, CalculationError
from api.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/import-export", tags=["import-export"])


# Pydantic models for request/response
class ImportRequest(BaseModel):
    """Request model for data import operations."""
    catalog_type: Optional[str] = Field(None, description="Type of catalog for global imports")
    data_type: Optional[str] = Field(None, description="Type of data for project imports")
    field_mapping: Optional[Dict[str, str]] = Field(None, description="Custom field mapping")
    sheet_name: Optional[str] = Field(None, description="Sheet name for Excel files")
    validate_only: bool = Field(False, description="Only validate without importing")
    transaction_id: Optional[str] = Field(None, description="Transaction ID for tracking")


class MultiSheetImportRequest(BaseModel):
    """Request model for multi-sheet import operations."""
    sheet_mappings: Dict[str, Dict[str, Any]] = Field(..., description="Sheet mapping configurations")
    validate_only: bool = Field(False, description="Only validate without importing")
    transaction_id: Optional[str] = Field(None, description="Transaction ID for tracking")


class TemplateRequest(BaseModel):
    """Request model for template generation."""
    template_type: str = Field(..., description="Type of template (global, project)")
    data_type: str = Field(..., description="Type of data or catalog")
    format: str = Field("xlsx", description="File format (xlsx, csv, json)")


class ImportResponse(BaseModel):
    """Response model for import operations."""
    success: bool
    message: str
    transaction_id: str
    import_type: str
    total_records: int
    imported_records: int
    validation_errors: int
    validation_warnings: int


# Global catalog import endpoints
@router.post("/admin/import/cable_catalog", response_model=ImportResponse)
async def import_cable_catalog(
    file: UploadFile = File(...),
    request_data: str = Form(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Import cable catalog data from uploaded file.
    
    Requires admin privileges for global catalog imports.
    """
    logger.info(f"Cable catalog import requested by user: {current_user.get('id')}")

    try:
        # Parse request data
        import json
        request = ImportRequest.parse_raw(request_data)

        # Validate user permissions (admin required for global imports)
        if not current_user.get("is_admin", False):
            raise HTTPException(status_code=403, detail="Admin privileges required for global imports")

        # Save uploaded file temporarily
        temp_file_path = await _save_uploaded_file(file)

        try:
            # Initialize import service
            import_service = ImportService()

            # Perform import
            result = import_service.import_global_data(
                file_path=temp_file_path,
                catalog_type="cables",
                field_mapping=request.field_mapping,
                sheet_name=request.sheet_name,
                validate_only=request.validate_only,
                transaction_id=request.transaction_id,
            )

            # Prepare response
            response = ImportResponse(
                success=result["success"],
                message="Cable catalog import completed successfully" if result["success"] else "Cable catalog import failed",
                transaction_id=result.get("transaction_id", ""),
                import_type="cable_catalog",
                total_records=result["total_records"],
                imported_records=result.get("imported_records", 0),
                validation_errors=result["validation"]["error_count"],
                validation_warnings=result["validation"]["warning_count"],
            )

            logger.info(f"Cable catalog import completed: {result['success']}")
            return response

        finally:
            # Clean up temporary file
            Path(temp_file_path).unlink(missing_ok=True)

    except InvalidInputError as e:
        logger.error(f"Invalid input for cable catalog import: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        logger.error(f"Cable catalog import failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in cable catalog import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/admin/import/material_catalog", response_model=ImportResponse)
async def import_material_catalog(
    file: UploadFile = File(...),
    request_data: str = Form(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Import material catalog data from uploaded file."""
    logger.info(f"Material catalog import requested by user: {current_user.get('id')}")

    try:
        import json
        request = ImportRequest.parse_raw(request_data)

        if not current_user.get("is_admin", False):
            raise HTTPException(status_code=403, detail="Admin privileges required for global imports")

        temp_file_path = await _save_uploaded_file(file)

        try:
            import_service = ImportService()
            result = import_service.import_global_data(
                file_path=temp_file_path,
                catalog_type="materials",
                field_mapping=request.field_mapping,
                sheet_name=request.sheet_name,
                validate_only=request.validate_only,
                transaction_id=request.transaction_id,
            )

            response = ImportResponse(
                success=result["success"],
                message="Material catalog import completed successfully" if result["success"] else "Material catalog import failed",
                transaction_id=result.get("transaction_id", ""),
                import_type="material_catalog",
                total_records=result["total_records"],
                imported_records=result.get("imported_records", 0),
                validation_errors=result["validation"]["error_count"],
                validation_warnings=result["validation"]["warning_count"],
            )

            return response

        finally:
            Path(temp_file_path).unlink(missing_ok=True)

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in material catalog import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/admin/import/standards_catalog", response_model=ImportResponse)
async def import_standards_catalog(
    file: UploadFile = File(...),
    request_data: str = Form(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Import standards catalog data from uploaded file."""
    logger.info(f"Standards catalog import requested by user: {current_user.get('id')}")

    try:
        import json
        request = ImportRequest.parse_raw(request_data)

        if not current_user.get("is_admin", False):
            raise HTTPException(status_code=403, detail="Admin privileges required for global imports")

        temp_file_path = await _save_uploaded_file(file)

        try:
            import_service = ImportService()
            result = import_service.import_global_data(
                file_path=temp_file_path,
                catalog_type="standards",
                field_mapping=request.field_mapping,
                sheet_name=request.sheet_name,
                validate_only=request.validate_only,
                transaction_id=request.transaction_id,
            )

            response = ImportResponse(
                success=result["success"],
                message="Standards catalog import completed successfully" if result["success"] else "Standards catalog import failed",
                transaction_id=result.get("transaction_id", ""),
                import_type="standards_catalog",
                total_records=result["total_records"],
                imported_records=result.get("imported_records", 0),
                validation_errors=result["validation"]["error_count"],
                validation_warnings=result["validation"]["warning_count"],
            )

            return response

        finally:
            Path(temp_file_path).unlink(missing_ok=True)

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in standards catalog import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Project-specific import endpoints
@router.post("/projects/{project_id}/import/pipes", response_model=ImportResponse)
async def import_project_pipes(
    project_id: str,
    file: UploadFile = File(...),
    request_data: str = Form(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Import pipes data for a specific project."""
    logger.info(f"Pipes import requested for project {project_id} by user: {current_user.get('id')}")

    try:
        import json
        request = ImportRequest.parse_raw(request_data)

        temp_file_path = await _save_uploaded_file(file)

        try:
            import_service = ImportService()
            result = import_service.import_project_data(
                project_id=project_id,
                file_path=temp_file_path,
                data_type="pipes",
                field_mapping=request.field_mapping,
                sheet_name=request.sheet_name,
                validate_only=request.validate_only,
                transaction_id=request.transaction_id,
            )

            response = ImportResponse(
                success=result["success"],
                message="Pipes import completed successfully" if result["success"] else "Pipes import failed",
                transaction_id=result.get("transaction_id", ""),
                import_type="pipes",
                total_records=result["total_records"],
                imported_records=result.get("imported_records", 0),
                validation_errors=result["validation"]["error_count"],
                validation_warnings=result["validation"]["warning_count"],
            )

            return response

        finally:
            Path(temp_file_path).unlink(missing_ok=True)

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in pipes import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/projects/{project_id}/import/components", response_model=ImportResponse)
async def import_project_components(
    project_id: str,
    file: UploadFile = File(...),
    request_data: str = Form(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Import components data for a specific project."""
    logger.info(f"Components import requested for project {project_id} by user: {current_user.get('id')}")

    try:
        import json
        request = ImportRequest.parse_raw(request_data)

        temp_file_path = await _save_uploaded_file(file)

        try:
            import_service = ImportService()
            result = import_service.import_project_data(
                project_id=project_id,
                file_path=temp_file_path,
                data_type="components",
                field_mapping=request.field_mapping,
                sheet_name=request.sheet_name,
                validate_only=request.validate_only,
                transaction_id=request.transaction_id,
            )

            response = ImportResponse(
                success=result["success"],
                message="Components import completed successfully" if result["success"] else "Components import failed",
                transaction_id=result.get("transaction_id", ""),
                import_type="components",
                total_records=result["total_records"],
                imported_records=result.get("imported_records", 0),
                validation_errors=result["validation"]["error_count"],
                validation_warnings=result["validation"]["warning_count"],
            )

            return response

        finally:
            Path(temp_file_path).unlink(missing_ok=True)

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in components import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/projects/{project_id}/import/circuits", response_model=ImportResponse)
async def import_project_circuits(
    project_id: str,
    file: UploadFile = File(...),
    request_data: str = Form(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Import heat tracing circuits data for a specific project."""
    logger.info(f"Circuits import requested for project {project_id} by user: {current_user.get('id')}")

    try:
        import json
        request = ImportRequest.parse_raw(request_data)

        temp_file_path = await _save_uploaded_file(file)

        try:
            import_service = ImportService()
            result = import_service.import_project_data(
                project_id=project_id,
                file_path=temp_file_path,
                data_type="heat_tracing_circuits",
                field_mapping=request.field_mapping,
                sheet_name=request.sheet_name,
                validate_only=request.validate_only,
                transaction_id=request.transaction_id,
            )

            response = ImportResponse(
                success=result["success"],
                message="Circuits import completed successfully" if result["success"] else "Circuits import failed",
                transaction_id=result.get("transaction_id", ""),
                import_type="heat_tracing_circuits",
                total_records=result["total_records"],
                imported_records=result.get("imported_records", 0),
                validation_errors=result["validation"]["error_count"],
                validation_warnings=result["validation"]["warning_count"],
            )

            return response

        finally:
            Path(temp_file_path).unlink(missing_ok=True)

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in circuits import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Multi-sheet import endpoint
@router.post("/projects/{project_id}/import/multi-sheet")
async def import_multi_sheet_project_data(
    project_id: str,
    file: UploadFile = File(...),
    request_data: str = Form(...),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Import data from multiple sheets in a single Excel file."""
    logger.info(f"Multi-sheet import requested for project {project_id} by user: {current_user.get('id')}")

    try:
        import json
        request = MultiSheetImportRequest.parse_raw(request_data)

        temp_file_path = await _save_uploaded_file(file)

        try:
            import_service = ImportService()
            result = import_service.import_multi_sheet_project_data(
                project_id=project_id,
                file_path=temp_file_path,
                sheet_mappings=request.sheet_mappings,
                validate_only=request.validate_only,
                transaction_id=request.transaction_id,
            )

            return {
                "success": result["total_success"],
                "message": "Multi-sheet import completed" if result["total_success"] else "Multi-sheet import failed",
                "transaction_id": result.get("transaction_id", ""),
                "import_type": "multi_sheet_project",
                "summary": result["summary"],
                "sheet_results": result["sheet_results"],
            }

        finally:
            Path(temp_file_path).unlink(missing_ok=True)

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in multi-sheet import: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Template generation endpoints
@router.post("/templates/generate")
async def generate_import_template(
    request: TemplateRequest,
    project_id: Optional[str] = Query(None, description="Project ID for project templates"),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Generate import template file."""
    logger.info(f"Template generation requested: {request.template_type}/{request.data_type}")

    try:
        import_service = ImportService()
        template_path = import_service.generate_import_template(
            template_type=request.template_type,
            data_type=request.data_type,
            format=request.format,
            project_id=project_id,
        )

        # Return file for download
        filename = f"{request.data_type}_import_template.{request.format}"
        return FileResponse(
            path=template_path,
            filename=filename,
            media_type="application/octet-stream",
        )

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in template generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Import history and status endpoints
@router.get("/history")
async def get_import_history(
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    import_type: Optional[str] = Query(None, description="Filter by import type"),
    limit: int = Query(100, description="Maximum number of records"),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Get import history with optional filtering."""
    logger.debug(f"Import history requested by user: {current_user.get('id')}")

    try:
        import_service = ImportService()
        history = import_service.get_import_history(
            project_id=project_id,
            import_type=import_type,
            limit=limit,
        )

        return {
            "history": history,
            "total_count": len(history),
            "filters": {
                "project_id": project_id,
                "import_type": import_type,
                "limit": limit,
            },
        }

    except Exception as e:
        logger.error(f"Error getting import history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/status/{transaction_id}")
async def get_import_status(
    transaction_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Get status of a specific import transaction."""
    logger.debug(f"Import status requested for transaction: {transaction_id}")

    try:
        import_service = ImportService()
        status = import_service.get_import_status(transaction_id)

        if status is None:
            raise HTTPException(status_code=404, detail="Transaction not found")

        return status

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting import status: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Utility functions
async def _save_uploaded_file(file: UploadFile) -> str:
    """Save uploaded file to temporary location."""
    try:
        # Create temporary file
        temp_dir = tempfile.mkdtemp()
        file_extension = Path(file.filename).suffix if file.filename else ""
        temp_file_path = Path(temp_dir) / f"upload_{file.filename}"

        # Save file content
        with open(temp_file_path, "wb") as temp_file:
            content = await file.read()
            temp_file.write(content)

        logger.debug(f"Uploaded file saved to: {temp_file_path}")
        return str(temp_file_path)

    except Exception as e:
        logger.error(f"Failed to save uploaded file: {e}")
        raise CalculationError(f"Failed to save uploaded file: {str(e)}")
