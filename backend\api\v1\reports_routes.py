# backend/api/v1/reports_routes.py
"""
Reports API Routes.

This module provides REST API endpoints for report generation and export operations,
supporting various output formats and comprehensive reporting functionality.
"""

import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field

from core.reports import ReportService
from core.errors.exceptions import InvalidInputError, CalculationError
from api.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/reports", tags=["reports"])


# Pydantic models for request/response
class ReportGenerationRequest(BaseModel):
    """Request model for report generation."""
    project_data: Dict[str, Any] = Field(..., description="Project information")
    circuits_data: List[Dict[str, Any]] = Field(..., description="Circuits data")
    calculations_data: List[Dict[str, Any]] = Field(default=[], description="Calculation results")
    design_parameters: Dict[str, Any] = Field(default={}, description="Design parameters")
    output_format: str = Field("pdf", description="Output format (pdf, html, excel)")
    template_name: Optional[str] = Field(None, description="Custom template name")
    use_cache: bool = Field(True, description="Whether to use cached results")


class CableScheduleRequest(BaseModel):
    """Request model for cable schedule generation."""
    project_data: Dict[str, Any] = Field(..., description="Project information")
    circuits_data: List[Dict[str, Any]] = Field(..., description="Circuits data")
    cable_data: List[Dict[str, Any]] = Field(..., description="Cable catalog data")
    output_format: str = Field("excel", description="Output format")
    use_cache: bool = Field(True, description="Whether to use cached results")


class DashboardRequest(BaseModel):
    """Request model for dashboard generation."""
    project_data: Dict[str, Any] = Field(..., description="Project information")
    circuits_data: List[Dict[str, Any]] = Field(..., description="Circuits data")
    calculations_data: List[Dict[str, Any]] = Field(..., description="Calculation results")
    include_charts: bool = Field(True, description="Whether to include charts")
    use_cache: bool = Field(True, description="Whether to use cached results")


class DataExportRequest(BaseModel):
    """Request model for data export."""
    data: Dict[str, Any] = Field(..., description="Data to export")
    export_type: str = Field(..., description="Type of export")
    format_type: str = Field(..., description="Export format (csv, json, excel)")
    filters: Optional[Dict[str, Any]] = Field(None, description="Data filters")


class ReportPackageRequest(BaseModel):
    """Request model for report package generation."""
    project_data: Dict[str, Any] = Field(..., description="Project information")
    circuits_data: List[Dict[str, Any]] = Field(..., description="Circuits data")
    calculations_data: List[Dict[str, Any]] = Field(..., description="Calculation results")
    design_parameters: Dict[str, Any] = Field(default={}, description="Design parameters")
    formats: Optional[List[str]] = Field(None, description="List of formats to generate")
    include_dashboard: bool = Field(True, description="Whether to include dashboard")


class ReportResponse(BaseModel):
    """Response model for report generation."""
    success: bool
    message: str
    report_type: str
    project_id: str
    output_format: str
    file_path: str
    file_size: int
    generation_time_seconds: float
    generated_at: str


# Report generation endpoints
@router.post("/projects/{project_id}/calculation-report", response_model=ReportResponse)
async def generate_calculation_report(
    project_id: str,
    request: ReportGenerationRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Generate heat tracing calculation report.
    
    Generates comprehensive calculation reports with design parameters,
    circuit analysis, and engineering calculations.
    """
    logger.info(f"Calculation report requested for project {project_id} by user: {current_user.get('id')}")

    try:
        # Initialize report service
        report_service = ReportService()

        # Generate report
        result = report_service.generate_calculation_report(
            project_id=project_id,
            project_data=request.project_data,
            circuits_data=request.circuits_data,
            calculations_data=request.calculations_data,
            design_parameters=request.design_parameters,
            output_format=request.output_format,
            template_name=request.template_name,
            use_cache=request.use_cache,
        )

        # Prepare response
        response = ReportResponse(
            success=result["success"],
            message="Calculation report generated successfully",
            report_type=result["report_type"],
            project_id=result["project_id"],
            output_format=result["output_format"],
            file_path=result["file_path"],
            file_size=result["file_size"],
            generation_time_seconds=result["generation_time_seconds"],
            generated_at=result["generated_at"],
        )

        logger.info(f"Calculation report generated successfully for project {project_id}")
        return response

    except InvalidInputError as e:
        logger.error(f"Invalid input for calculation report: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        logger.error(f"Calculation report generation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in calculation report generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/projects/{project_id}/cable-schedule")
async def generate_cable_schedule(
    project_id: str,
    request: CableScheduleRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Generate cable schedule report."""
    logger.info(f"Cable schedule requested for project {project_id} by user: {current_user.get('id')}")

    try:
        report_service = ReportService()

        result = report_service.generate_cable_schedule(
            project_id=project_id,
            project_data=request.project_data,
            circuits_data=request.circuits_data,
            cable_data=request.cable_data,
            output_format=request.output_format,
            use_cache=request.use_cache,
        )

        return {
            "success": result["success"],
            "message": "Cable schedule generated successfully",
            "report_type": result["report_type"],
            "project_id": result["project_id"],
            "output_format": result["output_format"],
            "file_path": result["file_path"],
            "file_size": result["file_size"],
            "generation_time_seconds": result["generation_time_seconds"],
            "generated_at": result["generated_at"],
        }

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in cable schedule generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/projects/{project_id}/dashboard")
async def generate_dashboard(
    project_id: str,
    request: DashboardRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Generate interactive dashboard report."""
    logger.info(f"Dashboard requested for project {project_id} by user: {current_user.get('id')}")

    try:
        report_service = ReportService()

        result = report_service.generate_dashboard(
            project_id=project_id,
            project_data=request.project_data,
            circuits_data=request.circuits_data,
            calculations_data=request.calculations_data,
            include_charts=request.include_charts,
            use_cache=request.use_cache,
        )

        return {
            "success": result["success"],
            "message": "Dashboard generated successfully",
            "report_type": result["report_type"],
            "project_id": result["project_id"],
            "output_format": result["output_format"],
            "file_path": result["file_path"],
            "file_size": result["file_size"],
            "generation_time_seconds": result["generation_time_seconds"],
            "generated_at": result["generated_at"],
            "include_charts": result["include_charts"],
        }

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in dashboard generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/projects/{project_id}/report-package")
async def generate_report_package(
    project_id: str,
    request: ReportPackageRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Generate complete report package in multiple formats."""
    logger.info(f"Report package requested for project {project_id} by user: {current_user.get('id')}")

    try:
        report_service = ReportService()

        result = report_service.generate_report_package(
            project_id=project_id,
            project_data=request.project_data,
            circuits_data=request.circuits_data,
            calculations_data=request.calculations_data,
            design_parameters=request.design_parameters,
            formats=request.formats,
            include_dashboard=request.include_dashboard,
        )

        return {
            "success": result["success"],
            "message": "Report package generated successfully",
            "report_type": result["report_type"],
            "project_id": result["project_id"],
            "generated_files": result["generated_files"],
            "total_files": result["total_files"],
            "successful_files": result["successful_files"],
            "total_size_bytes": result["total_size_bytes"],
            "generation_time_seconds": result["generation_time_seconds"],
            "generated_at": result["generated_at"],
        }

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in report package generation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Data export endpoints
@router.post("/projects/{project_id}/export")
async def export_data(
    project_id: str,
    request: DataExportRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Export project data in specified format."""
    logger.info(f"Data export requested for project {project_id} by user: {current_user.get('id')}")

    try:
        report_service = ReportService()

        result = report_service.export_data(
            project_id=project_id,
            data=request.data,
            export_type=request.export_type,
            format_type=request.format_type,
            filters=request.filters,
        )

        return {
            "success": result["success"],
            "message": "Data export completed successfully",
            "export_type": result["export_type"],
            "project_id": result["project_id"],
            "format": result["format"],
            "file_path": result["file_path"],
            "file_size": result["file_size"],
            "export_time_seconds": result["export_time_seconds"],
            "exported_at": result["exported_at"],
            "filters_applied": result["filters_applied"],
        }

    except InvalidInputError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in data export: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# File download endpoints
@router.get("/download/{file_path:path}")
async def download_report_file(
    file_path: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Download generated report file."""
    logger.info(f"File download requested: {file_path} by user: {current_user.get('id')}")

    try:
        # Validate file path and existence
        file_obj = Path(file_path)
        
        if not file_obj.exists():
            raise HTTPException(status_code=404, detail="File not found")

        if not file_obj.is_file():
            raise HTTPException(status_code=400, detail="Path is not a file")

        # Determine media type based on file extension
        media_type_map = {
            ".pdf": "application/pdf",
            ".html": "text/html",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".csv": "text/csv",
            ".json": "application/json",
            ".zip": "application/zip",
        }
        
        media_type = media_type_map.get(file_obj.suffix.lower(), "application/octet-stream")

        return FileResponse(
            path=file_path,
            filename=file_obj.name,
            media_type=media_type,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File download failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="File download failed")


# Utility endpoints
@router.get("/history")
async def get_generation_history(
    project_id: Optional[str] = Query(None, description="Filter by project ID"),
    report_type: Optional[str] = Query(None, description="Filter by report type"),
    limit: int = Query(50, description="Maximum number of records"),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Get report generation history."""
    logger.debug(f"Generation history requested by user: {current_user.get('id')}")

    try:
        report_service = ReportService()
        history = report_service.get_generation_history(
            project_id=project_id,
            report_type=report_type,
            limit=limit,
        )

        return {
            "history": history,
            "total_count": len(history),
            "filters": {
                "project_id": project_id,
                "report_type": report_type,
                "limit": limit,
            },
        }

    except Exception as e:
        logger.error(f"Error getting generation history: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/templates")
async def get_available_templates(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Get list of available report templates."""
    logger.debug(f"Available templates requested by user: {current_user.get('id')}")

    try:
        report_service = ReportService()
        templates = report_service.get_available_templates()

        return {
            "templates": templates,
            "total_count": len(templates),
        }

    except Exception as e:
        logger.error(f"Error getting available templates: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/cache")
async def clear_report_cache(
    project_id: Optional[str] = Query(None, description="Clear cache for specific project"),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """Clear report generation cache."""
    logger.info(f"Cache clear requested by user: {current_user.get('id')} for project: {project_id}")

    try:
        # Check if user has admin privileges for global cache clear
        if project_id is None and not current_user.get("is_admin", False):
            raise HTTPException(status_code=403, detail="Admin privileges required for global cache clear")

        report_service = ReportService()
        cleared_count = report_service.clear_cache(project_id)

        return {
            "success": True,
            "message": f"Cache cleared successfully",
            "cleared_entries": cleared_count,
            "project_id": project_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cache clear failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Cache clear failed")
