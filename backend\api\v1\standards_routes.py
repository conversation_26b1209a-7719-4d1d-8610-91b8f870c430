# backend/api/v1/standards_routes.py
"""
Standards API Routes.

This module provides REST API endpoints for engineering standards validation,
calculations, and compliance checking operations.
"""

import logging
from typing import Dict, Any, Optional, List

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from core.standards import StandardsService
from core.errors.exceptions import InvalidInputError, CalculationError
from api.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/standards", tags=["standards"])


# Pydantic models for request/response
class DesignValidationRequest(BaseModel):
    """Request model for design validation."""
    design_data: Dict[str, Any] = Field(..., description="Design data to validate")
    standard_ids: Optional[List[str]] = Field(None, description="Specific standards to validate against")
    validation_options: Optional[Dict[str, Any]] = Field(None, description="Validation configuration options")


class ParameterCalculationRequest(BaseModel):
    """Request model for parameter calculations."""
    input_data: Dict[str, Any] = Field(..., description="Input data for calculations")
    standard_ids: Optional[List[str]] = Field(None, description="Specific standards to use for calculations")
    calculation_options: Optional[Dict[str, Any]] = Field(None, description="Calculation configuration options")


class StandardsRecommendationRequest(BaseModel):
    """Request model for standards recommendations."""
    project_data: Dict[str, Any] = Field(..., description="Project information")
    application_type: str = Field(..., description="Type of application")


class ValidationResponse(BaseModel):
    """Response model for validation results."""
    success: bool
    validation_summary: Dict[str, Any]
    individual_results: Dict[str, Dict[str, Any]]
    compliance_report: Dict[str, Any]
    cross_standard_conflicts: List[Dict[str, Any]]
    recommendations: List[Dict[str, Any]]


class CalculationResponse(BaseModel):
    """Response model for calculation results."""
    success: bool
    calculation_summary: Dict[str, Any]
    individual_results: Dict[str, Dict[str, Any]]
    consolidated_parameters: Dict[str, Any]
    parameter_comparisons: Dict[str, Any]


# Standards information endpoints
@router.get("/available")
async def get_available_standards(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Get all available engineering standards.
    
    Returns comprehensive information about all supported engineering
    standards organized by type (IEEE, IEC, API, NFPA, ISO).
    """
    logger.info(f"Available standards requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()
        available_standards = standards_service.get_available_standards()

        return {
            "success": True,
            "message": "Available standards retrieved successfully",
            "standards_by_type": available_standards,
            "total_standards": sum(len(standards) for standards in available_standards.values()),
            "supported_types": list(available_standards.keys()),
        }

    except Exception as e:
        logger.error(f"Failed to get available standards: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve available standards")


@router.get("/recommendations")
async def get_standards_recommendations(
    application_type: str = Query(..., description="Type of application"),
    location: Optional[str] = Query(None, description="Project location"),
    industry: Optional[str] = Query(None, description="Industry type"),
    hazardous_area: bool = Query(False, description="Hazardous area application"),
    offshore: bool = Query(False, description="Offshore application"),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Get standards recommendations based on project parameters.
    
    Provides intelligent recommendations for applicable engineering
    standards based on application type and project characteristics.
    """
    logger.info(f"Standards recommendations requested for {application_type} by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()
        
        project_data = {
            "location": location or "",
            "industry": industry or "",
            "hazardous_area": hazardous_area,
            "offshore": offshore,
        }
        
        recommendations = standards_service.get_standards_recommendations(
            project_data, application_type
        )

        return {
            "success": True,
            "message": "Standards recommendations generated successfully",
            "application_type": application_type,
            "project_parameters": project_data,
            "recommendations": recommendations,
        }

    except Exception as e:
        logger.error(f"Standards recommendations failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to generate standards recommendations")


# Validation endpoints
@router.post("/validate", response_model=ValidationResponse)
async def validate_design_against_standards(
    request: DesignValidationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Validate design against engineering standards.
    
    Performs comprehensive validation of design data against specified
    engineering standards and generates detailed compliance reports.
    """
    logger.info(f"Design validation requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()

        validation_results = standards_service.validate_design_against_standards(
            design_data=request.design_data,
            standard_ids=request.standard_ids,
            validation_options=request.validation_options,
        )

        response = ValidationResponse(
            success=True,
            validation_summary=validation_results["validation_summary"],
            individual_results=validation_results["individual_results"],
            compliance_report=validation_results["compliance_report"],
            cross_standard_conflicts=validation_results["cross_standard_conflicts"],
            recommendations=validation_results["recommendations"],
        )

        logger.info(f"Design validation completed successfully")
        return response

    except InvalidInputError as e:
        logger.error(f"Invalid input for design validation: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        logger.error(f"Design validation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in design validation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/validate/{standard_id}")
async def validate_against_specific_standard(
    standard_id: str,
    design_data: Dict[str, Any],
    validation_options: Optional[Dict[str, Any]] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Validate design against a specific engineering standard.
    
    Performs validation against a single specified standard and
    returns detailed compliance information.
    """
    logger.info(f"Validation against {standard_id} requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()

        validation_results = standards_service.validate_design_against_standards(
            design_data=design_data,
            standard_ids=[standard_id],
            validation_options=validation_options,
        )

        # Extract results for the specific standard
        individual_result = validation_results["individual_results"].get(standard_id, {})

        return {
            "success": True,
            "message": f"Validation against {standard_id} completed",
            "standard_id": standard_id,
            "validation_result": individual_result,
            "compliance_summary": {
                "is_compliant": individual_result.get("is_compliant", False),
                "compliance_level": individual_result.get("compliance_level", "unknown"),
                "violations_count": individual_result.get("violations_count", 0),
                "warnings_count": individual_result.get("warnings_count", 0),
            },
        }

    except InvalidInputError as e:
        logger.error(f"Invalid input for standard validation: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        logger.error(f"Standard validation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in standard validation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Calculation endpoints
@router.post("/calculate", response_model=CalculationResponse)
async def calculate_standards_parameters(
    request: ParameterCalculationRequest,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Calculate parameters according to engineering standards.
    
    Performs calculations using specified engineering standards and
    returns consolidated parameter results with comparisons.
    """
    logger.info(f"Standards parameter calculation requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()

        calculation_results = standards_service.calculate_standards_parameters(
            input_data=request.input_data,
            standard_ids=request.standard_ids,
            calculation_options=request.calculation_options,
        )

        response = CalculationResponse(
            success=True,
            calculation_summary=calculation_results["calculation_summary"],
            individual_results=calculation_results["individual_results"],
            consolidated_parameters=calculation_results["consolidated_parameters"],
            parameter_comparisons=calculation_results["parameter_comparisons"],
        )

        logger.info(f"Standards parameter calculation completed successfully")
        return response

    except InvalidInputError as e:
        logger.error(f"Invalid input for parameter calculation: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        logger.error(f"Parameter calculation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in parameter calculation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/calculate/{standard_id}")
async def calculate_with_specific_standard(
    standard_id: str,
    input_data: Dict[str, Any],
    calculation_options: Optional[Dict[str, Any]] = None,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Calculate parameters using a specific engineering standard.
    
    Performs calculations using a single specified standard and
    returns detailed parameter results.
    """
    logger.info(f"Calculation with {standard_id} requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()

        calculation_results = standards_service.calculate_standards_parameters(
            input_data=input_data,
            standard_ids=[standard_id],
            calculation_options=calculation_options,
        )

        # Extract results for the specific standard
        individual_result = calculation_results["individual_results"].get(standard_id, {})

        return {
            "success": True,
            "message": f"Calculation with {standard_id} completed",
            "standard_id": standard_id,
            "calculation_result": individual_result,
            "calculated_values": individual_result.get("calculated_values", {}),
            "calculation_steps": individual_result.get("calculation_steps", []),
            "warnings": individual_result.get("warnings", []),
        }

    except InvalidInputError as e:
        logger.error(f"Invalid input for standard calculation: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except CalculationError as e:
        logger.error(f"Standard calculation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error in standard calculation: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# Compliance and reporting endpoints
@router.get("/compliance/summary")
async def get_compliance_summary(
    project_id: Optional[str] = Query(None, description="Specific project ID"),
    time_period: Optional[str] = Query(None, description="Time period for summary"),
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Get compliance summary for projects.
    
    Provides comprehensive compliance statistics and usage patterns
    for engineering standards validation and calculations.
    """
    logger.info(f"Compliance summary requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()
        
        compliance_summary = standards_service.get_compliance_summary(
            project_id=project_id,
            time_period=time_period,
        )

        return {
            "success": True,
            "message": "Compliance summary retrieved successfully",
            "summary": compliance_summary,
        }

    except Exception as e:
        logger.error(f"Compliance summary failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve compliance summary")


@router.get("/standards/{standard_id}/info")
async def get_standard_information(
    standard_id: str,
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Get detailed information about a specific standard.
    
    Returns comprehensive information about a specific engineering
    standard including rules, parameters, and application guidelines.
    """
    logger.info(f"Standard information for {standard_id} requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()
        
        # Get standard from registry
        if standard_id not in standards_service.standards_registry:
            raise HTTPException(status_code=404, detail=f"Standard {standard_id} not found")

        standard = standards_service.standards_registry[standard_id]
        standard_info = standard.get_standard_info()

        return {
            "success": True,
            "message": f"Standard information retrieved for {standard_id}",
            "standard_info": standard_info,
            "rules": standard.get_all_rules(),
            "parameters": standard.get_all_parameters(),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get standard information: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retrieve standard information")


@router.get("/health")
async def standards_health_check(
    current_user: Dict[str, Any] = Depends(get_current_user),
):
    """
    Health check for standards service.
    
    Verifies that all standards are properly loaded and accessible.
    """
    logger.debug(f"Standards health check requested by user: {current_user.get('id')}")

    try:
        standards_service = StandardsService()
        
        health_status = {
            "service_status": "healthy",
            "registered_standards_count": len(standards_service.standards_registry),
            "available_standard_types": list(standards_service.standards_interfaces.keys()),
            "validator_status": "operational",
            "calculator_status": "operational",
            "timestamp": "2024-12-19T10:00:00Z",
        }

        return {
            "success": True,
            "message": "Standards service is healthy",
            "health_status": health_status,
        }

    except Exception as e:
        logger.error(f"Standards health check failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Standards service health check failed")
