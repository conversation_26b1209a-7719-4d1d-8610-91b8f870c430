# backend/core/calculations/calculation_service.py
"""
Calculation Service - Orchestrates engineering calculations.

This service acts as the main facade for all engineering calculations,
coordinating between different calculation modules and ensuring proper
error handling and validation.
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, Optional, List

from core.errors.exceptions import (
    CalculationError,
    InvalidInputError,
    NotFoundError,
)

# Note: Unit conversion utilities are available in core.utils.unit_conversion_utils
# but importing them here creates a circular import, so we'll import them locally
# when needed in the methods that use them

logger = logging.getLogger(__name__)


@dataclass
class CalculationInput:
    """Base class for calculation inputs."""

    pass


@dataclass
class CalculationResult:
    """Base class for calculation results."""

    calculation_type: str
    inputs: Dict[str, Any]
    outputs: Dict[str, Any]
    warnings: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class HeatLossInput(CalculationInput):
    """Input parameters for heat loss calculations."""

    pipe_diameter: float  # meters
    pipe_length: float  # meters
    fluid_temperature: float  # Celsius
    ambient_temperature: float  # Celsius
    insulation_thickness: float  # meters
    insulation_type: str
    wind_speed: Optional[float] = 0.0  # m/s
    pipe_material: str = "carbon_steel"


@dataclass
class HeatLossResult(CalculationResult):
    """Results from heat loss calculations."""

    heat_loss_rate: float = 0.0  # Watts per meter
    total_heat_loss: float = 0.0  # Watts
    surface_temperature: float = 0.0  # Celsius
    required_power: float = 0.0  # Watts


@dataclass
class CableSizingInput(CalculationInput):
    """Input parameters for cable sizing calculations."""

    required_power: float  # Watts
    cable_length: float  # meters
    supply_voltage: float  # Volts
    ambient_temperature: float  # Celsius
    installation_method: str
    cable_type: Optional[str] = None


@dataclass
class CableSizingResult(CalculationResult):
    """Results from cable sizing calculations."""

    recommended_cable_type: str = ""
    cable_power_per_meter: float = 0.0  # Watts per meter
    total_cable_length: float = 0.0  # meters
    current_draw: float = 0.0  # Amperes
    voltage_drop: float = 0.0  # Volts
    power_density: float = 0.0  # Watts per square meter


class CalculationService:
    """
    Main service for orchestrating engineering calculations.

    This service provides a unified interface for all calculation operations,
    handling error management and coordination between different calculation modules.
    """

    def __init__(self):
        """Initialize the calculation service."""
        logger.debug("CalculationService initialized")

    def convert_temperature(
        self, value: float, from_unit: str, to_unit: str = "celsius"
    ) -> float:
        """
        Convert temperature between different units.

        Args:
            value: Temperature value to convert
            from_unit: Source temperature unit
            to_unit: Target temperature unit (default: celsius)

        Returns:
            float: Converted temperature value

        Raises:
            InvalidInputError: If conversion is invalid
        """
        try:
            # Local import to avoid circular dependency
            from core.utils.unit_conversion_utils import (
                convert_units,
                UnitConversionError,
            )

            return convert_units(value, from_unit, to_unit, "temperature")
        except UnitConversionError as e:
            raise InvalidInputError(f"Temperature conversion failed: {str(e)}")
        except ImportError as e:
            raise InvalidInputError(f"Unit conversion not available: {str(e)}")

    def convert_power(
        self, value: float, from_unit: str, to_unit: str = "watt"
    ) -> float:
        """
        Convert power between different units.

        Args:
            value: Power value to convert
            from_unit: Source power unit
            to_unit: Target power unit (default: watt)

        Returns:
            float: Converted power value

        Raises:
            InvalidInputError: If conversion is invalid
        """
        try:
            # Local import to avoid circular dependency
            from core.utils.unit_conversion_utils import (
                convert_units,
                UnitConversionError,
            )

            return convert_units(value, from_unit, to_unit, "power")
        except UnitConversionError as e:
            raise InvalidInputError(f"Power conversion failed: {str(e)}")
        except ImportError as e:
            raise InvalidInputError(f"Unit conversion not available: {str(e)}")

    def convert_length(
        self, value: float, from_unit: str, to_unit: str = "meter"
    ) -> float:
        """
        Convert length between different units.

        Args:
            value: Length value to convert
            from_unit: Source length unit
            to_unit: Target length unit (default: meter)

        Returns:
            float: Converted length value

        Raises:
            InvalidInputError: If conversion is invalid
        """
        try:
            # Local import to avoid circular dependency
            from core.utils.unit_conversion_utils import (
                convert_units,
                UnitConversionError,
            )

            return convert_units(value, from_unit, to_unit, "length")
        except UnitConversionError as e:
            raise InvalidInputError(f"Length conversion failed: {str(e)}")
        except ImportError as e:
            raise InvalidInputError(f"Unit conversion not available: {str(e)}")

    def validate_unit_conversion(
        self, from_unit: str, to_unit: str, unit_type: str
    ) -> bool:
        """
        Validate if a unit conversion is possible.

        Args:
            from_unit: Source unit
            to_unit: Target unit
            unit_type: Type of unit (temperature, power, length, etc.)

        Returns:
            bool: True if conversion is valid
        """
        try:
            # Local import to avoid circular dependency
            from core.utils.unit_conversion_utils import validate_conversion

            return validate_conversion(from_unit, to_unit, unit_type)
        except ImportError:
            return False

    def calculate_heat_loss(self, inputs: HeatLossInput) -> HeatLossResult:
        """
        Calculate heat loss for pipes and vessels.

        Args:
            inputs: Heat loss calculation parameters

        Returns:
            HeatLossResult: Calculated heat loss values

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
        """
        logger.info(
            f"Starting heat loss calculation for pipe diameter: {inputs.pipe_diameter}m"
        )

        try:
            # Validate inputs
            self._validate_heat_loss_inputs(inputs)

            # Import calculation modules (lazy loading)
            from .heat_loss.insulation_properties import get_insulation_properties
            from .heat_loss.pipe_heat_loss import calculate_pipe_heat_loss

            # Get insulation properties
            try:
                insulation_props = get_insulation_properties(inputs.insulation_type)
                assert insulation_props is not None, (
                    "Insulation properties should not be None"
                )
            except NotFoundError as e:
                raise CalculationError(
                    f"Insulation type '{inputs.insulation_type}' not found: {e.detail}"
                )

            # Perform heat loss calculation
            heat_loss_rate = calculate_pipe_heat_loss(
                diameter=inputs.pipe_diameter,
                fluid_temp=inputs.fluid_temperature,
                ambient_temp=inputs.ambient_temperature,
                insulation_thickness=inputs.insulation_thickness,
                insulation_conductivity=insulation_props["thermal_conductivity"],
                wind_speed=inputs.wind_speed or 0.0,
            )

            # Calculate total heat loss
            total_heat_loss = heat_loss_rate * inputs.pipe_length

            # Calculate surface temperature (simplified)
            surface_temp = self._calculate_surface_temperature(
                inputs.fluid_temperature,
                inputs.ambient_temperature,
                inputs.insulation_thickness,
                insulation_props["thermal_conductivity"],
            )

            # Calculate required heating power (with safety factor)
            required_power = total_heat_loss * 1.2  # 20% safety factor

            result = HeatLossResult(
                calculation_type="heat_loss",
                inputs=inputs.__dict__,
                outputs={
                    "heat_loss_rate": heat_loss_rate,
                    "total_heat_loss": total_heat_loss,
                    "surface_temperature": surface_temp,
                    "required_power": required_power,
                },
                heat_loss_rate=heat_loss_rate,
                total_heat_loss=total_heat_loss,
                surface_temperature=surface_temp,
                required_power=required_power,
                metadata={
                    "insulation_properties": insulation_props,
                    "safety_factor": 1.2,
                },
            )

            logger.info(f"Heat loss calculation completed: {heat_loss_rate:.2f} W/m")
            return result

        except (InvalidInputError, NotFoundError):
            raise
        except Exception as e:
            logger.error(f"Heat loss calculation failed: {e}", exc_info=True)
            raise CalculationError(details=f"Heat loss calculation failed: {str(e)}")

    def calculate_cable_sizing(self, inputs: CableSizingInput) -> CableSizingResult:
        """
        Calculate cable sizing for heating circuits.

        Args:
            inputs: Cable sizing calculation parameters

        Returns:
            CableSizingResult: Calculated cable sizing values

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
        """
        logger.info(f"Starting cable sizing calculation for {inputs.required_power}W")

        try:
            # Validate inputs
            self._validate_cable_sizing_inputs(inputs)

            # Import calculation modules
            from .electrical_sizing.cable_sizing import (
                calculate_cable_parameters,
                select_optimal_cable,
            )
            from .electrical_sizing.voltage_drop import calculate_voltage_drop

            # Select optimal cable type
            cable_selection = select_optimal_cable(
                required_power=inputs.required_power,
                cable_length=inputs.cable_length,
                ambient_temp=inputs.ambient_temperature,
                installation_method=inputs.installation_method,
                preferred_type=inputs.cable_type,
            )

            if not cable_selection:
                raise CalculationError(
                    details="No suitable cable found for the given requirements"
                )

            # Calculate cable parameters
            cable_params = calculate_cable_parameters(
                cable_type=cable_selection["type"],
                required_power=inputs.required_power,
                cable_length=inputs.cable_length,
                supply_voltage=inputs.supply_voltage,
            )

            # Calculate voltage drop
            voltage_drop = calculate_voltage_drop(
                current=cable_params["current_draw"],
                length=inputs.cable_length,
                cable_resistance=cable_selection["resistance_per_meter"],
            )

            result = CableSizingResult(
                calculation_type="cable_sizing",
                inputs=inputs.__dict__,
                outputs={
                    "recommended_cable_type": cable_selection["type"],
                    "cable_power_per_meter": cable_params["power_per_meter"],
                    "total_cable_length": cable_params["total_length"],
                    "current_draw": cable_params["current_draw"],
                    "voltage_drop": voltage_drop,
                    "power_density": cable_params["power_density"],
                },
                recommended_cable_type=cable_selection["type"],
                cable_power_per_meter=cable_params["power_per_meter"],
                total_cable_length=cable_params["total_length"],
                current_draw=cable_params["current_draw"],
                voltage_drop=voltage_drop,
                power_density=cable_params["power_density"],
                metadata={
                    "cable_selection": cable_selection,
                    "cable_parameters": cable_params,
                },
            )

            logger.info(
                f"Cable sizing calculation completed: {cable_selection['type']}"
            )
            return result

        except (InvalidInputError, NotFoundError):
            raise
        except Exception as e:
            logger.error(f"Cable sizing calculation failed: {e}", exc_info=True)
            raise CalculationError(details=f"Cable sizing calculation failed: {str(e)}")

    def _validate_heat_loss_inputs(self, inputs: HeatLossInput) -> None:
        """Validate heat loss calculation inputs."""
        if inputs.pipe_diameter <= 0:
            raise InvalidInputError("Pipe diameter must be positive")

        if inputs.pipe_length <= 0:
            raise InvalidInputError("Pipe length must be positive")

        if inputs.fluid_temperature <= inputs.ambient_temperature:
            raise InvalidInputError(
                "Fluid temperature must be higher than ambient temperature"
            )

        if inputs.insulation_thickness < 0:
            raise InvalidInputError("Insulation thickness cannot be negative")

        if inputs.wind_speed and inputs.wind_speed < 0:
            raise InvalidInputError("Wind speed cannot be negative")

    def _validate_cable_sizing_inputs(self, inputs: CableSizingInput) -> None:
        """Validate cable sizing calculation inputs."""
        if inputs.required_power <= 0:
            raise InvalidInputError("Required power must be positive")

        if inputs.cable_length <= 0:
            raise InvalidInputError("Cable length must be positive")

        if inputs.supply_voltage <= 0:
            raise InvalidInputError("Supply voltage must be positive")

        if not inputs.installation_method:
            raise InvalidInputError("Installation method is required")

    def calculate_required_power(
        self,
        heat_loss_rate: float,
        cable_length: float,
        safety_factor: float = 1.2,
        maintenance_factor: float = 1.1,
    ) -> Dict[str, Any]:
        """
        Calculate required power for heat tracing system.

        Args:
            heat_loss_rate: Heat loss rate in W/m
            cable_length: Total cable length in meters
            safety_factor: Safety factor for design margin
            maintenance_factor: Factor for maintenance temperature

        Returns:
            Dict with power calculation results

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
        """
        logger.info(f"Calculating required power for {cable_length}m cable")

        try:
            # Validate inputs
            if heat_loss_rate <= 0:
                raise InvalidInputError("Heat loss rate must be positive")
            if cable_length <= 0:
                raise InvalidInputError("Cable length must be positive")
            if safety_factor < 1.0:
                raise InvalidInputError("Safety factor must be at least 1.0")
            if maintenance_factor < 1.0:
                raise InvalidInputError("Maintenance factor must be at least 1.0")

            # Calculate base power requirement
            base_power = heat_loss_rate * cable_length

            # Apply factors
            design_power = base_power * safety_factor
            maintenance_power = design_power * maintenance_factor

            # Calculate power density
            power_density = maintenance_power / cable_length

            result = {
                "base_power": base_power,
                "design_power": design_power,
                "maintenance_power": maintenance_power,
                "power_density": power_density,
                "safety_factor": safety_factor,
                "maintenance_factor": maintenance_factor,
                "total_cable_length": cable_length,
            }

            logger.info(
                f"Required power calculation completed: {maintenance_power:.1f}W"
            )
            return result

        except InvalidInputError:
            raise
        except Exception as e:
            logger.error(f"Required power calculation failed: {e}")
            raise CalculationError(f"Required power calculation failed: {str(e)}")

    def select_cable_type(
        self,
        required_power: float,
        cable_length: float,
        ambient_temperature: float = 20.0,
        installation_method: str = "surface_mount",
        voltage: float = 230.0,
        hazardous_area: bool = False,
    ) -> Dict[str, Any]:
        """
        Select optimal cable type based on requirements.

        Args:
            required_power: Required power in Watts
            cable_length: Cable length in meters
            ambient_temperature: Ambient temperature in °C
            installation_method: Installation method
            voltage: Supply voltage in Volts
            hazardous_area: Whether installation is in hazardous area

        Returns:
            Dict with cable selection results

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If selection fails
        """
        logger.info(f"Selecting cable type for {required_power}W, {cable_length}m")

        try:
            # Import cable selection module
            from .electrical_sizing.cable_sizing import select_optimal_cable

            # Validate inputs
            if required_power <= 0:
                raise InvalidInputError("Required power must be positive")
            if cable_length <= 0:
                raise InvalidInputError("Cable length must be positive")

            # Calculate power per meter
            power_per_meter = required_power / cable_length

            # Select optimal cable
            cable_selection = select_optimal_cable(
                required_power=required_power,
                cable_length=cable_length,
                ambient_temp=ambient_temperature,
                installation_method=installation_method,
                preferred_type=None,
            )

            if not cable_selection:
                raise CalculationError("No suitable cable found for requirements")

            # Add additional selection criteria
            selection_result = {
                "selected_cable": cable_selection,
                "power_per_meter": power_per_meter,
                "selection_criteria": {
                    "required_power": required_power,
                    "cable_length": cable_length,
                    "ambient_temperature": ambient_temperature,
                    "installation_method": installation_method,
                    "voltage": voltage,
                    "hazardous_area": hazardous_area,
                },
                "recommendations": self._generate_cable_recommendations(
                    cable_selection, power_per_meter, ambient_temperature
                ),
            }

            logger.info(f"Cable selection completed: {cable_selection['type']}")
            return selection_result

        except InvalidInputError:
            raise
        except Exception as e:
            logger.error(f"Cable selection failed: {e}")
            raise CalculationError(f"Cable selection failed: {str(e)}")

    def calculate_cable_parameters(
        self,
        cable_type: str,
        required_power: float,
        cable_length: float,
        supply_voltage: float = 230.0,
    ) -> Dict[str, Any]:
        """
        Calculate detailed cable parameters.

        Args:
            cable_type: Type of cable selected
            required_power: Required power in Watts
            cable_length: Cable length in meters
            supply_voltage: Supply voltage in Volts

        Returns:
            Dict with detailed cable parameters

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
        """
        logger.info(f"Calculating parameters for {cable_type} cable")

        try:
            # Import calculation modules
            from .electrical_sizing.cable_sizing import calculate_cable_parameters
            from .electrical_sizing.voltage_drop import calculate_voltage_drop

            # Validate inputs
            if not cable_type:
                raise InvalidInputError("Cable type is required")
            if required_power <= 0:
                raise InvalidInputError("Required power must be positive")
            if cable_length <= 0:
                raise InvalidInputError("Cable length must be positive")
            if supply_voltage <= 0:
                raise InvalidInputError("Supply voltage must be positive")

            # Calculate cable parameters
            cable_params = calculate_cable_parameters(
                cable_type=cable_type,
                required_power=required_power,
                cable_length=cable_length,
                supply_voltage=supply_voltage,
            )

            # Calculate voltage drop
            voltage_drop = calculate_voltage_drop(
                current=cable_params["current_draw"],
                length=cable_length,
                cable_resistance=cable_params.get("resistance_per_meter", 0.1),
            )

            # Calculate efficiency
            power_loss = voltage_drop * cable_params["current_draw"]
            efficiency = (required_power - power_loss) / required_power * 100

            result = {
                **cable_params,
                "voltage_drop": voltage_drop,
                "power_loss": power_loss,
                "efficiency": efficiency,
                "voltage_drop_percentage": (voltage_drop / supply_voltage) * 100,
            }

            logger.info(f"Cable parameters calculated: {efficiency:.1f}% efficiency")
            return result

        except InvalidInputError:
            raise
        except Exception as e:
            logger.error(f"Cable parameters calculation failed: {e}")
            raise CalculationError(f"Cable parameters calculation failed: {str(e)}")

    def calculate_ssr_power_options(
        self,
        total_power: float,
        number_of_zones: int = 1,
        control_strategy: str = "proportional",
        minimum_power_percentage: float = 10.0,
    ) -> Dict[str, Any]:
        """
        Calculate SSR (Solid State Relay) power control options.

        Args:
            total_power: Total system power in Watts
            number_of_zones: Number of control zones
            control_strategy: Control strategy (proportional, on_off, staged)
            minimum_power_percentage: Minimum power output percentage

        Returns:
            Dict with SSR power control options

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
        """
        logger.info(f"Calculating SSR options for {total_power}W system")

        try:
            # Validate inputs
            if total_power <= 0:
                raise InvalidInputError("Total power must be positive")
            if number_of_zones <= 0:
                raise InvalidInputError("Number of zones must be positive")
            if not (0 < minimum_power_percentage <= 100):
                raise InvalidInputError(
                    "Minimum power percentage must be between 0 and 100"
                )

            # Calculate power per zone
            power_per_zone = total_power / number_of_zones

            # Define control options based on strategy
            control_options = self._calculate_control_options(
                control_strategy, power_per_zone, minimum_power_percentage
            )

            # Calculate SSR ratings
            ssr_ratings = self._calculate_ssr_ratings(power_per_zone, control_options)

            result = {
                "total_power": total_power,
                "number_of_zones": number_of_zones,
                "power_per_zone": power_per_zone,
                "control_strategy": control_strategy,
                "minimum_power_percentage": minimum_power_percentage,
                "control_options": control_options,
                "ssr_ratings": ssr_ratings,
                "recommended_configuration": self._recommend_ssr_configuration(
                    control_strategy, ssr_ratings
                ),
            }

            logger.info(f"SSR power options calculated for {number_of_zones} zones")
            return result

        except InvalidInputError:
            raise
        except Exception as e:
            logger.error(f"SSR power options calculation failed: {e}")
            raise CalculationError(f"SSR power options calculation failed: {str(e)}")

    def _calculate_surface_temperature(
        self,
        fluid_temp: float,
        ambient_temp: float,
        insulation_thickness: float,
        thermal_conductivity: float,
    ) -> float:
        """Calculate approximate surface temperature (simplified model)."""
        # Simplified calculation - in reality this would be more complex
        temp_drop = (fluid_temp - ambient_temp) * (
            insulation_thickness * thermal_conductivity
        )
        return fluid_temp - temp_drop

    def _generate_cable_recommendations(
        self,
        cable_selection: Dict[str, Any],
        power_per_meter: float,
        ambient_temperature: float,
    ) -> List[str]:
        """Generate cable selection recommendations."""
        recommendations = []

        # Power density recommendations
        if power_per_meter > 50:
            recommendations.append("High power density - consider parallel cables")
        elif power_per_meter < 5:
            recommendations.append("Low power density - consider higher power cable")

        # Temperature recommendations
        if ambient_temperature > 40:
            recommendations.append("High ambient temperature - verify cable derating")
        elif ambient_temperature < 0:
            recommendations.append("Low ambient temperature - verify cable flexibility")

        # Cable type specific recommendations
        cable_type = cable_selection.get("type", "")
        if "self_regulating" in cable_type:
            recommendations.append(
                "Self-regulating cable - good for varying temperatures"
            )
        elif "constant_wattage" in cable_type:
            recommendations.append(
                "Constant wattage cable - requires temperature control"
            )

        return recommendations

    def _calculate_control_options(
        self,
        control_strategy: str,
        power_per_zone: float,
        minimum_power_percentage: float,
    ) -> Dict[str, Any]:
        """Calculate control options based on strategy."""
        if control_strategy == "proportional":
            return {
                "type": "proportional",
                "power_range": {
                    "minimum": power_per_zone * minimum_power_percentage / 100,
                    "maximum": power_per_zone,
                },
                "control_resolution": "continuous",
                "response_time": "fast",
            }
        elif control_strategy == "on_off":
            return {
                "type": "on_off",
                "power_range": {
                    "minimum": 0,
                    "maximum": power_per_zone,
                },
                "control_resolution": "binary",
                "response_time": "immediate",
            }
        elif control_strategy == "staged":
            stages = 3  # Default to 3 stages
            return {
                "type": "staged",
                "power_range": {
                    "minimum": power_per_zone / stages,
                    "maximum": power_per_zone,
                },
                "control_resolution": f"{stages} stages",
                "response_time": "medium",
                "stages": stages,
            }
        else:
            # Default to proportional
            return self._calculate_control_options(
                "proportional", power_per_zone, minimum_power_percentage
            )

    def _calculate_ssr_ratings(
        self,
        power_per_zone: float,
        control_options: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Calculate SSR ratings based on power and control requirements."""
        # Calculate current at 230V (typical)
        voltage = 230.0
        current_per_zone = power_per_zone / voltage

        # Apply safety factor for SSR rating
        safety_factor = 1.5
        ssr_current_rating = current_per_zone * safety_factor

        # Standard SSR ratings
        standard_ratings = [10, 25, 40, 60, 90, 120, 150, 200]
        selected_rating = None

        for rating in standard_ratings:
            if rating >= ssr_current_rating:
                selected_rating = rating
                break

        if selected_rating is None:
            selected_rating = standard_ratings[-1]  # Use highest rating

        return {
            "power_per_zone": power_per_zone,
            "current_per_zone": current_per_zone,
            "ssr_current_rating": ssr_current_rating,
            "selected_ssr_rating": selected_rating,
            "safety_factor": safety_factor,
            "voltage": voltage,
            "control_type": control_options.get("type", "proportional"),
        }

    def _recommend_ssr_configuration(
        self,
        control_strategy: str,
        ssr_ratings: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Recommend SSR configuration based on strategy and ratings."""
        recommendations = {
            "ssr_type": "solid_state_relay",
            "control_signal": "4-20mA"
            if control_strategy == "proportional"
            else "digital",
            "heat_sink_required": ssr_ratings["selected_ssr_rating"] > 40,
            "protection_required": True,
            "monitoring_recommended": control_strategy in ["proportional", "staged"],
        }

        # Add specific recommendations based on power level
        if ssr_ratings["power_per_zone"] > 5000:  # 5kW
            recommendations["parallel_ssrs"] = True
            recommendations["load_sharing"] = True
        else:
            recommendations["parallel_ssrs"] = False
            recommendations["load_sharing"] = False

        # Add control recommendations
        if control_strategy == "proportional":
            recommendations["controller_type"] = "PID"
            recommendations["feedback_required"] = True
        elif control_strategy == "staged":
            recommendations["controller_type"] = "multi_stage"
            recommendations["feedback_required"] = True
        else:
            recommendations["controller_type"] = "simple_thermostat"
            recommendations["feedback_required"] = False

        return recommendations
