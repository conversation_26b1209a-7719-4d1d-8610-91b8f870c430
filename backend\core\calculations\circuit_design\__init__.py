# backend/core/calculations/circuit_design/__init__.py
"""
Circuit Design Calculations Module.

This module contains circuit design calculations for circuit breakers,
control circuits, and electrical protection systems.
"""

from .circuit_breaker_sizing import (
    calculate_circuit_breaker_size,
    select_optimal_breaker,
    validate_breaker_coordination,
)
from .control_circuit_logic import (
    design_control_circuit,
    calculate_control_power,
    validate_control_logic,
)

__all__ = [
    "calculate_circuit_breaker_size",
    "select_optimal_breaker", 
    "validate_breaker_coordination",
    "design_control_circuit",
    "calculate_control_power",
    "validate_control_logic",
]
