# backend/core/calculations/circuit_design/circuit_breaker_sizing.py
"""
Circuit Breaker Sizing Calculations.

This module implements circuit breaker sizing calculations according to
electrical standards and safety requirements.
"""

import logging
import math
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)

# Standard circuit breaker ratings (Amperes)
STANDARD_BREAKER_RATINGS = [
    15, 20, 25, 30, 35, 40, 45, 50, 60, 70, 80, 90, 100,
    110, 125, 150, 175, 200, 225, 250, 300, 350, 400, 450,
    500, 600, 700, 800, 1000, 1200, 1600, 2000, 2500, 3000
]

# Breaker types and characteristics
BREAKER_TYPES = {
    "thermal_magnetic": {
        "trip_curve": "B",
        "breaking_capacity": 10000,  # kA
        "applications": ["general", "motor_protection"],
        "cost_factor": 1.0,
    },
    "electronic": {
        "trip_curve": "adjustable",
        "breaking_capacity": 25000,
        "applications": ["precision", "coordination"],
        "cost_factor": 1.5,
    },
    "molded_case": {
        "trip_curve": "C",
        "breaking_capacity": 50000,
        "applications": ["industrial", "high_current"],
        "cost_factor": 2.0,
    },
}

# Safety factors for different applications
SAFETY_FACTORS = {
    "general": 1.25,
    "motor_starting": 1.5,
    "heating_elements": 1.25,
    "hazardous_area": 1.4,
    "critical_systems": 1.6,
}


@dataclass
class BreakerSizingResult:
    """Result of circuit breaker sizing calculation."""
    recommended_rating: float
    selected_breaker: Dict[str, Any]
    safety_factor_applied: float
    coordination_check: bool
    breaking_capacity_adequate: bool
    cost_estimate: float
    alternatives: List[Dict[str, Any]]


def calculate_circuit_breaker_size(
    load_current: float,
    application_type: str = "general",
    fault_current: float = 10000.0,
    voltage: float = 400.0,
    ambient_temperature: float = 40.0,
    installation_method: str = "panel_mount",
) -> BreakerSizingResult:
    """
    Calculate required circuit breaker size for a given load.

    Args:
        load_current: Load current in Amperes
        application_type: Type of application (general, motor_starting, etc.)
        fault_current: Available fault current in Amperes
        voltage: System voltage in Volts
        ambient_temperature: Ambient temperature in °C
        installation_method: Installation method

    Returns:
        BreakerSizingResult: Comprehensive sizing results

    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating breaker size for {load_current}A load")

    try:
        # Validate inputs
        _validate_breaker_inputs(
            load_current, application_type, fault_current, voltage, ambient_temperature
        )

        # Apply safety factor
        safety_factor = SAFETY_FACTORS.get(application_type, 1.25)
        required_rating = load_current * safety_factor

        # Apply temperature derating if necessary
        if ambient_temperature > 40.0:
            temp_derating = 1.0 - (ambient_temperature - 40.0) * 0.01
            required_rating = required_rating / temp_derating

        # Select standard breaker rating
        selected_rating = _select_standard_rating(required_rating)

        # Select optimal breaker type
        optimal_breaker = select_optimal_breaker(
            selected_rating, fault_current, application_type, voltage
        )

        # Validate coordination
        coordination_ok = validate_breaker_coordination(
            optimal_breaker, load_current, fault_current
        )

        # Check breaking capacity
        breaking_capacity_ok = optimal_breaker["breaking_capacity"] >= fault_current / 1000

        # Calculate cost estimate
        cost_estimate = _calculate_breaker_cost(optimal_breaker, selected_rating)

        # Find alternatives
        alternatives = _find_alternative_breakers(
            selected_rating, fault_current, application_type
        )

        result = BreakerSizingResult(
            recommended_rating=selected_rating,
            selected_breaker=optimal_breaker,
            safety_factor_applied=safety_factor,
            coordination_check=coordination_ok,
            breaking_capacity_adequate=breaking_capacity_ok,
            cost_estimate=cost_estimate,
            alternatives=alternatives,
        )

        logger.info(f"Breaker sizing completed: {selected_rating}A {optimal_breaker['type']}")
        return result

    except InvalidInputError:
        raise
    except Exception as e:
        logger.error(f"Breaker sizing calculation failed: {e}", exc_info=True)
        raise CalculationError(f"Breaker sizing calculation failed: {str(e)}")


def select_optimal_breaker(
    rating: float,
    fault_current: float,
    application_type: str,
    voltage: float,
) -> Dict[str, Any]:
    """
    Select optimal breaker type for given requirements.

    Args:
        rating: Required breaker rating in Amperes
        fault_current: Available fault current in Amperes
        application_type: Application type
        voltage: System voltage

    Returns:
        Dict with optimal breaker specifications
    """
    logger.debug(f"Selecting optimal breaker for {rating}A, {fault_current}A fault")

    # Score each breaker type
    best_score = 0
    optimal_breaker = None

    for breaker_type, specs in BREAKER_TYPES.items():
        score = 0

        # Check breaking capacity adequacy
        if specs["breaking_capacity"] >= fault_current / 1000:
            score += 40

        # Check application suitability
        if application_type in specs["applications"]:
            score += 30

        # Consider cost (lower cost = higher score)
        cost_score = max(0, 20 - (specs["cost_factor"] - 1.0) * 10)
        score += cost_score

        # Voltage compatibility (assume all compatible for now)
        score += 10

        if score > best_score:
            best_score = score
            optimal_breaker = {
                "type": breaker_type,
                "rating": rating,
                "breaking_capacity": specs["breaking_capacity"],
                "trip_curve": specs["trip_curve"],
                "cost_factor": specs["cost_factor"],
                "score": score,
            }

    if optimal_breaker is None:
        raise CalculationError("No suitable breaker found for requirements")

    logger.debug(f"Selected {optimal_breaker['type']} breaker with score {best_score}")
    return optimal_breaker


def validate_breaker_coordination(
    breaker: Dict[str, Any], load_current: float, fault_current: float
) -> bool:
    """
    Validate breaker coordination and selectivity.

    Args:
        breaker: Breaker specifications
        load_current: Load current
        fault_current: Fault current

    Returns:
        bool: True if coordination is adequate
    """
    logger.debug("Validating breaker coordination")

    try:
        # Check if breaker rating is appropriate for load
        if breaker["rating"] < load_current * 1.25:
            logger.warning("Breaker rating too low for load current")
            return False

        # Check breaking capacity
        if breaker["breaking_capacity"] < fault_current / 1000:
            logger.warning("Breaking capacity insufficient for fault current")
            return False

        # Check trip curve compatibility (simplified)
        trip_curve = breaker.get("trip_curve", "C")
        if trip_curve == "B" and load_current > 100:
            logger.warning("B-curve breaker may not be suitable for high current loads")
            return False

        logger.debug("Breaker coordination validation passed")
        return True

    except Exception as e:
        logger.error(f"Coordination validation failed: {e}")
        return False


def _validate_breaker_inputs(
    load_current: float,
    application_type: str,
    fault_current: float,
    voltage: float,
    ambient_temperature: float,
) -> None:
    """Validate input parameters for breaker sizing."""
    if load_current <= 0:
        raise InvalidInputError("Load current must be positive")

    if load_current > 5000:
        raise InvalidInputError("Load current exceeds maximum supported value (5000A)")

    if application_type not in SAFETY_FACTORS:
        raise InvalidInputError(f"Invalid application type: {application_type}")

    if fault_current <= 0:
        raise InvalidInputError("Fault current must be positive")

    if voltage <= 0:
        raise InvalidInputError("Voltage must be positive")

    if ambient_temperature < -40 or ambient_temperature > 80:
        raise InvalidInputError("Ambient temperature must be between -40°C and 80°C")


def _select_standard_rating(required_rating: float) -> float:
    """Select next higher standard breaker rating."""
    for rating in STANDARD_BREAKER_RATINGS:
        if rating >= required_rating:
            return rating
    
    # If no standard rating is high enough, return the highest
    return STANDARD_BREAKER_RATINGS[-1]


def _calculate_breaker_cost(breaker: Dict[str, Any], rating: float) -> float:
    """Calculate estimated breaker cost."""
    base_cost = 50.0  # Base cost in currency units
    rating_factor = math.log10(rating / 10) if rating > 10 else 1.0
    type_factor = breaker.get("cost_factor", 1.0)
    
    return base_cost * rating_factor * type_factor


def _find_alternative_breakers(
    rating: float, fault_current: float, application_type: str
) -> List[Dict[str, Any]]:
    """Find alternative breaker options."""
    alternatives = []
    
    for breaker_type, specs in BREAKER_TYPES.items():
        if specs["breaking_capacity"] >= fault_current / 1000:
            alternative = {
                "type": breaker_type,
                "rating": rating,
                "breaking_capacity": specs["breaking_capacity"],
                "cost_factor": specs["cost_factor"],
                "estimated_cost": _calculate_breaker_cost(
                    {"cost_factor": specs["cost_factor"]}, rating
                ),
            }
            alternatives.append(alternative)
    
    # Sort by cost
    alternatives.sort(key=lambda x: x["estimated_cost"])
    return alternatives[:3]  # Return top 3 alternatives
