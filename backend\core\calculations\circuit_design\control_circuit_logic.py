# backend/core/calculations/circuit_design/control_circuit_logic.py
"""
Control Circuit Design Logic.

This module implements control circuit design calculations and logic
for heat tracing systems including contactors, relays, and control power.
"""

import logging
import math
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)


class ControlType(Enum):
    """Types of control circuits."""
    MANUAL = "manual"
    AUTOMATIC = "automatic"
    REMOTE = "remote"
    SMART = "smart"


class ContactorType(Enum):
    """Types of contactors."""
    AC = "ac"
    DC = "dc"
    SOLID_STATE = "solid_state"


@dataclass
class ControlCircuitDesign:
    """Result of control circuit design."""
    control_type: str
    contactor_specifications: Dict[str, Any]
    control_power_requirements: Dict[str, Any]
    protection_devices: List[Dict[str, Any]]
    wiring_requirements: Dict[str, Any]
    estimated_cost: float
    reliability_score: float


# Standard contactor ratings and specifications
CONTACTOR_SPECIFICATIONS = {
    "ac": {
        "voltage_ratings": [24, 110, 230, 400, 500],
        "current_ratings": [9, 12, 18, 25, 32, 40, 50, 65, 80, 95, 115, 150, 185, 225, 265, 330, 400],
        "coil_power": {"24V": 8, "110V": 15, "230V": 20, "400V": 25},
        "contact_life": 1000000,  # operations
        "cost_factor": 1.0,
    },
    "dc": {
        "voltage_ratings": [12, 24, 48, 110, 220],
        "current_ratings": [10, 16, 25, 40, 63, 80, 100, 125, 160, 200, 250, 315, 400],
        "coil_power": {"12V": 5, "24V": 8, "48V": 12, "110V": 15},
        "contact_life": 500000,
        "cost_factor": 1.2,
    },
    "solid_state": {
        "voltage_ratings": [24, 48, 110, 230, 400, 500],
        "current_ratings": [10, 25, 40, 60, 90, 120, 150, 200, 280, 360, 480],
        "coil_power": {"24V": 2, "48V": 3, "110V": 5, "230V": 8},
        "contact_life": 10000000,  # much higher for SSR
        "cost_factor": 2.0,
    },
}

# Control circuit protection requirements
PROTECTION_REQUIREMENTS = {
    "control_fuse": {"rating_factor": 1.5, "type": "fast_acting"},
    "auxiliary_contacts": {"normally_open": 2, "normally_closed": 1},
    "status_indication": {"power_on": True, "fault": True, "running": True},
    "emergency_stop": {"required": True, "type": "normally_closed"},
}


def design_control_circuit(
    load_current: float,
    control_voltage: float = 230.0,
    control_type: str = "automatic",
    hazardous_area: bool = False,
    redundancy_required: bool = False,
    remote_control: bool = False,
) -> ControlCircuitDesign:
    """
    Design control circuit for heat tracing system.

    Args:
        load_current: Load current to be controlled (A)
        control_voltage: Control circuit voltage (V)
        control_type: Type of control (manual, automatic, remote, smart)
        hazardous_area: Whether installation is in hazardous area
        redundancy_required: Whether redundant control is required
        remote_control: Whether remote control capability is needed

    Returns:
        ControlCircuitDesign: Complete control circuit design

    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If design calculation fails
    """
    logger.debug(f"Designing control circuit for {load_current}A load")

    try:
        # Validate inputs
        _validate_control_inputs(load_current, control_voltage, control_type)

        # Select appropriate contactor
        contactor_specs = _select_contactor(
            load_current, control_voltage, hazardous_area, redundancy_required
        )

        # Calculate control power requirements
        control_power = calculate_control_power(
            contactor_specs, control_voltage, remote_control
        )

        # Design protection devices
        protection_devices = _design_protection_devices(
            contactor_specs, control_power, hazardous_area
        )

        # Calculate wiring requirements
        wiring_requirements = _calculate_wiring_requirements(
            contactor_specs, control_power, remote_control
        )

        # Estimate cost
        estimated_cost = _estimate_control_cost(
            contactor_specs, protection_devices, wiring_requirements
        )

        # Calculate reliability score
        reliability_score = _calculate_reliability_score(
            contactor_specs, redundancy_required, hazardous_area
        )

        design = ControlCircuitDesign(
            control_type=control_type,
            contactor_specifications=contactor_specs,
            control_power_requirements=control_power,
            protection_devices=protection_devices,
            wiring_requirements=wiring_requirements,
            estimated_cost=estimated_cost,
            reliability_score=reliability_score,
        )

        logger.info(f"Control circuit design completed: {contactor_specs['type']} contactor")
        return design

    except InvalidInputError:
        raise
    except Exception as e:
        logger.error(f"Control circuit design failed: {e}", exc_info=True)
        raise CalculationError(f"Control circuit design failed: {str(e)}")


def calculate_control_power(
    contactor_specs: Dict[str, Any],
    control_voltage: float,
    remote_control: bool = False,
) -> Dict[str, Any]:
    """
    Calculate control power requirements.

    Args:
        contactor_specs: Contactor specifications
        control_voltage: Control voltage
        remote_control: Whether remote control is needed

    Returns:
        Dict with control power requirements
    """
    logger.debug("Calculating control power requirements")

    try:
        # Base coil power
        coil_power = contactor_specs["coil_power"]
        
        # Additional power for auxiliary devices
        auxiliary_power = 0
        if remote_control:
            auxiliary_power += 10  # Remote control interface
        
        auxiliary_power += 5  # Status indicators
        auxiliary_power += 3  # Control relays

        total_power = coil_power + auxiliary_power

        # Calculate current
        control_current = total_power / control_voltage

        # Add safety margin
        design_power = total_power * 1.25
        design_current = design_power / control_voltage

        return {
            "coil_power": coil_power,
            "auxiliary_power": auxiliary_power,
            "total_power": total_power,
            "design_power": design_power,
            "control_current": control_current,
            "design_current": design_current,
            "control_voltage": control_voltage,
        }

    except Exception as e:
        logger.error(f"Control power calculation failed: {e}")
        raise CalculationError(f"Control power calculation failed: {str(e)}")


def validate_control_logic(
    design: ControlCircuitDesign,
    safety_requirements: Dict[str, Any],
) -> Dict[str, Any]:
    """
    Validate control circuit logic against safety requirements.

    Args:
        design: Control circuit design
        safety_requirements: Safety requirements to validate against

    Returns:
        Dict with validation results
    """
    logger.debug("Validating control circuit logic")

    validation_results = {
        "is_valid": True,
        "violations": [],
        "warnings": [],
        "recommendations": [],
    }

    try:
        # Check emergency stop requirement
        if safety_requirements.get("emergency_stop_required", True):
            has_emergency_stop = any(
                device.get("type") == "emergency_stop" 
                for device in design.protection_devices
            )
            if not has_emergency_stop:
                validation_results["violations"].append(
                    "Emergency stop device required but not included"
                )
                validation_results["is_valid"] = False

        # Check protection adequacy
        if design.control_power_requirements["design_current"] > 10:
            has_adequate_protection = any(
                device.get("type") == "control_fuse" 
                for device in design.protection_devices
            )
            if not has_adequate_protection:
                validation_results["warnings"].append(
                    "High control current may require additional protection"
                )

        # Check reliability score
        if design.reliability_score < 0.95:
            validation_results["recommendations"].append(
                "Consider redundant control for improved reliability"
            )

        # Check contactor rating adequacy
        contactor_rating = design.contactor_specifications.get("current_rating", 0)
        if contactor_rating < design.control_power_requirements["design_current"] * 1.25:
            validation_results["violations"].append(
                "Contactor rating insufficient for control current"
            )
            validation_results["is_valid"] = False

        logger.debug(f"Control logic validation completed: valid={validation_results['is_valid']}")
        return validation_results

    except Exception as e:
        logger.error(f"Control logic validation failed: {e}")
        validation_results["is_valid"] = False
        validation_results["violations"].append(f"Validation error: {str(e)}")
        return validation_results


def _validate_control_inputs(
    load_current: float, control_voltage: float, control_type: str
) -> None:
    """Validate input parameters for control circuit design."""
    if load_current <= 0:
        raise InvalidInputError("Load current must be positive")

    if load_current > 1000:
        raise InvalidInputError("Load current exceeds maximum supported value (1000A)")

    if control_voltage not in [24, 48, 110, 230, 400, 500]:
        raise InvalidInputError(f"Unsupported control voltage: {control_voltage}V")

    valid_control_types = [ct.value for ct in ControlType]
    if control_type not in valid_control_types:
        raise InvalidInputError(f"Invalid control type: {control_type}")


def _select_contactor(
    load_current: float,
    control_voltage: float,
    hazardous_area: bool,
    redundancy_required: bool,
) -> Dict[str, Any]:
    """Select appropriate contactor for the application."""
    # Determine contactor type based on requirements
    if hazardous_area:
        contactor_type = "solid_state"  # Safer for hazardous areas
    elif load_current > 200:
        contactor_type = "ac"  # Better for high current
    else:
        contactor_type = "ac"  # Standard choice

    specs = CONTACTOR_SPECIFICATIONS[contactor_type]

    # Select current rating (next higher standard rating)
    required_rating = load_current * 1.25  # Safety factor
    selected_rating = None
    for rating in specs["current_ratings"]:
        if rating >= required_rating:
            selected_rating = rating
            break

    if selected_rating is None:
        selected_rating = specs["current_ratings"][-1]

    # Get coil power for selected voltage
    voltage_key = f"{int(control_voltage)}V"
    coil_power = specs["coil_power"].get(voltage_key, specs["coil_power"]["230V"])

    return {
        "type": contactor_type,
        "current_rating": selected_rating,
        "voltage_rating": control_voltage,
        "coil_power": coil_power,
        "contact_life": specs["contact_life"],
        "cost_factor": specs["cost_factor"],
        "redundancy": redundancy_required,
    }


def _design_protection_devices(
    contactor_specs: Dict[str, Any],
    control_power: Dict[str, Any],
    hazardous_area: bool,
) -> List[Dict[str, Any]]:
    """Design protection devices for control circuit."""
    protection_devices = []

    # Control circuit fuse
    fuse_rating = control_power["design_current"] * 1.5
    protection_devices.append({
        "type": "control_fuse",
        "rating": fuse_rating,
        "characteristics": "fast_acting",
        "purpose": "control_circuit_protection",
    })

    # Emergency stop (always required)
    protection_devices.append({
        "type": "emergency_stop",
        "contacts": "normally_closed",
        "purpose": "emergency_shutdown",
        "hazardous_area_rated": hazardous_area,
    })

    # Status indicators
    protection_devices.append({
        "type": "status_indicators",
        "indicators": ["power_on", "running", "fault"],
        "voltage": control_power["control_voltage"],
    })

    # Auxiliary contacts
    protection_devices.append({
        "type": "auxiliary_contacts",
        "normally_open": 2,
        "normally_closed": 1,
        "purpose": "status_feedback",
    })

    return protection_devices


def _calculate_wiring_requirements(
    contactor_specs: Dict[str, Any],
    control_power: Dict[str, Any],
    remote_control: bool,
) -> Dict[str, Any]:
    """Calculate wiring requirements for control circuit."""
    # Control circuit wire size based on current
    control_current = control_power["design_current"]
    
    if control_current <= 5:
        wire_size = "1.5 mm²"
    elif control_current <= 10:
        wire_size = "2.5 mm²"
    else:
        wire_size = "4.0 mm²"

    # Number of control wires
    control_wires = 4  # Basic control
    if remote_control:
        control_wires += 6  # Additional for remote interface

    return {
        "control_wire_size": wire_size,
        "number_of_control_wires": control_wires,
        "power_wire_size": f"{contactor_specs['current_rating']} A rated",
        "conduit_size": "20mm" if control_wires <= 8 else "25mm",
        "estimated_length": 50,  # meters, typical installation
    }


def _estimate_control_cost(
    contactor_specs: Dict[str, Any],
    protection_devices: List[Dict[str, Any]],
    wiring_requirements: Dict[str, Any],
) -> float:
    """Estimate total cost of control circuit."""
    # Base contactor cost
    base_cost = 100.0
    contactor_cost = base_cost * contactor_specs["cost_factor"]
    
    # Protection devices cost
    protection_cost = len(protection_devices) * 25.0
    
    # Wiring cost (simplified)
    wiring_cost = wiring_requirements["number_of_control_wires"] * 5.0
    
    # Installation cost
    installation_cost = (contactor_cost + protection_cost) * 0.3
    
    total_cost = contactor_cost + protection_cost + wiring_cost + installation_cost
    
    return round(total_cost, 2)


def _calculate_reliability_score(
    contactor_specs: Dict[str, Any],
    redundancy_required: bool,
    hazardous_area: bool,
) -> float:
    """Calculate reliability score for control circuit."""
    base_reliability = 0.95
    
    # Contactor type factor
    if contactor_specs["type"] == "solid_state":
        reliability_factor = 1.05
    elif contactor_specs["type"] == "dc":
        reliability_factor = 1.02
    else:
        reliability_factor = 1.0
    
    # Redundancy factor
    if redundancy_required:
        redundancy_factor = 1.1
    else:
        redundancy_factor = 1.0
    
    # Hazardous area factor (more stringent requirements)
    if hazardous_area:
        hazardous_factor = 0.98
    else:
        hazardous_factor = 1.0
    
    reliability_score = base_reliability * reliability_factor * redundancy_factor * hazardous_factor
    
    return min(reliability_score, 0.999)  # Cap at 99.9%
