# backend/core/calculations/common_properties/__init__.py
"""
Common Properties Module.

This module contains material properties, fluid properties, and other
common data used across various calculations.
"""

from .material_data import (
    get_material_properties,
    get_pipe_material_properties,
    get_insulation_thermal_conductivity,
    get_material_density,
    MATERIAL_PROPERTIES,
)
from .fluid_properties import (
    get_fluid_properties,
    calculate_fluid_viscosity,
    calculate_fluid_density,
    get_fluid_thermal_conductivity,
    FLUID_PROPERTIES,
)

__all__ = [
    "get_material_properties",
    "get_pipe_material_properties", 
    "get_insulation_thermal_conductivity",
    "get_material_density",
    "MATERIAL_PROPERTIES",
    "get_fluid_properties",
    "calculate_fluid_viscosity",
    "calculate_fluid_density",
    "get_fluid_thermal_conductivity",
    "FLUID_PROPERTIES",
]
