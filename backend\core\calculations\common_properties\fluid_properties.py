# backend/core/calculations/common_properties/fluid_properties.py
"""
Fluid Properties Database and Calculations.

This module contains fluid property data and calculation functions
for various fluids used in heat tracing applications.
"""

import logging
import math
from typing import Dict, Any, Optional

from core.errors.exceptions import NotFoundError, CalculationError

logger = logging.getLogger(__name__)

# Comprehensive fluid properties database
FLUID_PROPERTIES = {
    "water": {
        "density_20c": 998.2,  # kg/m³ at 20°C
        "specific_heat": 4182.0,  # J/(kg·K)
        "thermal_conductivity_20c": 0.598,  # W/(m·K) at 20°C
        "viscosity_20c": 0.001002,  # Pa·s at 20°C
        "freezing_point": 0.0,  # °C
        "boiling_point": 100.0,  # °C at 1 atm
        "vapor_pressure_constants": {"A": 8.07131, "B": 1730.63, "C": 233.426},
        "applications": ["general", "heating", "cooling"],
        "safety_class": "non_hazardous",
    },
    "ethylene_glycol": {
        "density_20c": 1113.0,
        "specific_heat": 2415.0,
        "thermal_conductivity_20c": 0.252,
        "viscosity_20c": 0.0161,
        "freezing_point": -12.9,
        "boiling_point": 197.3,
        "vapor_pressure_constants": {"A": 8.0929, "B": 2088.94, "C": 203.454},
        "applications": ["antifreeze", "heat_transfer"],
        "safety_class": "toxic",
    },
    "propylene_glycol": {
        "density_20c": 1036.0,
        "specific_heat": 2500.0,
        "thermal_conductivity_20c": 0.200,
        "viscosity_20c": 0.042,
        "freezing_point": -59.0,
        "boiling_point": 188.2,
        "vapor_pressure_constants": {"A": 7.9453, "B": 1823.22, "C": 195.0},
        "applications": ["food_grade", "antifreeze"],
        "safety_class": "food_grade",
    },
    "crude_oil": {
        "density_20c": 870.0,
        "specific_heat": 2100.0,
        "thermal_conductivity_20c": 0.145,
        "viscosity_20c": 0.010,
        "freezing_point": -30.0,
        "boiling_point": 350.0,
        "vapor_pressure_constants": {"A": 7.5, "B": 1500.0, "C": 200.0},
        "applications": ["petroleum", "process"],
        "safety_class": "flammable",
    },
    "heavy_fuel_oil": {
        "density_20c": 950.0,
        "specific_heat": 1900.0,
        "thermal_conductivity_20c": 0.130,
        "viscosity_20c": 0.180,
        "freezing_point": -10.0,
        "boiling_point": 400.0,
        "vapor_pressure_constants": {"A": 7.2, "B": 1800.0, "C": 220.0},
        "applications": ["marine", "power_generation"],
        "safety_class": "flammable",
    },
    "diesel": {
        "density_20c": 832.0,
        "specific_heat": 2090.0,
        "thermal_conductivity_20c": 0.149,
        "viscosity_20c": 0.0024,
        "freezing_point": -35.0,
        "boiling_point": 250.0,
        "vapor_pressure_constants": {"A": 7.8, "B": 1600.0, "C": 180.0},
        "applications": ["automotive", "heating"],
        "safety_class": "flammable",
    },
    "steam": {
        "density_20c": 0.598,  # kg/m³ at 100°C, 1 atm
        "specific_heat": 2080.0,  # J/(kg·K) for superheated steam
        "thermal_conductivity_20c": 0.025,
        "viscosity_20c": 0.0000123,
        "freezing_point": 0.0,
        "boiling_point": 100.0,
        "vapor_pressure_constants": {"A": 8.07131, "B": 1730.63, "C": 233.426},
        "applications": ["heating", "process"],
        "safety_class": "high_temperature",
    },
    "air": {
        "density_20c": 1.204,
        "specific_heat": 1005.0,
        "thermal_conductivity_20c": 0.0257,
        "viscosity_20c": 0.0000181,
        "freezing_point": -273.15,
        "boiling_point": -195.8,  # Nitrogen boiling point
        "vapor_pressure_constants": {"A": 0, "B": 0, "C": 0},
        "applications": ["ventilation", "pneumatic"],
        "safety_class": "non_hazardous",
    },
}


def get_fluid_properties(fluid_name: str) -> Dict[str, Any]:
    """
    Get comprehensive fluid properties.

    Args:
        fluid_name: Name of the fluid

    Returns:
        Dict containing all fluid properties

    Raises:
        NotFoundError: If fluid is not found
    """
    logger.debug(f"Getting properties for fluid: {fluid_name}")

    fluid_name = fluid_name.lower().replace(" ", "_")
    
    if fluid_name not in FLUID_PROPERTIES:
        available_fluids = list(FLUID_PROPERTIES.keys())
        raise NotFoundError(
            code="404_004",
            detail=f"Fluid '{fluid_name}' not found. "
            f"Available fluids: {available_fluids}",
            category="ClientError",
            status_code=404,
        )

    properties = FLUID_PROPERTIES[fluid_name].copy()
    properties["name"] = fluid_name
    
    logger.debug(f"Retrieved properties for {fluid_name}")
    return properties


def calculate_fluid_density(fluid_name: str, temperature: float) -> float:
    """
    Calculate fluid density at given temperature.

    Args:
        fluid_name: Name of the fluid
        temperature: Temperature (°C)

    Returns:
        float: Fluid density (kg/m³)

    Raises:
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating density for {fluid_name} at {temperature}°C")

    try:
        properties = get_fluid_properties(fluid_name)
        density_20c = properties["density_20c"]

        # Temperature correction for density
        # Most liquids: density decreases with temperature
        # Typical volumetric expansion coefficient: 0.0002 to 0.001 per °C
        
        if fluid_name == "water":
            # Water has specific density-temperature relationship
            density = _calculate_water_density(temperature)
        elif fluid_name in ["ethylene_glycol", "propylene_glycol"]:
            # Glycols have higher expansion coefficients
            expansion_coeff = 0.0006
            density = density_20c / (1 + expansion_coeff * (temperature - 20.0))
        elif "oil" in fluid_name or fluid_name == "diesel":
            # Petroleum products
            expansion_coeff = 0.0008
            density = density_20c / (1 + expansion_coeff * (temperature - 20.0))
        elif fluid_name == "steam":
            # Steam density calculation (ideal gas approximation)
            density = _calculate_steam_density(temperature)
        elif fluid_name == "air":
            # Air density calculation (ideal gas)
            density = _calculate_air_density(temperature)
        else:
            # Generic liquid expansion
            expansion_coeff = 0.0005
            density = density_20c / (1 + expansion_coeff * (temperature - 20.0))

        logger.debug(f"Calculated density: {density:.2f} kg/m³")
        return density

    except NotFoundError:
        raise
    except Exception as e:
        logger.error(f"Density calculation failed: {e}")
        raise CalculationError(f"Density calculation failed: {str(e)}")


def calculate_fluid_viscosity(fluid_name: str, temperature: float) -> float:
    """
    Calculate fluid dynamic viscosity at given temperature.

    Args:
        fluid_name: Name of the fluid
        temperature: Temperature (°C)

    Returns:
        float: Dynamic viscosity (Pa·s)

    Raises:
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating viscosity for {fluid_name} at {temperature}°C")

    try:
        properties = get_fluid_properties(fluid_name)
        viscosity_20c = properties["viscosity_20c"]

        # Temperature correction for viscosity
        # Liquids: viscosity decreases exponentially with temperature
        # Gases: viscosity increases with temperature
        
        if fluid_name == "water":
            # Water viscosity correlation
            viscosity = _calculate_water_viscosity(temperature)
        elif fluid_name in ["ethylene_glycol", "propylene_glycol"]:
            # Glycol viscosity (strong temperature dependence)
            viscosity = viscosity_20c * math.exp(1500 * (1/(temperature + 273.15) - 1/293.15))
        elif "oil" in fluid_name or fluid_name == "diesel":
            # Petroleum products (Andrade equation)
            viscosity = viscosity_20c * math.exp(1000 * (1/(temperature + 273.15) - 1/293.15))
        elif fluid_name in ["steam", "air"]:
            # Gas viscosity (Sutherland's law approximation)
            viscosity = viscosity_20c * ((temperature + 273.15) / 293.15) ** 0.7
        else:
            # Generic liquid viscosity
            viscosity = viscosity_20c * math.exp(800 * (1/(temperature + 273.15) - 1/293.15))

        logger.debug(f"Calculated viscosity: {viscosity:.6f} Pa·s")
        return viscosity

    except NotFoundError:
        raise
    except Exception as e:
        logger.error(f"Viscosity calculation failed: {e}")
        raise CalculationError(f"Viscosity calculation failed: {str(e)}")


def get_fluid_thermal_conductivity(fluid_name: str, temperature: float) -> float:
    """
    Get fluid thermal conductivity at given temperature.

    Args:
        fluid_name: Name of the fluid
        temperature: Temperature (°C)

    Returns:
        float: Thermal conductivity (W/m·K)
    """
    logger.debug(f"Getting thermal conductivity for {fluid_name} at {temperature}°C")

    try:
        properties = get_fluid_properties(fluid_name)
        k_20c = properties["thermal_conductivity_20c"]

        # Temperature correction for thermal conductivity
        if fluid_name == "water":
            # Water thermal conductivity increases slightly with temperature
            k = k_20c * (1 + 0.001 * (temperature - 20.0))
        elif fluid_name in ["ethylene_glycol", "propylene_glycol"]:
            # Glycols: slight decrease with temperature
            k = k_20c * (1 - 0.0005 * (temperature - 20.0))
        elif "oil" in fluid_name or fluid_name == "diesel":
            # Petroleum products: decrease with temperature
            k = k_20c * (1 - 0.0008 * (temperature - 20.0))
        elif fluid_name in ["steam", "air"]:
            # Gases: increase with temperature
            k = k_20c * ((temperature + 273.15) / 293.15) ** 0.8
        else:
            # Generic correction
            k = k_20c * (1 - 0.0003 * (temperature - 20.0))

        logger.debug(f"Thermal conductivity: {k:.4f} W/m·K")
        return max(k, 0.001)  # Minimum value for stability

    except NotFoundError:
        raise
    except Exception as e:
        logger.error(f"Thermal conductivity calculation failed: {e}")
        return properties["thermal_conductivity_20c"]  # Fallback to 20°C value


def _calculate_water_density(temperature: float) -> float:
    """Calculate water density using polynomial correlation."""
    # Polynomial correlation for water density (kg/m³)
    # Valid from 0°C to 100°C
    t = temperature
    if t < 0:
        t = 0
    elif t > 100:
        t = 100
    
    density = (999.83952 + 16.945176 * t - 7.9870401e-3 * t**2 
               - 46.170461e-6 * t**3 + 105.56302e-9 * t**4 
               - 280.54253e-12 * t**5) / (1 + 16.879850e-3 * t)
    
    return density


def _calculate_water_viscosity(temperature: float) -> float:
    """Calculate water dynamic viscosity using correlation."""
    # Correlation for water viscosity (Pa·s)
    # Valid from 0°C to 100°C
    t = max(0, min(temperature, 100))
    
    # Simplified correlation
    viscosity = 0.001792 * math.exp(-0.0255 * t + 0.000085 * t**2)
    
    return viscosity


def _calculate_steam_density(temperature: float) -> float:
    """Calculate steam density using ideal gas law."""
    # Steam density at atmospheric pressure
    # Using ideal gas law: ρ = PM/(RT)
    pressure = 101325  # Pa (atmospheric)
    molar_mass = 0.018015  # kg/mol (water)
    R = 8.314  # J/(mol·K)
    
    temp_k = temperature + 273.15
    density = (pressure * molar_mass) / (R * temp_k)
    
    return density


def _calculate_air_density(temperature: float) -> float:
    """Calculate air density using ideal gas law."""
    # Air density at atmospheric pressure
    pressure = 101325  # Pa
    molar_mass = 0.02897  # kg/mol (dry air)
    R = 8.314  # J/(mol·K)
    
    temp_k = temperature + 273.15
    density = (pressure * molar_mass) / (R * temp_k)
    
    return density
