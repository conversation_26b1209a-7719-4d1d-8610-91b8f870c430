# backend/core/calculations/common_properties/material_data.py
"""
Material Properties Database.

This module contains comprehensive material property data for pipes,
insulation, and other components used in heat tracing calculations.
"""

import logging
from typing import Dict, Any, Optional, List

from core.errors.exceptions import NotFoundError

logger = logging.getLogger(__name__)

# Comprehensive material properties database
MATERIAL_PROPERTIES = {
    # Pipe materials
    "carbon_steel": {
        "thermal_conductivity": 50.0,  # W/(m·K)
        "density": 7850.0,  # kg/m³
        "specific_heat": 460.0,  # J/(kg·K)
        "emissivity": 0.8,
        "max_temperature": 400.0,  # °C
        "corrosion_resistance": "poor",
        "cost_factor": 1.0,
        "applications": ["general", "low_temperature"],
    },
    "stainless_steel_304": {
        "thermal_conductivity": 16.2,
        "density": 8000.0,
        "specific_heat": 500.0,
        "emissivity": 0.28,
        "max_temperature": 800.0,
        "corrosion_resistance": "excellent",
        "cost_factor": 3.5,
        "applications": ["corrosive", "high_temperature", "food_grade"],
    },
    "stainless_steel_316": {
        "thermal_conductivity": 16.3,
        "density": 8000.0,
        "specific_heat": 500.0,
        "emissivity": 0.28,
        "max_temperature": 800.0,
        "corrosion_resistance": "excellent",
        "cost_factor": 4.0,
        "applications": ["marine", "chemical", "high_temperature"],
    },
    "copper": {
        "thermal_conductivity": 401.0,
        "density": 8960.0,
        "specific_heat": 385.0,
        "emissivity": 0.04,
        "max_temperature": 200.0,
        "corrosion_resistance": "good",
        "cost_factor": 6.0,
        "applications": ["heat_transfer", "plumbing"],
    },
    "aluminum": {
        "thermal_conductivity": 237.0,
        "density": 2700.0,
        "specific_heat": 897.0,
        "emissivity": 0.04,
        "max_temperature": 300.0,
        "corrosion_resistance": "good",
        "cost_factor": 2.5,
        "applications": ["lightweight", "heat_transfer"],
    },
    "pvc": {
        "thermal_conductivity": 0.19,
        "density": 1400.0,
        "specific_heat": 900.0,
        "emissivity": 0.91,
        "max_temperature": 60.0,
        "corrosion_resistance": "excellent",
        "cost_factor": 0.5,
        "applications": ["chemical", "low_temperature"],
    },
    "hdpe": {
        "thermal_conductivity": 0.48,
        "density": 950.0,
        "specific_heat": 2300.0,
        "emissivity": 0.94,
        "max_temperature": 80.0,
        "corrosion_resistance": "excellent",
        "cost_factor": 0.8,
        "applications": ["chemical", "buried"],
    },
    # Insulation materials (extended from existing)
    "mineral_wool": {
        "thermal_conductivity": 0.040,
        "density": 100.0,
        "specific_heat": 840.0,
        "max_temperature": 700.0,
        "moisture_resistance": "poor",
        "cost_factor": 1.0,
        "applications": ["general", "high_temperature"],
    },
    "glass_wool": {
        "thermal_conductivity": 0.035,
        "density": 50.0,
        "specific_heat": 840.0,
        "max_temperature": 450.0,
        "moisture_resistance": "poor",
        "cost_factor": 0.8,
        "applications": ["general", "low_temperature"],
    },
    "polyurethane_foam": {
        "thermal_conductivity": 0.025,
        "density": 40.0,
        "specific_heat": 1400.0,
        "max_temperature": 110.0,
        "moisture_resistance": "excellent",
        "cost_factor": 1.5,
        "applications": ["low_temperature", "moisture_prone"],
    },
    "phenolic_foam": {
        "thermal_conductivity": 0.020,
        "density": 50.0,
        "specific_heat": 1400.0,
        "max_temperature": 120.0,
        "moisture_resistance": "good",
        "cost_factor": 2.0,
        "applications": ["fire_resistant", "low_temperature"],
    },
    "calcium_silicate": {
        "thermal_conductivity": 0.055,
        "density": 240.0,
        "specific_heat": 1000.0,
        "max_temperature": 650.0,
        "moisture_resistance": "fair",
        "cost_factor": 1.8,
        "applications": ["high_temperature", "industrial"],
    },
    "perlite": {
        "thermal_conductivity": 0.050,
        "density": 120.0,
        "specific_heat": 837.0,
        "max_temperature": 870.0,
        "moisture_resistance": "poor",
        "cost_factor": 1.2,
        "applications": ["very_high_temperature", "industrial"],
    },
    "aerogel": {
        "thermal_conductivity": 0.015,
        "density": 150.0,
        "specific_heat": 1000.0,
        "max_temperature": 200.0,
        "moisture_resistance": "excellent",
        "cost_factor": 10.0,
        "applications": ["space_constrained", "premium"],
    },
}

# Temperature correction factors for thermal conductivity
TEMPERATURE_CORRECTIONS = {
    "mineral_wool": {
        "base_temp": 20.0,  # °C
        "correction_factor": 0.0002,  # per °C
    },
    "glass_wool": {
        "base_temp": 20.0,
        "correction_factor": 0.0002,
    },
    "polyurethane_foam": {
        "base_temp": 20.0,
        "correction_factor": 0.0003,
    },
    "calcium_silicate": {
        "base_temp": 20.0,
        "correction_factor": 0.0001,
    },
}


def get_material_properties(material_name: str) -> Dict[str, Any]:
    """
    Get comprehensive material properties.

    Args:
        material_name: Name of the material

    Returns:
        Dict containing all material properties

    Raises:
        NotFoundError: If material is not found
    """
    logger.debug(f"Getting properties for material: {material_name}")

    material_name = material_name.lower().replace(" ", "_")

    if material_name not in MATERIAL_PROPERTIES:
        available_materials = list(MATERIAL_PROPERTIES.keys())
        raise NotFoundError(
            code="404_003",
            detail=f"Material '{material_name}' not found. "
            f"Available materials: {available_materials}",
            category="ClientError",
            status_code=404,
        )

    properties = MATERIAL_PROPERTIES[material_name].copy()
    properties["name"] = material_name

    logger.debug(f"Retrieved properties for {material_name}")
    return properties


def get_pipe_material_properties(material_name: str) -> Dict[str, Any]:
    """
    Get properties specific to pipe materials.

    Args:
        material_name: Name of the pipe material

    Returns:
        Dict containing pipe-specific properties
    """
    logger.debug(f"Getting pipe material properties: {material_name}")

    properties = get_material_properties(material_name)

    # Filter for pipe-relevant properties
    pipe_properties = {
        "thermal_conductivity": properties["thermal_conductivity"],
        "density": properties["density"],
        "emissivity": properties.get("emissivity", 0.8),
        "max_temperature": properties["max_temperature"],
        "corrosion_resistance": properties.get("corrosion_resistance", "unknown"),
        "cost_factor": properties["cost_factor"],
    }

    return pipe_properties


def get_insulation_thermal_conductivity(
    insulation_type: str, temperature: float = 20.0
) -> float:
    """
    Get temperature-corrected thermal conductivity for insulation.

    Args:
        insulation_type: Type of insulation material
        temperature: Operating temperature (°C)

    Returns:
        float: Temperature-corrected thermal conductivity (W/m·K)
    """
    logger.debug(
        f"Getting thermal conductivity for {insulation_type} at {temperature}°C"
    )

    properties = get_material_properties(insulation_type)
    base_conductivity = properties["thermal_conductivity"]

    # Apply temperature correction if available
    if insulation_type in TEMPERATURE_CORRECTIONS:
        correction_data = TEMPERATURE_CORRECTIONS[insulation_type]
        base_temp = correction_data["base_temp"]
        correction_factor = correction_data["correction_factor"]

        temp_diff = temperature - base_temp
        corrected_conductivity = base_conductivity * (1 + correction_factor * temp_diff)

        logger.debug(
            f"Applied temperature correction: {base_conductivity:.4f} -> {corrected_conductivity:.4f}"
        )
        return corrected_conductivity

    logger.debug(f"No temperature correction available for {insulation_type}")
    return base_conductivity


def get_material_density(material_name: str, temperature: float = 20.0) -> float:
    """
    Get material density with optional temperature correction.

    Args:
        material_name: Name of the material
        temperature: Temperature (°C)

    Returns:
        float: Material density (kg/m³)
    """
    logger.debug(f"Getting density for {material_name} at {temperature}°C")

    properties = get_material_properties(material_name)
    base_density = properties["density"]

    # Simple temperature correction for density (most materials expand with temperature)
    # Typical volumetric expansion coefficient: 3 * linear expansion coefficient
    # For most materials: ~0.00003 per °C
    temp_correction = 1 - (temperature - 20.0) * 0.00003
    corrected_density = base_density * temp_correction

    logger.debug(f"Density with temperature correction: {corrected_density:.1f} kg/m³")
    return corrected_density


def get_material_emissivity(
    material_name: str, surface_condition: str = "oxidized"
) -> float:
    """
    Get material emissivity based on surface condition.

    Args:
        material_name: Name of the material
        surface_condition: Surface condition (polished, oxidized, painted, etc.)

    Returns:
        float: Material emissivity
    """
    logger.debug(
        f"Getting emissivity for {material_name}, condition: {surface_condition}"
    )

    properties = get_material_properties(material_name)
    base_emissivity = properties.get("emissivity", 0.8)

    # Adjust emissivity based on surface condition
    emissivity_factors = {
        "polished": 0.3,
        "clean": 0.7,
        "oxidized": 1.0,
        "painted": 1.2,
        "rough": 1.1,
    }

    factor = emissivity_factors.get(surface_condition, 1.0)
    adjusted_emissivity = min(base_emissivity * factor, 0.95)  # Cap at 0.95

    logger.debug(f"Adjusted emissivity: {adjusted_emissivity:.3f}")
    return adjusted_emissivity


def validate_material_temperature(material_name: str, temperature: float) -> bool:
    """
    Validate if material can operate at given temperature.

    Args:
        material_name: Name of the material
        temperature: Operating temperature (°C)

    Returns:
        bool: True if temperature is within limits
    """
    logger.debug(f"Validating temperature {temperature}°C for {material_name}")

    try:
        properties = get_material_properties(material_name)
        max_temp = properties["max_temperature"]

        is_valid = temperature <= max_temp

        if not is_valid:
            logger.warning(
                f"Temperature {temperature}°C exceeds maximum for {material_name} ({max_temp}°C)"
            )

        return is_valid

    except NotFoundError:
        logger.error(
            f"Cannot validate temperature for unknown material: {material_name}"
        )
        return False


def get_compatible_materials(
    application: str, max_temperature: float, corrosion_resistance: Optional[str] = None
) -> List[str]:
    """
    Get list of materials compatible with given requirements.

    Args:
        application: Application type
        max_temperature: Maximum operating temperature (°C)
        corrosion_resistance: Required corrosion resistance level

    Returns:
        List of compatible material names
    """
    logger.debug(f"Finding materials for {application}, max temp: {max_temperature}°C")

    compatible_materials = []

    for material_name, properties in MATERIAL_PROPERTIES.items():
        # Check temperature compatibility
        if properties["max_temperature"] < max_temperature:
            continue

        # Check application compatibility
        if application not in properties.get("applications", []):
            continue

        # Check corrosion resistance if specified
        if corrosion_resistance:
            material_resistance = properties.get("corrosion_resistance", "unknown")
            if corrosion_resistance == "excellent" and material_resistance not in [
                "excellent"
            ]:
                continue
            elif corrosion_resistance == "good" and material_resistance not in [
                "excellent",
                "good",
            ]:
                continue

        compatible_materials.append(material_name)

    logger.debug(f"Found {len(compatible_materials)} compatible materials")
    return compatible_materials
