# backend/core/calculations/utils/__init__.py
"""
Calculation Utilities Module.

This module contains utility functions for calculations including
input parsing, validation, mathematical helpers, and unit conversions.
"""

from .input_parser import (
    parse_calculation_inputs,
    sanitize_numeric_input,
    validate_input_ranges,
)
from .validation_rules import (
    validate_cable_selection,
    validate_temperature_range,
    validate_pressure_range,
    validate_power_range,
)
from .math_helpers import (
    interpolate_linear,
    interpolate_bilinear,
    solve_iterative,
    calculate_polynomial,
)
from .units_conversion import (
    convert_temperature,
    convert_pressure,
    convert_power,
    convert_length,
    convert_area,
)

__all__ = [
    "parse_calculation_inputs",
    "sanitize_numeric_input",
    "validate_input_ranges",
    "validate_cable_selection",
    "validate_temperature_range",
    "validate_pressure_range", 
    "validate_power_range",
    "interpolate_linear",
    "interpolate_bilinear",
    "solve_iterative",
    "calculate_polynomial",
    "convert_temperature",
    "convert_pressure",
    "convert_power",
    "convert_length",
    "convert_area",
]
