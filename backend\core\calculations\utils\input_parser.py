# backend/core/calculations/utils/input_parser.py
"""
Input Parsing and Sanitization Utilities.

This module provides utilities for parsing and sanitizing calculation inputs
to ensure they are in the correct format for algorithmic consumption.
"""

import logging
import re
from typing import Dict, Any, Union, Optional, List
from decimal import Decimal, InvalidOperation

from core.errors.exceptions import InvalidInputError

logger = logging.getLogger(__name__)

# Regular expressions for input validation
NUMERIC_PATTERN = re.compile(r'^-?\d*\.?\d+([eE][+-]?\d+)?$')
UNIT_PATTERN = re.compile(r'^([\d\.\-\+eE]+)\s*([a-zA-Z°%]+)?$')

# Common unit mappings
UNIT_MAPPINGS = {
    # Temperature units
    'c': '°C', 'celsius': '°C', 'degc': '°C',
    'f': '°F', 'fahrenheit': '°F', 'degf': '°F',
    'k': 'K', 'kelvin': 'K',
    
    # Length units
    'm': 'm', 'meter': 'm', 'metre': 'm',
    'mm': 'mm', 'millimeter': 'mm', 'millimetre': 'mm',
    'cm': 'cm', 'centimeter': 'cm', 'centimetre': 'cm',
    'ft': 'ft', 'foot': 'ft', 'feet': 'ft',
    'in': 'in', 'inch': 'in', 'inches': 'in',
    
    # Power units
    'w': 'W', 'watt': 'W', 'watts': 'W',
    'kw': 'kW', 'kilowatt': 'kW', 'kilowatts': 'kW',
    'mw': 'MW', 'megawatt': 'MW', 'megawatts': 'MW',
    
    # Pressure units
    'pa': 'Pa', 'pascal': 'Pa',
    'kpa': 'kPa', 'kilopascal': 'kPa',
    'mpa': 'MPa', 'megapascal': 'MPa',
    'bar': 'bar', 'bars': 'bar',
    'psi': 'psi', 'psig': 'psi',
}


def parse_calculation_inputs(raw_inputs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse and sanitize raw calculation inputs.

    Args:
        raw_inputs: Dictionary of raw input values

    Returns:
        Dict with parsed and sanitized inputs

    Raises:
        InvalidInputError: If parsing fails due to malformed data
    """
    logger.debug("Parsing calculation inputs")

    try:
        parsed_inputs = {}

        for key, value in raw_inputs.items():
            if value is None:
                parsed_inputs[key] = None
                continue

            # Handle different input types
            if isinstance(value, (int, float)):
                parsed_inputs[key] = float(value)
            elif isinstance(value, str):
                parsed_inputs[key] = _parse_string_input(key, value)
            elif isinstance(value, dict):
                parsed_inputs[key] = parse_calculation_inputs(value)  # Recursive
            elif isinstance(value, list):
                parsed_inputs[key] = [_parse_single_value(item) for item in value]
            else:
                parsed_inputs[key] = value

        logger.debug(f"Successfully parsed {len(parsed_inputs)} inputs")
        return parsed_inputs

    except Exception as e:
        logger.error(f"Input parsing failed: {e}")
        raise InvalidInputError(f"Failed to parse calculation inputs: {str(e)}")


def sanitize_numeric_input(
    value: Union[str, int, float], 
    field_name: str,
    min_value: Optional[float] = None,
    max_value: Optional[float] = None,
    allow_negative: bool = True,
) -> float:
    """
    Sanitize and validate a numeric input.

    Args:
        value: Input value to sanitize
        field_name: Name of the field (for error messages)
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        allow_negative: Whether negative values are allowed

    Returns:
        float: Sanitized numeric value

    Raises:
        InvalidInputError: If value is invalid
    """
    logger.debug(f"Sanitizing numeric input for {field_name}: {value}")

    try:
        # Convert to float
        if isinstance(value, str):
            # Remove whitespace and handle units
            value = value.strip()
            numeric_value, unit = _extract_value_and_unit(value)
        else:
            numeric_value = float(value)
            unit = None

        # Validate range
        if not allow_negative and numeric_value < 0:
            raise InvalidInputError(f"{field_name} cannot be negative")

        if min_value is not None and numeric_value < min_value:
            raise InvalidInputError(
                f"{field_name} must be at least {min_value}, got {numeric_value}"
            )

        if max_value is not None and numeric_value > max_value:
            raise InvalidInputError(
                f"{field_name} must be at most {max_value}, got {numeric_value}"
            )

        # Check for reasonable values (avoid extreme numbers)
        if abs(numeric_value) > 1e15:
            raise InvalidInputError(f"{field_name} value is too large: {numeric_value}")

        logger.debug(f"Sanitized {field_name}: {numeric_value}")
        return numeric_value

    except (ValueError, TypeError, InvalidOperation) as e:
        logger.error(f"Failed to sanitize {field_name}: {e}")
        raise InvalidInputError(f"Invalid numeric value for {field_name}: {value}")


def validate_input_ranges(inputs: Dict[str, Any], validation_rules: Dict[str, Dict]) -> None:
    """
    Validate input values against defined ranges and rules.

    Args:
        inputs: Dictionary of input values
        validation_rules: Dictionary of validation rules per field

    Raises:
        InvalidInputError: If any validation fails
    """
    logger.debug("Validating input ranges")

    violations = []

    for field_name, rules in validation_rules.items():
        if field_name not in inputs:
            if rules.get('required', False):
                violations.append(f"Required field '{field_name}' is missing")
            continue

        value = inputs[field_name]
        if value is None:
            if rules.get('required', False):
                violations.append(f"Required field '{field_name}' cannot be None")
            continue

        # Validate numeric ranges
        if 'min_value' in rules and value < rules['min_value']:
            violations.append(
                f"{field_name} ({value}) is below minimum ({rules['min_value']})"
            )

        if 'max_value' in rules and value > rules['max_value']:
            violations.append(
                f"{field_name} ({value}) exceeds maximum ({rules['max_value']})"
            )

        # Validate allowed values
        if 'allowed_values' in rules and value not in rules['allowed_values']:
            violations.append(
                f"{field_name} ({value}) not in allowed values: {rules['allowed_values']}"
            )

        # Validate data type
        if 'data_type' in rules and not isinstance(value, rules['data_type']):
            violations.append(
                f"{field_name} must be of type {rules['data_type'].__name__}, got {type(value).__name__}"
            )

    if violations:
        error_message = "Input validation failed:\n" + "\n".join(f"- {v}" for v in violations)
        logger.error(error_message)
        raise InvalidInputError(error_message)

    logger.debug("All input validations passed")


def _parse_string_input(key: str, value: str) -> Union[float, str]:
    """Parse a string input value."""
    value = value.strip()

    # Try to parse as numeric
    if NUMERIC_PATTERN.match(value):
        try:
            return float(value)
        except ValueError:
            pass

    # Try to parse with units
    match = UNIT_PATTERN.match(value)
    if match:
        numeric_part = match.group(1)
        unit_part = match.group(2)
        
        try:
            numeric_value = float(numeric_part)
            if unit_part:
                # Store unit information for later conversion if needed
                return {"value": numeric_value, "unit": unit_part.lower()}
            return numeric_value
        except ValueError:
            pass

    # Return as string if not numeric
    return value


def _parse_single_value(value: Any) -> Any:
    """Parse a single value (used for list items)."""
    if isinstance(value, str):
        return _parse_string_input("item", value)
    elif isinstance(value, (int, float)):
        return float(value)
    else:
        return value


def _extract_value_and_unit(input_str: str) -> tuple[float, Optional[str]]:
    """Extract numeric value and unit from input string."""
    match = UNIT_PATTERN.match(input_str.strip())
    if match:
        numeric_part = match.group(1)
        unit_part = match.group(2)
        
        try:
            value = float(numeric_part)
            unit = unit_part.lower() if unit_part else None
            return value, unit
        except ValueError:
            raise InvalidInputError(f"Invalid numeric value: {numeric_part}")
    
    # Try direct float conversion
    try:
        return float(input_str), None
    except ValueError:
        raise InvalidInputError(f"Cannot parse numeric value from: {input_str}")


def normalize_unit_name(unit: str) -> str:
    """
    Normalize unit name to standard format.

    Args:
        unit: Unit string to normalize

    Returns:
        str: Normalized unit name
    """
    if not unit:
        return ""

    unit_lower = unit.lower().strip()
    return UNIT_MAPPINGS.get(unit_lower, unit)


def extract_numeric_inputs(inputs: Dict[str, Any]) -> Dict[str, float]:
    """
    Extract only numeric inputs from a mixed input dictionary.

    Args:
        inputs: Mixed input dictionary

    Returns:
        Dict containing only numeric values
    """
    logger.debug("Extracting numeric inputs")

    numeric_inputs = {}

    for key, value in inputs.items():
        try:
            if isinstance(value, (int, float)):
                numeric_inputs[key] = float(value)
            elif isinstance(value, str):
                # Try to extract numeric value
                numeric_value, _ = _extract_value_and_unit(value)
                numeric_inputs[key] = numeric_value
            elif isinstance(value, dict) and "value" in value:
                # Handle unit dictionaries
                numeric_inputs[key] = float(value["value"])
        except (ValueError, InvalidInputError):
            # Skip non-numeric values
            logger.debug(f"Skipping non-numeric input: {key} = {value}")
            continue

    logger.debug(f"Extracted {len(numeric_inputs)} numeric inputs")
    return numeric_inputs


def validate_required_inputs(inputs: Dict[str, Any], required_fields: List[str]) -> None:
    """
    Validate that all required inputs are present and not None.

    Args:
        inputs: Input dictionary
        required_fields: List of required field names

    Raises:
        InvalidInputError: If any required field is missing
    """
    logger.debug(f"Validating {len(required_fields)} required inputs")

    missing_fields = []

    for field in required_fields:
        if field not in inputs:
            missing_fields.append(field)
        elif inputs[field] is None:
            missing_fields.append(f"{field} (None value)")

    if missing_fields:
        error_message = f"Missing required inputs: {', '.join(missing_fields)}"
        logger.error(error_message)
        raise InvalidInputError(error_message)

    logger.debug("All required inputs are present")


def clean_input_dictionary(inputs: Dict[str, Any]) -> Dict[str, Any]:
    """
    Clean input dictionary by removing None values and empty strings.

    Args:
        inputs: Input dictionary to clean

    Returns:
        Dict with cleaned inputs
    """
    logger.debug("Cleaning input dictionary")

    cleaned = {}

    for key, value in inputs.items():
        if value is None:
            continue
        elif isinstance(value, str) and value.strip() == "":
            continue
        elif isinstance(value, dict):
            cleaned_sub = clean_input_dictionary(value)
            if cleaned_sub:  # Only add if not empty
                cleaned[key] = cleaned_sub
        else:
            cleaned[key] = value

    logger.debug(f"Cleaned dictionary: {len(inputs)} -> {len(cleaned)} entries")
    return cleaned
