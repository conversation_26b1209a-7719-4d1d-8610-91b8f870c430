# backend/core/calculations/utils/math_helpers.py
"""
Mathematical Helper Functions.

This module contains mathematical utility functions for interpolation,
curve fitting, iterative solving, and other mathematical operations.
"""

import logging
import math
from typing import List, Tuple, Callable, Optional, Dict, Any
import numpy as np

from core.errors.exceptions import CalculationError

logger = logging.getLogger(__name__)


def interpolate_linear(
    x: float,
    x_points: List[float],
    y_points: List[float],
    extrapolate: bool = False,
) -> float:
    """
    Perform linear interpolation between data points.

    Args:
        x: Value to interpolate at
        x_points: List of x coordinates (must be sorted)
        y_points: List of y coordinates
        extrapolate: Whether to extrapolate beyond data range

    Returns:
        float: Interpolated y value

    Raises:
        CalculationError: If interpolation fails
    """
    logger.debug(f"Linear interpolation at x={x}")

    try:
        if len(x_points) != len(y_points):
            raise CalculationError("x_points and y_points must have same length")

        if len(x_points) < 2:
            raise CalculationError("Need at least 2 points for interpolation")

        # Check if x is within range
        if not extrapolate:
            if x < min(x_points) or x > max(x_points):
                raise CalculationError(f"x={x} is outside interpolation range")

        # Find surrounding points
        for i in range(len(x_points) - 1):
            if x_points[i] <= x <= x_points[i + 1]:
                # Linear interpolation formula
                x1, x2 = x_points[i], x_points[i + 1]
                y1, y2 = y_points[i], y_points[i + 1]
                
                if x2 == x1:  # Avoid division by zero
                    return y1
                
                y = y1 + (y2 - y1) * (x - x1) / (x2 - x1)
                logger.debug(f"Interpolated value: {y}")
                return y

        # Extrapolation if allowed
        if extrapolate:
            if x < x_points[0]:
                # Extrapolate using first two points
                x1, x2 = x_points[0], x_points[1]
                y1, y2 = y_points[0], y_points[1]
            else:
                # Extrapolate using last two points
                x1, x2 = x_points[-2], x_points[-1]
                y1, y2 = y_points[-2], y_points[-1]
            
            if x2 != x1:
                y = y1 + (y2 - y1) * (x - x1) / (x2 - x1)
                logger.debug(f"Extrapolated value: {y}")
                return y

        raise CalculationError(f"Could not interpolate at x={x}")

    except Exception as e:
        logger.error(f"Linear interpolation failed: {e}")
        raise CalculationError(f"Linear interpolation failed: {str(e)}")


def interpolate_bilinear(
    x: float,
    y: float,
    x_points: List[float],
    y_points: List[float],
    z_grid: List[List[float]],
) -> float:
    """
    Perform bilinear interpolation on a 2D grid.

    Args:
        x: X coordinate to interpolate at
        y: Y coordinate to interpolate at
        x_points: List of x coordinates
        y_points: List of y coordinates
        z_grid: 2D grid of z values [y_index][x_index]

    Returns:
        float: Interpolated z value

    Raises:
        CalculationError: If interpolation fails
    """
    logger.debug(f"Bilinear interpolation at ({x}, {y})")

    try:
        # Find surrounding x points
        x_idx = _find_interval_index(x, x_points)
        y_idx = _find_interval_index(y, y_points)

        if x_idx is None or y_idx is None:
            raise CalculationError("Point outside interpolation grid")

        # Get corner points
        x1, x2 = x_points[x_idx], x_points[x_idx + 1]
        y1, y2 = y_points[y_idx], y_points[y_idx + 1]

        # Get corner values
        z11 = z_grid[y_idx][x_idx]
        z12 = z_grid[y_idx + 1][x_idx]
        z21 = z_grid[y_idx][x_idx + 1]
        z22 = z_grid[y_idx + 1][x_idx + 1]

        # Bilinear interpolation
        dx = x2 - x1
        dy = y2 - y1

        if dx == 0 or dy == 0:
            raise CalculationError("Zero interval in interpolation grid")

        # Interpolate in x direction
        z_y1 = z11 + (z21 - z11) * (x - x1) / dx
        z_y2 = z12 + (z22 - z12) * (x - x1) / dx

        # Interpolate in y direction
        z = z_y1 + (z_y2 - z_y1) * (y - y1) / dy

        logger.debug(f"Bilinear interpolated value: {z}")
        return z

    except Exception as e:
        logger.error(f"Bilinear interpolation failed: {e}")
        raise CalculationError(f"Bilinear interpolation failed: {str(e)}")


def solve_iterative(
    func: Callable[[float], float],
    initial_guess: float,
    tolerance: float = 1e-6,
    max_iterations: int = 100,
    method: str = "newton",
) -> float:
    """
    Solve equation iteratively using various methods.

    Args:
        func: Function to find root of (should return 0 at solution)
        initial_guess: Initial guess for solution
        tolerance: Convergence tolerance
        max_iterations: Maximum number of iterations
        method: Solution method ('newton', 'bisection', 'secant')

    Returns:
        float: Solution value

    Raises:
        CalculationError: If solution doesn't converge
    """
    logger.debug(f"Iterative solution using {method} method")

    try:
        if method == "newton":
            return _solve_newton(func, initial_guess, tolerance, max_iterations)
        elif method == "bisection":
            return _solve_bisection(func, initial_guess, tolerance, max_iterations)
        elif method == "secant":
            return _solve_secant(func, initial_guess, tolerance, max_iterations)
        else:
            raise CalculationError(f"Unknown solution method: {method}")

    except Exception as e:
        logger.error(f"Iterative solution failed: {e}")
        raise CalculationError(f"Iterative solution failed: {str(e)}")


def calculate_polynomial(coefficients: List[float], x: float) -> float:
    """
    Calculate polynomial value at given x.

    Args:
        coefficients: Polynomial coefficients [a0, a1, a2, ...] for a0 + a1*x + a2*x^2 + ...
        x: Value to evaluate at

    Returns:
        float: Polynomial value
    """
    logger.debug(f"Calculating polynomial at x={x}")

    try:
        result = 0.0
        for i, coeff in enumerate(coefficients):
            result += coeff * (x ** i)

        logger.debug(f"Polynomial value: {result}")
        return result

    except Exception as e:
        logger.error(f"Polynomial calculation failed: {e}")
        raise CalculationError(f"Polynomial calculation failed: {str(e)}")


def fit_polynomial(
    x_points: List[float],
    y_points: List[float],
    degree: int = 2,
) -> List[float]:
    """
    Fit polynomial to data points using least squares.

    Args:
        x_points: X coordinates
        y_points: Y coordinates
        degree: Polynomial degree

    Returns:
        List of polynomial coefficients

    Raises:
        CalculationError: If fitting fails
    """
    logger.debug(f"Fitting polynomial of degree {degree}")

    try:
        if len(x_points) != len(y_points):
            raise CalculationError("x_points and y_points must have same length")

        if len(x_points) < degree + 1:
            raise CalculationError(f"Need at least {degree + 1} points for degree {degree} polynomial")

        # Use numpy for polynomial fitting
        coefficients = np.polyfit(x_points, y_points, degree)
        # Reverse to match our convention (a0 + a1*x + a2*x^2 + ...)
        coefficients = coefficients[::-1].tolist()

        logger.debug(f"Fitted polynomial coefficients: {coefficients}")
        return coefficients

    except Exception as e:
        logger.error(f"Polynomial fitting failed: {e}")
        raise CalculationError(f"Polynomial fitting failed: {str(e)}")


def calculate_derivative(
    func: Callable[[float], float],
    x: float,
    h: float = 1e-6,
) -> float:
    """
    Calculate numerical derivative using central difference.

    Args:
        func: Function to differentiate
        x: Point to calculate derivative at
        h: Step size

    Returns:
        float: Derivative value
    """
    logger.debug(f"Calculating derivative at x={x}")

    try:
        # Central difference formula
        derivative = (func(x + h) - func(x - h)) / (2 * h)
        
        logger.debug(f"Derivative value: {derivative}")
        return derivative

    except Exception as e:
        logger.error(f"Derivative calculation failed: {e}")
        raise CalculationError(f"Derivative calculation failed: {str(e)}")


def integrate_trapezoidal(
    func: Callable[[float], float],
    a: float,
    b: float,
    n: int = 1000,
) -> float:
    """
    Numerical integration using trapezoidal rule.

    Args:
        func: Function to integrate
        a: Lower limit
        b: Upper limit
        n: Number of intervals

    Returns:
        float: Integral value
    """
    logger.debug(f"Trapezoidal integration from {a} to {b}")

    try:
        if n <= 0:
            raise CalculationError("Number of intervals must be positive")

        h = (b - a) / n
        integral = 0.5 * (func(a) + func(b))

        for i in range(1, n):
            x = a + i * h
            integral += func(x)

        integral *= h

        logger.debug(f"Integral value: {integral}")
        return integral

    except Exception as e:
        logger.error(f"Trapezoidal integration failed: {e}")
        raise CalculationError(f"Trapezoidal integration failed: {str(e)}")


def _find_interval_index(x: float, x_points: List[float]) -> Optional[int]:
    """Find index of interval containing x."""
    for i in range(len(x_points) - 1):
        if x_points[i] <= x <= x_points[i + 1]:
            return i
    return None


def _solve_newton(
    func: Callable[[float], float],
    initial_guess: float,
    tolerance: float,
    max_iterations: int,
) -> float:
    """Solve using Newton's method."""
    x = initial_guess

    for iteration in range(max_iterations):
        f_x = func(x)
        
        if abs(f_x) < tolerance:
            logger.debug(f"Newton converged in {iteration} iterations")
            return x

        # Calculate derivative numerically
        h = 1e-8
        df_dx = (func(x + h) - func(x - h)) / (2 * h)

        if abs(df_dx) < 1e-12:
            raise CalculationError("Derivative too small in Newton's method")

        x_new = x - f_x / df_dx
        x = x_new

    raise CalculationError(f"Newton's method did not converge in {max_iterations} iterations")


def _solve_bisection(
    func: Callable[[float], float],
    initial_guess: float,
    tolerance: float,
    max_iterations: int,
) -> float:
    """Solve using bisection method."""
    # Find initial bracket
    a = initial_guess - 1.0
    b = initial_guess + 1.0

    # Expand bracket until we find sign change
    for _ in range(20):
        if func(a) * func(b) < 0:
            break
        a -= abs(a)
        b += abs(b)
    else:
        raise CalculationError("Could not find initial bracket for bisection")

    # Bisection iterations
    for iteration in range(max_iterations):
        c = (a + b) / 2
        f_c = func(c)

        if abs(f_c) < tolerance or abs(b - a) < tolerance:
            logger.debug(f"Bisection converged in {iteration} iterations")
            return c

        if func(a) * f_c < 0:
            b = c
        else:
            a = c

    raise CalculationError(f"Bisection method did not converge in {max_iterations} iterations")


def _solve_secant(
    func: Callable[[float], float],
    initial_guess: float,
    tolerance: float,
    max_iterations: int,
) -> float:
    """Solve using secant method."""
    x0 = initial_guess
    x1 = initial_guess + 0.1

    for iteration in range(max_iterations):
        f0 = func(x0)
        f1 = func(x1)

        if abs(f1) < tolerance:
            logger.debug(f"Secant converged in {iteration} iterations")
            return x1

        if abs(f1 - f0) < 1e-12:
            raise CalculationError("Function values too close in secant method")

        x_new = x1 - f1 * (x1 - x0) / (f1 - f0)
        x0, x1 = x1, x_new

    raise CalculationError(f"Secant method did not converge in {max_iterations} iterations")
