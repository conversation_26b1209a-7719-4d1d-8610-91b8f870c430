# backend/core/calculations/utils/units_conversion.py
"""
Units Conversion Utilities.

This module provides unit conversion functions specific to engineering
calculations used in heat tracing applications.
"""

import logging
from typing import Dict, Any, Optional

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)

# Conversion factors to base units
TEMPERATURE_CONVERSIONS = {
    "celsius": {"to_kelvin": lambda c: c + 273.15, "from_kelvin": lambda k: k - 273.15},
    "fahrenheit": {"to_kelvin": lambda f: (f - 32) * 5/9 + 273.15, "from_kelvin": lambda k: (k - 273.15) * 9/5 + 32},
    "kelvin": {"to_kelvin": lambda k: k, "from_kelvin": lambda k: k},
    "rankine": {"to_kelvin": lambda r: r * 5/9, "from_kelvin": lambda k: k * 9/5},
}

PRESSURE_CONVERSIONS = {
    "pascal": 1.0,
    "kilopascal": 1000.0,
    "megapascal": 1000000.0,
    "bar": 100000.0,
    "millibar": 100.0,
    "atmosphere": 101325.0,
    "psi": 6894.757,
    "psig": 6894.757,  # Gauge pressure, same conversion factor
    "torr": 133.322,
    "mmhg": 133.322,
    "inhg": 3386.389,
}

POWER_CONVERSIONS = {
    "watt": 1.0,
    "kilowatt": 1000.0,
    "megawatt": 1000000.0,
    "horsepower": 745.7,
    "btu_per_hour": 0.293071,
    "calorie_per_second": 4.184,
    "kcal_per_hour": 1.163,
}

LENGTH_CONVERSIONS = {
    "meter": 1.0,
    "millimeter": 0.001,
    "centimeter": 0.01,
    "kilometer": 1000.0,
    "inch": 0.0254,
    "foot": 0.3048,
    "yard": 0.9144,
    "mile": 1609.344,
}

AREA_CONVERSIONS = {
    "square_meter": 1.0,
    "square_millimeter": 1e-6,
    "square_centimeter": 1e-4,
    "square_kilometer": 1e6,
    "square_inch": 0.00064516,
    "square_foot": 0.092903,
    "square_yard": 0.836127,
    "acre": 4046.86,
    "hectare": 10000.0,
}

VOLUME_CONVERSIONS = {
    "cubic_meter": 1.0,
    "liter": 0.001,
    "milliliter": 1e-6,
    "cubic_centimeter": 1e-6,
    "cubic_inch": 1.6387e-5,
    "cubic_foot": 0.0283168,
    "gallon_us": 0.00378541,
    "gallon_imperial": 0.00454609,
    "barrel": 0.158987,
}

MASS_CONVERSIONS = {
    "kilogram": 1.0,
    "gram": 0.001,
    "pound": 0.453592,
    "ounce": 0.0283495,
    "ton": 1000.0,
    "stone": 6.35029,
}

ENERGY_CONVERSIONS = {
    "joule": 1.0,
    "kilojoule": 1000.0,
    "megajoule": 1000000.0,
    "calorie": 4.184,
    "kilocalorie": 4184.0,
    "btu": 1055.06,
    "kwh": 3600000.0,
    "wh": 3600.0,
}


def convert_temperature(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert temperature between different units.

    Args:
        value: Temperature value to convert
        from_unit: Source unit (celsius, fahrenheit, kelvin, rankine)
        to_unit: Target unit

    Returns:
        float: Converted temperature value

    Raises:
        InvalidInputError: If units are invalid
        CalculationError: If conversion fails
    """
    logger.debug(f"Converting temperature: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in TEMPERATURE_CONVERSIONS:
            raise InvalidInputError(f"Unknown temperature unit: {from_unit}")

        if to_unit not in TEMPERATURE_CONVERSIONS:
            raise InvalidInputError(f"Unknown temperature unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to Kelvin first, then to target unit
        kelvin_value = TEMPERATURE_CONVERSIONS[from_unit]["to_kelvin"](value)
        result = TEMPERATURE_CONVERSIONS[to_unit]["from_kelvin"](kelvin_value)

        logger.debug(f"Temperature conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Temperature conversion failed: {e}")
        raise CalculationError(f"Temperature conversion failed: {str(e)}")


def convert_pressure(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert pressure between different units.

    Args:
        value: Pressure value to convert
        from_unit: Source unit
        to_unit: Target unit

    Returns:
        float: Converted pressure value

    Raises:
        InvalidInputError: If units are invalid
        CalculationError: If conversion fails
    """
    logger.debug(f"Converting pressure: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in PRESSURE_CONVERSIONS:
            raise InvalidInputError(f"Unknown pressure unit: {from_unit}")

        if to_unit not in PRESSURE_CONVERSIONS:
            raise InvalidInputError(f"Unknown pressure unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to Pascal first, then to target unit
        pascal_value = value * PRESSURE_CONVERSIONS[from_unit]
        result = pascal_value / PRESSURE_CONVERSIONS[to_unit]

        logger.debug(f"Pressure conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Pressure conversion failed: {e}")
        raise CalculationError(f"Pressure conversion failed: {str(e)}")


def convert_power(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert power between different units.

    Args:
        value: Power value to convert
        from_unit: Source unit
        to_unit: Target unit

    Returns:
        float: Converted power value

    Raises:
        InvalidInputError: If units are invalid
        CalculationError: If conversion fails
    """
    logger.debug(f"Converting power: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in POWER_CONVERSIONS:
            raise InvalidInputError(f"Unknown power unit: {from_unit}")

        if to_unit not in POWER_CONVERSIONS:
            raise InvalidInputError(f"Unknown power unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to Watts first, then to target unit
        watt_value = value * POWER_CONVERSIONS[from_unit]
        result = watt_value / POWER_CONVERSIONS[to_unit]

        logger.debug(f"Power conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Power conversion failed: {e}")
        raise CalculationError(f"Power conversion failed: {str(e)}")


def convert_length(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert length between different units.

    Args:
        value: Length value to convert
        from_unit: Source unit
        to_unit: Target unit

    Returns:
        float: Converted length value

    Raises:
        InvalidInputError: If units are invalid
        CalculationError: If conversion fails
    """
    logger.debug(f"Converting length: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in LENGTH_CONVERSIONS:
            raise InvalidInputError(f"Unknown length unit: {from_unit}")

        if to_unit not in LENGTH_CONVERSIONS:
            raise InvalidInputError(f"Unknown length unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to meters first, then to target unit
        meter_value = value * LENGTH_CONVERSIONS[from_unit]
        result = meter_value / LENGTH_CONVERSIONS[to_unit]

        logger.debug(f"Length conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Length conversion failed: {e}")
        raise CalculationError(f"Length conversion failed: {str(e)}")


def convert_area(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert area between different units.

    Args:
        value: Area value to convert
        from_unit: Source unit
        to_unit: Target unit

    Returns:
        float: Converted area value

    Raises:
        InvalidInputError: If units are invalid
        CalculationError: If conversion fails
    """
    logger.debug(f"Converting area: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in AREA_CONVERSIONS:
            raise InvalidInputError(f"Unknown area unit: {from_unit}")

        if to_unit not in AREA_CONVERSIONS:
            raise InvalidInputError(f"Unknown area unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to square meters first, then to target unit
        sqm_value = value * AREA_CONVERSIONS[from_unit]
        result = sqm_value / AREA_CONVERSIONS[to_unit]

        logger.debug(f"Area conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Area conversion failed: {e}")
        raise CalculationError(f"Area conversion failed: {str(e)}")


def convert_volume(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert volume between different units.

    Args:
        value: Volume value to convert
        from_unit: Source unit
        to_unit: Target unit

    Returns:
        float: Converted volume value
    """
    logger.debug(f"Converting volume: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in VOLUME_CONVERSIONS:
            raise InvalidInputError(f"Unknown volume unit: {from_unit}")

        if to_unit not in VOLUME_CONVERSIONS:
            raise InvalidInputError(f"Unknown volume unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to cubic meters first, then to target unit
        cbm_value = value * VOLUME_CONVERSIONS[from_unit]
        result = cbm_value / VOLUME_CONVERSIONS[to_unit]

        logger.debug(f"Volume conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Volume conversion failed: {e}")
        raise CalculationError(f"Volume conversion failed: {str(e)}")


def convert_mass(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert mass between different units.

    Args:
        value: Mass value to convert
        from_unit: Source unit
        to_unit: Target unit

    Returns:
        float: Converted mass value
    """
    logger.debug(f"Converting mass: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in MASS_CONVERSIONS:
            raise InvalidInputError(f"Unknown mass unit: {from_unit}")

        if to_unit not in MASS_CONVERSIONS:
            raise InvalidInputError(f"Unknown mass unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to kilograms first, then to target unit
        kg_value = value * MASS_CONVERSIONS[from_unit]
        result = kg_value / MASS_CONVERSIONS[to_unit]

        logger.debug(f"Mass conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Mass conversion failed: {e}")
        raise CalculationError(f"Mass conversion failed: {str(e)}")


def convert_energy(
    value: float,
    from_unit: str,
    to_unit: str,
) -> float:
    """
    Convert energy between different units.

    Args:
        value: Energy value to convert
        from_unit: Source unit
        to_unit: Target unit

    Returns:
        float: Converted energy value
    """
    logger.debug(f"Converting energy: {value} {from_unit} to {to_unit}")

    try:
        from_unit = from_unit.lower()
        to_unit = to_unit.lower()

        if from_unit not in ENERGY_CONVERSIONS:
            raise InvalidInputError(f"Unknown energy unit: {from_unit}")

        if to_unit not in ENERGY_CONVERSIONS:
            raise InvalidInputError(f"Unknown energy unit: {to_unit}")

        if from_unit == to_unit:
            return value

        # Convert to joules first, then to target unit
        joule_value = value * ENERGY_CONVERSIONS[from_unit]
        result = joule_value / ENERGY_CONVERSIONS[to_unit]

        logger.debug(f"Energy conversion result: {result} {to_unit}")
        return result

    except Exception as e:
        logger.error(f"Energy conversion failed: {e}")
        raise CalculationError(f"Energy conversion failed: {str(e)}")


def get_supported_units(unit_type: str) -> list[str]:
    """
    Get list of supported units for a given type.

    Args:
        unit_type: Type of unit (temperature, pressure, power, etc.)

    Returns:
        List of supported unit names
    """
    unit_mappings = {
        "temperature": list(TEMPERATURE_CONVERSIONS.keys()),
        "pressure": list(PRESSURE_CONVERSIONS.keys()),
        "power": list(POWER_CONVERSIONS.keys()),
        "length": list(LENGTH_CONVERSIONS.keys()),
        "area": list(AREA_CONVERSIONS.keys()),
        "volume": list(VOLUME_CONVERSIONS.keys()),
        "mass": list(MASS_CONVERSIONS.keys()),
        "energy": list(ENERGY_CONVERSIONS.keys()),
    }

    return unit_mappings.get(unit_type.lower(), [])


def validate_unit(unit: str, unit_type: str) -> bool:
    """
    Validate if a unit is supported for the given type.

    Args:
        unit: Unit name to validate
        unit_type: Type of unit

    Returns:
        bool: True if unit is supported
    """
    supported_units = get_supported_units(unit_type)
    return unit.lower() in supported_units
