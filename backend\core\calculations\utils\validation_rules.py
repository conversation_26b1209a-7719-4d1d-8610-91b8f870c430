# backend/core/calculations/utils/validation_rules.py
"""
Validation Rules for Calculations.

This module contains reusable validation functions for calculation inputs
and outputs, including cable selection validation and range checks.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple

from core.errors.exceptions import InvalidInputError

logger = logging.getLogger(__name__)

# Validation ranges for common parameters
VALIDATION_RANGES = {
    "temperature": {
        "min_celsius": -273.15,  # Absolute zero
        "max_celsius": 2000.0,   # Reasonable upper limit
        "typical_min": -50.0,    # Typical engineering range
        "typical_max": 500.0,
    },
    "pressure": {
        "min_pascal": 0.0,       # Vacuum
        "max_pascal": 1e9,       # 1 GPa
        "typical_min": 1000.0,   # Typical engineering range
        "typical_max": 1e7,      # 100 bar
    },
    "power": {
        "min_watts": 0.0,
        "max_watts": 1e9,        # 1 GW
        "typical_min": 1.0,      # 1 W
        "typical_max": 1e6,      # 1 MW
    },
    "length": {
        "min_meters": 0.0,
        "max_meters": 1e6,       # 1000 km
        "typical_min": 0.001,    # 1 mm
        "typical_max": 10000.0,  # 10 km
    },
    "current": {
        "min_amperes": 0.0,
        "max_amperes": 10000.0,  # 10 kA
        "typical_min": 0.1,      # 100 mA
        "typical_max": 1000.0,   # 1 kA
    },
    "voltage": {
        "min_volts": 0.0,
        "max_volts": 100000.0,   # 100 kV
        "typical_min": 12.0,     # Low voltage
        "typical_max": 1000.0,   # Medium voltage
    },
}

# Cable selection validation criteria
CABLE_VALIDATION_CRITERIA = {
    "temperature_limits": {
        "min_operating": -40.0,   # °C
        "max_operating": 250.0,   # °C
        "max_exposure": 300.0,    # °C
    },
    "power_limits": {
        "min_power_per_meter": 1.0,    # W/m
        "max_power_per_meter": 100.0,  # W/m
    },
    "length_limits": {
        "min_length": 1.0,       # m
        "max_length": 1000.0,    # m
    },
    "voltage_limits": {
        "min_voltage": 12.0,     # V
        "max_voltage": 1000.0,   # V
    },
}

# Hazardous area classifications
HAZARDOUS_AREA_ZONES = {
    "Zone 0": {
        "description": "Explosive atmosphere present continuously",
        "required_protection": ["ia", "ma"],
        "max_surface_temp": {"T1": 450, "T2": 300, "T3": 200, "T4": 135, "T5": 100, "T6": 85},
    },
    "Zone 1": {
        "description": "Explosive atmosphere likely in normal operation",
        "required_protection": ["ia", "ib", "ma", "mb", "d", "e", "p"],
        "max_surface_temp": {"T1": 450, "T2": 300, "T3": 200, "T4": 135, "T5": 100, "T6": 85},
    },
    "Zone 2": {
        "description": "Explosive atmosphere unlikely to occur",
        "required_protection": ["ia", "ib", "ic", "ma", "mb", "mc", "d", "e", "p", "n"],
        "max_surface_temp": {"T1": 450, "T2": 300, "T3": 200, "T4": 135, "T5": 100, "T6": 85},
    },
}


def validate_cable_selection(
    cable_specs: Dict[str, Any],
    operating_conditions: Dict[str, Any],
    hazardous_area: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Perform comprehensive validation of cable selection.

    Args:
        cable_specs: Cable specifications
        operating_conditions: Operating conditions
        hazardous_area: Hazardous area requirements (optional)

    Returns:
        Dict with validation results

    Raises:
        InvalidInputError: If validation fails
    """
    logger.debug("Validating cable selection")

    validation_result = {
        "is_valid": True,
        "violations": [],
        "warnings": [],
        "recommendations": [],
    }

    try:
        # Temperature validation
        _validate_cable_temperature_limits(
            cable_specs, operating_conditions, validation_result
        )

        # Power validation
        _validate_cable_power_limits(
            cable_specs, operating_conditions, validation_result
        )

        # Length validation
        _validate_cable_length_limits(
            cable_specs, operating_conditions, validation_result
        )

        # Voltage validation
        _validate_cable_voltage_limits(
            cable_specs, operating_conditions, validation_result
        )

        # Hazardous area validation
        if hazardous_area:
            _validate_hazardous_area_compliance(
                cable_specs, hazardous_area, validation_result
            )

        # Overall validation status
        validation_result["is_valid"] = len(validation_result["violations"]) == 0

        logger.debug(f"Cable validation completed: valid={validation_result['is_valid']}")
        return validation_result

    except Exception as e:
        logger.error(f"Cable validation failed: {e}")
        raise InvalidInputError(f"Cable validation failed: {str(e)}")


def validate_temperature_range(
    temperature: float,
    field_name: str = "temperature",
    allow_extreme: bool = False,
) -> None:
    """
    Validate temperature value against reasonable ranges.

    Args:
        temperature: Temperature value in Celsius
        field_name: Name of the temperature field
        allow_extreme: Whether to allow extreme temperatures

    Raises:
        InvalidInputError: If temperature is out of range
    """
    logger.debug(f"Validating temperature: {temperature}°C")

    ranges = VALIDATION_RANGES["temperature"]

    # Check absolute limits
    if temperature < ranges["min_celsius"]:
        raise InvalidInputError(
            f"{field_name} ({temperature}°C) is below absolute zero"
        )

    if temperature > ranges["max_celsius"]:
        raise InvalidInputError(
            f"{field_name} ({temperature}°C) exceeds maximum limit ({ranges['max_celsius']}°C)"
        )

    # Check typical ranges if not allowing extreme values
    if not allow_extreme:
        if temperature < ranges["typical_min"]:
            raise InvalidInputError(
                f"{field_name} ({temperature}°C) is below typical minimum ({ranges['typical_min']}°C)"
            )

        if temperature > ranges["typical_max"]:
            raise InvalidInputError(
                f"{field_name} ({temperature}°C) exceeds typical maximum ({ranges['typical_max']}°C)"
            )

    logger.debug(f"Temperature validation passed: {temperature}°C")


def validate_pressure_range(
    pressure: float,
    field_name: str = "pressure",
    allow_extreme: bool = False,
) -> None:
    """
    Validate pressure value against reasonable ranges.

    Args:
        pressure: Pressure value in Pascal
        field_name: Name of the pressure field
        allow_extreme: Whether to allow extreme pressures

    Raises:
        InvalidInputError: If pressure is out of range
    """
    logger.debug(f"Validating pressure: {pressure} Pa")

    ranges = VALIDATION_RANGES["pressure"]

    # Check absolute limits
    if pressure < ranges["min_pascal"]:
        raise InvalidInputError(
            f"{field_name} ({pressure} Pa) cannot be negative"
        )

    if pressure > ranges["max_pascal"]:
        raise InvalidInputError(
            f"{field_name} ({pressure} Pa) exceeds maximum limit ({ranges['max_pascal']} Pa)"
        )

    # Check typical ranges if not allowing extreme values
    if not allow_extreme:
        if pressure > ranges["typical_max"]:
            raise InvalidInputError(
                f"{field_name} ({pressure} Pa) exceeds typical maximum ({ranges['typical_max']} Pa)"
            )

    logger.debug(f"Pressure validation passed: {pressure} Pa")


def validate_power_range(
    power: float,
    field_name: str = "power",
    allow_extreme: bool = False,
) -> None:
    """
    Validate power value against reasonable ranges.

    Args:
        power: Power value in Watts
        field_name: Name of the power field
        allow_extreme: Whether to allow extreme power values

    Raises:
        InvalidInputError: If power is out of range
    """
    logger.debug(f"Validating power: {power} W")

    ranges = VALIDATION_RANGES["power"]

    # Check absolute limits
    if power < ranges["min_watts"]:
        raise InvalidInputError(
            f"{field_name} ({power} W) cannot be negative"
        )

    if power > ranges["max_watts"]:
        raise InvalidInputError(
            f"{field_name} ({power} W) exceeds maximum limit ({ranges['max_watts']} W)"
        )

    # Check typical ranges if not allowing extreme values
    if not allow_extreme:
        if power > ranges["typical_max"]:
            raise InvalidInputError(
                f"{field_name} ({power} W) exceeds typical maximum ({ranges['typical_max']} W)"
            )

    logger.debug(f"Power validation passed: {power} W")


def validate_electrical_parameters(
    voltage: float,
    current: float,
    power: Optional[float] = None,
) -> Dict[str, Any]:
    """
    Validate electrical parameters for consistency.

    Args:
        voltage: Voltage in Volts
        current: Current in Amperes
        power: Power in Watts (optional)

    Returns:
        Dict with validation results
    """
    logger.debug(f"Validating electrical parameters: {voltage}V, {current}A")

    validation_result = {
        "is_valid": True,
        "violations": [],
        "warnings": [],
        "calculated_power": voltage * current,
    }

    try:
        # Validate individual parameters
        validate_range_value(voltage, "voltage", VALIDATION_RANGES["voltage"])
        validate_range_value(current, "current", VALIDATION_RANGES["current"])

        # Calculate power and check consistency
        calculated_power = voltage * current

        if power is not None:
            power_difference = abs(power - calculated_power)
            power_tolerance = max(calculated_power * 0.05, 1.0)  # 5% or 1W

            if power_difference > power_tolerance:
                validation_result["violations"].append(
                    f"Power inconsistency: given {power}W, calculated {calculated_power:.1f}W"
                )
                validation_result["is_valid"] = False

        # Check for reasonable power levels
        if calculated_power > VALIDATION_RANGES["power"]["typical_max"]:
            validation_result["warnings"].append(
                f"High power level: {calculated_power:.1f}W"
            )

        validation_result["calculated_power"] = calculated_power

        logger.debug(f"Electrical validation completed: valid={validation_result['is_valid']}")
        return validation_result

    except InvalidInputError as e:
        validation_result["violations"].append(str(e))
        validation_result["is_valid"] = False
        return validation_result


def validate_range_value(
    value: float,
    parameter_name: str,
    ranges: Dict[str, float],
) -> None:
    """
    Validate a value against defined ranges.

    Args:
        value: Value to validate
        parameter_name: Name of the parameter
        ranges: Dictionary with min/max ranges

    Raises:
        InvalidInputError: If value is out of range
    """
    min_key = f"min_{parameter_name.lower()}" if f"min_{parameter_name.lower()}" in ranges else "min"
    max_key = f"max_{parameter_name.lower()}" if f"max_{parameter_name.lower()}" in ranges else "max"

    if min_key in ranges and value < ranges[min_key]:
        raise InvalidInputError(
            f"{parameter_name} ({value}) is below minimum ({ranges[min_key]})"
        )

    if max_key in ranges and value > ranges[max_key]:
        raise InvalidInputError(
            f"{parameter_name} ({value}) exceeds maximum ({ranges[max_key]})"
        )


def _validate_cable_temperature_limits(
    cable_specs: Dict[str, Any],
    operating_conditions: Dict[str, Any],
    validation_result: Dict[str, Any],
) -> None:
    """Validate cable temperature limits."""
    operating_temp = operating_conditions.get("temperature", 20.0)
    max_operating_temp = cable_specs.get("max_operating_temperature", 85.0)
    max_exposure_temp = cable_specs.get("max_exposure_temperature", 105.0)

    if operating_temp > max_operating_temp:
        validation_result["violations"].append(
            f"Operating temperature ({operating_temp}°C) exceeds cable limit ({max_operating_temp}°C)"
        )

    if operating_temp > max_exposure_temp:
        validation_result["violations"].append(
            f"Operating temperature ({operating_temp}°C) exceeds exposure limit ({max_exposure_temp}°C)"
        )

    # Warning for temperatures close to limits
    if operating_temp > max_operating_temp * 0.9:
        validation_result["warnings"].append(
            f"Operating temperature ({operating_temp}°C) is close to cable limit"
        )


def _validate_cable_power_limits(
    cable_specs: Dict[str, Any],
    operating_conditions: Dict[str, Any],
    validation_result: Dict[str, Any],
) -> None:
    """Validate cable power limits."""
    required_power = operating_conditions.get("power_per_meter", 0.0)
    max_power = cable_specs.get("max_power_per_meter", 50.0)

    if required_power > max_power:
        validation_result["violations"].append(
            f"Required power ({required_power} W/m) exceeds cable limit ({max_power} W/m)"
        )

    # Warning for high power usage
    if required_power > max_power * 0.8:
        validation_result["warnings"].append(
            f"Power usage ({required_power} W/m) is high relative to cable capacity"
        )


def _validate_cable_length_limits(
    cable_specs: Dict[str, Any],
    operating_conditions: Dict[str, Any],
    validation_result: Dict[str, Any],
) -> None:
    """Validate cable length limits."""
    cable_length = operating_conditions.get("cable_length", 0.0)
    max_length = cable_specs.get("max_circuit_length", 500.0)

    if cable_length > max_length:
        validation_result["violations"].append(
            f"Cable length ({cable_length} m) exceeds maximum ({max_length} m)"
        )


def _validate_cable_voltage_limits(
    cable_specs: Dict[str, Any],
    operating_conditions: Dict[str, Any],
    validation_result: Dict[str, Any],
) -> None:
    """Validate cable voltage limits."""
    operating_voltage = operating_conditions.get("voltage", 230.0)
    rated_voltage = cable_specs.get("rated_voltage", 250.0)

    if operating_voltage > rated_voltage:
        validation_result["violations"].append(
            f"Operating voltage ({operating_voltage}V) exceeds cable rating ({rated_voltage}V)"
        )


def _validate_hazardous_area_compliance(
    cable_specs: Dict[str, Any],
    hazardous_area: Dict[str, Any],
    validation_result: Dict[str, Any],
) -> None:
    """Validate hazardous area compliance."""
    zone = hazardous_area.get("zone", "Zone 2")
    temp_class = hazardous_area.get("temperature_class", "T4")
    
    if zone not in HAZARDOUS_AREA_ZONES:
        validation_result["violations"].append(f"Unknown hazardous area zone: {zone}")
        return

    zone_requirements = HAZARDOUS_AREA_ZONES[zone]
    
    # Check temperature class
    if temp_class in zone_requirements["max_surface_temp"]:
        max_temp = zone_requirements["max_surface_temp"][temp_class]
        cable_surface_temp = cable_specs.get("surface_temperature", 85.0)
        
        if cable_surface_temp > max_temp:
            validation_result["violations"].append(
                f"Cable surface temperature ({cable_surface_temp}°C) exceeds {temp_class} limit ({max_temp}°C)"
            )

    # Check protection type
    cable_protection = cable_specs.get("protection_type", "")
    required_protection = zone_requirements["required_protection"]
    
    if cable_protection and cable_protection not in required_protection:
        validation_result["violations"].append(
            f"Cable protection type '{cable_protection}' not suitable for {zone}"
        )
