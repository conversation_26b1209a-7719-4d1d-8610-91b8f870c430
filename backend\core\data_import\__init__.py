# backend/core/data_import/__init__.py
"""
Data Import Layer - File parsing and data import functionality.

This module provides comprehensive data import capabilities for the Ultimate
Electrical Designer, supporting multiple file formats and validation.

The data import layer is organized into specialized sub-packages:
- parsers: File format parsers (XLSX, JSON, CSV)
- validators: Data validation against schemas
- mappers: Data mapping to ORM models
- Global and project-specific import handling
- Transactional operations with rollback capability

All import operations follow strict validation patterns and provide
detailed error reporting and progress tracking.
"""

from .import_service import ImportService
from .global_importer import GlobalImporter
from .project_importer import ProjectImporter

__all__ = [
    "ImportService",
    "GlobalImporter", 
    "ProjectImporter",
]
