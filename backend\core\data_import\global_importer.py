# backend/core/data_import/global_importer.py
"""
Global Data Importer.

This module handles importing global catalog data such as cables,
materials, and standards that are shared across all projects.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import tempfile
import shutil

from .parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON>NParser, CSVParser
from .validators import ImportDataValidator
from .mappers import CatalogDataMapper
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class GlobalImporter:
    """
    Handles importing global catalog data with validation and error handling.
    """

    def __init__(self):
        """Initialize the global importer."""
        self.xlsx_parser = XLSXParser()
        self.json_parser = JSONParser()
        self.csv_parser = CSVParser()
        self.validator = ImportDataValidator()
        self.mapper = CatalogDataMapper()
        
        self.supported_formats = {
            '.xlsx': self.xlsx_parser,
            '.xls': self.xlsx_parser,
            '.json': self.json_parser,
            '.jsonl': self.json_parser,
            '.csv': self.csv_parser,
            '.tsv': self.csv_parser,
            '.txt': self.csv_parser,
        }
        
        logger.debug("GlobalImporter initialized")

    def import_cable_catalog(
        self,
        file_path: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
    ) -> Dict[str, Any]:
        """
        Import cable catalog data from file.

        Args:
            file_path: Path to the import file
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing

        Returns:
            Dict with import results

        Raises:
            InvalidInputError: If file or data is invalid
            CalculationError: If import process fails
        """
        logger.info(f"Importing cable catalog from: {file_path}")

        try:
            # Parse file
            parsed_data = self._parse_file(file_path, sheet_name)
            
            # Extract data
            if isinstance(parsed_data.get("data"), list):
                raw_data = parsed_data["data"]
            elif "sheets_data" in parsed_data:
                # For Excel files, use first sheet or specified sheet
                sheet_key = sheet_name or list(parsed_data["sheets_data"].keys())[0]
                raw_data = parsed_data["sheets_data"][sheet_key]["data"]
            else:
                raise CalculationError("No data found in file")

            logger.info(f"Parsed {len(raw_data)} cable records")

            # Map data
            mapped_data = self.mapper.map_cable_catalog_data(raw_data, field_mapping)

            # Validate data
            validation_result = self._validate_catalog_data(mapped_data, "cables")

            if not validation_result.is_valid:
                logger.error(f"Cable catalog validation failed: {validation_result.error_count} errors")
                if not validate_only:
                    raise CalculationError(f"Validation failed: {validation_result.error_count} errors")

            result = {
                "import_type": "cable_catalog",
                "file_path": file_path,
                "total_records": len(raw_data),
                "mapped_records": len(mapped_data),
                "validation": validation_result.to_dict(),
                "data": mapped_data if validate_only else None,
                "success": validation_result.is_valid,
            }

            if not validate_only and validation_result.is_valid:
                # TODO: Save to database
                logger.info(f"Cable catalog import completed: {len(mapped_data)} records")
                result["imported_records"] = len(mapped_data)

            return result

        except Exception as e:
            logger.error(f"Cable catalog import failed: {e}", exc_info=True)
            raise CalculationError(f"Cable catalog import failed: {str(e)}")

    def import_material_catalog(
        self,
        file_path: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
    ) -> Dict[str, Any]:
        """
        Import material catalog data from file.

        Args:
            file_path: Path to the import file
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing

        Returns:
            Dict with import results
        """
        logger.info(f"Importing material catalog from: {file_path}")

        try:
            # Parse file
            parsed_data = self._parse_file(file_path, sheet_name)
            
            # Extract data
            if isinstance(parsed_data.get("data"), list):
                raw_data = parsed_data["data"]
            elif "sheets_data" in parsed_data:
                sheet_key = sheet_name or list(parsed_data["sheets_data"].keys())[0]
                raw_data = parsed_data["sheets_data"][sheet_key]["data"]
            else:
                raise CalculationError("No data found in file")

            logger.info(f"Parsed {len(raw_data)} material records")

            # Map data
            mapped_data = self.mapper.map_material_catalog_data(raw_data, field_mapping)

            # Validate data
            validation_result = self._validate_catalog_data(mapped_data, "materials")

            if not validation_result.is_valid:
                logger.error(f"Material catalog validation failed: {validation_result.error_count} errors")
                if not validate_only:
                    raise CalculationError(f"Validation failed: {validation_result.error_count} errors")

            result = {
                "import_type": "material_catalog",
                "file_path": file_path,
                "total_records": len(raw_data),
                "mapped_records": len(mapped_data),
                "validation": validation_result.to_dict(),
                "data": mapped_data if validate_only else None,
                "success": validation_result.is_valid,
            }

            if not validate_only and validation_result.is_valid:
                # TODO: Save to database
                logger.info(f"Material catalog import completed: {len(mapped_data)} records")
                result["imported_records"] = len(mapped_data)

            return result

        except Exception as e:
            logger.error(f"Material catalog import failed: {e}", exc_info=True)
            raise CalculationError(f"Material catalog import failed: {str(e)}")

    def import_standards_catalog(
        self,
        file_path: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
    ) -> Dict[str, Any]:
        """
        Import standards catalog data from file.

        Args:
            file_path: Path to the import file
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing

        Returns:
            Dict with import results
        """
        logger.info(f"Importing standards catalog from: {file_path}")

        try:
            # Parse file
            parsed_data = self._parse_file(file_path, sheet_name)
            
            # Extract data
            if isinstance(parsed_data.get("data"), list):
                raw_data = parsed_data["data"]
            elif "sheets_data" in parsed_data:
                sheet_key = sheet_name or list(parsed_data["sheets_data"].keys())[0]
                raw_data = parsed_data["sheets_data"][sheet_key]["data"]
            else:
                raise CalculationError("No data found in file")

            logger.info(f"Parsed {len(raw_data)} standards records")

            # Map data
            mapped_data = self.mapper.map_standards_data(raw_data, field_mapping)

            # Validate data
            validation_result = self._validate_catalog_data(mapped_data, "standards")

            if not validation_result.is_valid:
                logger.error(f"Standards catalog validation failed: {validation_result.error_count} errors")
                if not validate_only:
                    raise CalculationError(f"Validation failed: {validation_result.error_count} errors")

            result = {
                "import_type": "standards_catalog",
                "file_path": file_path,
                "total_records": len(raw_data),
                "mapped_records": len(mapped_data),
                "validation": validation_result.to_dict(),
                "data": mapped_data if validate_only else None,
                "success": validation_result.is_valid,
            }

            if not validate_only and validation_result.is_valid:
                # TODO: Save to database
                logger.info(f"Standards catalog import completed: {len(mapped_data)} records")
                result["imported_records"] = len(mapped_data)

            return result

        except Exception as e:
            logger.error(f"Standards catalog import failed: {e}", exc_info=True)
            raise CalculationError(f"Standards catalog import failed: {str(e)}")

    def get_import_template(self, catalog_type: str, format: str = "xlsx") -> str:
        """
        Generate import template file for specified catalog type.

        Args:
            catalog_type: Type of catalog (cables, materials, standards)
            format: File format (xlsx, csv, json)

        Returns:
            Path to generated template file

        Raises:
            InvalidInputError: If catalog type or format is invalid
        """
        logger.info(f"Generating {format} template for {catalog_type}")

        try:
            # Get mapping template
            mapping_template = self.mapper.create_catalog_mapping_template(catalog_type)
            
            # Remove metadata
            if "_metadata" in mapping_template:
                del mapping_template["_metadata"]

            # Create template file
            temp_dir = tempfile.mkdtemp()
            template_path = Path(temp_dir) / f"{catalog_type}_import_template.{format}"

            if format.lower() == "xlsx":
                self._create_xlsx_template(template_path, mapping_template, catalog_type)
            elif format.lower() == "csv":
                self._create_csv_template(template_path, mapping_template)
            elif format.lower() == "json":
                self._create_json_template(template_path, mapping_template, catalog_type)
            else:
                raise InvalidInputError(f"Unsupported template format: {format}")

            logger.info(f"Template created: {template_path}")
            return str(template_path)

        except Exception as e:
            logger.error(f"Template generation failed: {e}")
            raise CalculationError(f"Template generation failed: {str(e)}")

    def _parse_file(self, file_path: str, sheet_name: Optional[str] = None) -> Dict[str, Any]:
        """Parse file using appropriate parser."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise InvalidInputError(f"File not found: {file_path}")

        file_ext = file_path.suffix.lower()
        
        if file_ext not in self.supported_formats:
            raise InvalidInputError(f"Unsupported file format: {file_ext}")

        parser = self.supported_formats[file_ext]
        
        if file_ext in ['.xlsx', '.xls']:
            return parser.parse_file(file_path, sheet_name)
        elif file_ext in ['.json', '.jsonl']:
            return parser.parse_file(file_path)
        else:  # CSV and variants
            return parser.parse_file(file_path)

    def _validate_catalog_data(self, data: List[Dict[str, Any]], catalog_type: str):
        """Validate catalog data."""
        # For now, use business rules validation
        # In a real implementation, this would use specific catalog schemas
        return self.validator.validate_business_rules(data, catalog_type)

    def _create_xlsx_template(self, template_path: Path, mapping: Dict[str, str], catalog_type: str):
        """Create Excel template file."""
        import pandas as pd
        
        # Create headers from mapping
        headers = list(mapping.keys())
        
        # Create sample data
        sample_data = {}
        for header in headers:
            sample_data[header] = [f"Sample {header} 1", f"Sample {header} 2"]

        df = pd.DataFrame(sample_data)
        
        with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=catalog_type, index=False)

    def _create_csv_template(self, template_path: Path, mapping: Dict[str, str]):
        """Create CSV template file."""
        import csv
        
        headers = list(mapping.keys())
        
        with open(template_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            # Add sample rows
            writer.writerow([f"Sample {header} 1" for header in headers])
            writer.writerow([f"Sample {header} 2" for header in headers])

    def _create_json_template(self, template_path: Path, mapping: Dict[str, str], catalog_type: str):
        """Create JSON template file."""
        import json
        
        # Create sample data structure
        sample_data = {
            "catalog_type": catalog_type,
            "data": []
        }
        
        # Add sample records
        for i in range(2):
            sample_record = {}
            for key in mapping.keys():
                sample_record[key] = f"Sample {key} {i + 1}"
            sample_data["data"].append(sample_record)

        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2)
