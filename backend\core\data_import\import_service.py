# backend/core/data_import/import_service.py
"""
Import Service.

This module provides the main service interface for data import operations,
orchestrating global and project-specific imports with transaction management.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import tempfile
import shutil
from datetime import datetime

from .global_importer import GlobalImporter
from .project_importer import ProjectImporter
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ImportService:
    """
    Main service for orchestrating data import operations with transaction management.
    """

    def __init__(self):
        """Initialize the import service."""
        self.global_importer = GlobalImporter()
        self.import_history = []
        logger.debug("ImportService initialized")

    def import_global_data(
        self,
        file_path: str,
        catalog_type: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
        transaction_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Import global catalog data.

        Args:
            file_path: Path to the import file
            catalog_type: Type of catalog (cables, materials, standards)
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing
            transaction_id: Transaction ID for tracking (optional)

        Returns:
            Dict with import results

        Raises:
            InvalidInputError: If parameters are invalid
            CalculationError: If import fails
        """
        logger.info(f"Starting global {catalog_type} import from: {file_path}")

        try:
            # Validate catalog type
            valid_catalog_types = ["cables", "materials", "standards"]
            if catalog_type not in valid_catalog_types:
                raise InvalidInputError(f"Invalid catalog type: {catalog_type}. Valid types: {valid_catalog_types}")

            # Create transaction record
            transaction = self._create_transaction_record(
                import_type="global",
                catalog_type=catalog_type,
                file_path=file_path,
                transaction_id=transaction_id,
            )

            try:
                # Perform import based on catalog type
                if catalog_type == "cables":
                    result = self.global_importer.import_cable_catalog(
                        file_path, field_mapping, sheet_name, validate_only
                    )
                elif catalog_type == "materials":
                    result = self.global_importer.import_material_catalog(
                        file_path, field_mapping, sheet_name, validate_only
                    )
                elif catalog_type == "standards":
                    result = self.global_importer.import_standards_catalog(
                        file_path, field_mapping, sheet_name, validate_only
                    )

                # Update transaction record
                transaction["status"] = "completed" if result["success"] else "failed"
                transaction["result"] = result
                transaction["completed_at"] = datetime.utcnow()

                self.import_history.append(transaction)

                logger.info(f"Global {catalog_type} import completed: {result['success']}")
                return result

            except Exception as e:
                transaction["status"] = "failed"
                transaction["error"] = str(e)
                transaction["completed_at"] = datetime.utcnow()
                self.import_history.append(transaction)
                raise

        except Exception as e:
            logger.error(f"Global {catalog_type} import failed: {e}", exc_info=True)
            raise CalculationError(f"Global {catalog_type} import failed: {str(e)}")

    def import_project_data(
        self,
        project_id: str,
        file_path: str,
        data_type: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
        reference_data: Optional[Dict[str, List[Dict[str, Any]]]] = None,
        transaction_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Import project-specific data.

        Args:
            project_id: ID of the target project
            file_path: Path to the import file
            data_type: Type of data (pipes, components, heat_tracing_circuits)
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing
            reference_data: Reference data for validation (optional)
            transaction_id: Transaction ID for tracking (optional)

        Returns:
            Dict with import results

        Raises:
            InvalidInputError: If parameters are invalid
            CalculationError: If import fails
        """
        logger.info(f"Starting project {data_type} import for project {project_id}")

        try:
            # Validate data type
            valid_data_types = ["pipes", "components", "heat_tracing_circuits"]
            if data_type not in valid_data_types:
                raise InvalidInputError(f"Invalid data type: {data_type}. Valid types: {valid_data_types}")

            # Create project importer
            project_importer = ProjectImporter(project_id)

            # Create transaction record
            transaction = self._create_transaction_record(
                import_type="project",
                data_type=data_type,
                file_path=file_path,
                project_id=project_id,
                transaction_id=transaction_id,
            )

            try:
                # Perform import based on data type
                if data_type == "pipes":
                    result = project_importer.import_pipes_data(
                        file_path, field_mapping, sheet_name, validate_only
                    )
                elif data_type == "components":
                    result = project_importer.import_components_data(
                        file_path, field_mapping, sheet_name, validate_only
                    )
                elif data_type == "heat_tracing_circuits":
                    result = project_importer.import_heat_tracing_circuits_data(
                        file_path, field_mapping, sheet_name, validate_only, reference_data
                    )

                # Update transaction record
                transaction["status"] = "completed" if result["success"] else "failed"
                transaction["result"] = result
                transaction["completed_at"] = datetime.utcnow()

                self.import_history.append(transaction)

                logger.info(f"Project {data_type} import completed: {result['success']}")
                return result

            except Exception as e:
                transaction["status"] = "failed"
                transaction["error"] = str(e)
                transaction["completed_at"] = datetime.utcnow()
                self.import_history.append(transaction)
                raise

        except Exception as e:
            logger.error(f"Project {data_type} import failed: {e}", exc_info=True)
            raise CalculationError(f"Project {data_type} import failed: {str(e)}")

    def import_multi_sheet_project_data(
        self,
        project_id: str,
        file_path: str,
        sheet_mappings: Dict[str, Dict[str, Any]],
        validate_only: bool = False,
        transaction_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Import data from multiple sheets in a single file.

        Args:
            project_id: ID of the target project
            file_path: Path to the Excel file
            sheet_mappings: Dict mapping sheet names to import configurations
            validate_only: Only validate without importing
            transaction_id: Transaction ID for tracking (optional)

        Returns:
            Dict with combined import results
        """
        logger.info(f"Starting multi-sheet project import for project {project_id}")

        try:
            # Create project importer
            project_importer = ProjectImporter(project_id)

            # Create transaction record
            transaction = self._create_transaction_record(
                import_type="multi_sheet_project",
                file_path=file_path,
                project_id=project_id,
                transaction_id=transaction_id,
                sheet_mappings=sheet_mappings,
            )

            try:
                result = project_importer.import_multi_sheet_project_data(
                    file_path, sheet_mappings, validate_only
                )

                # Update transaction record
                transaction["status"] = "completed" if result["total_success"] else "failed"
                transaction["result"] = result
                transaction["completed_at"] = datetime.utcnow()

                self.import_history.append(transaction)

                logger.info(f"Multi-sheet project import completed: {result['total_success']}")
                return result

            except Exception as e:
                transaction["status"] = "failed"
                transaction["error"] = str(e)
                transaction["completed_at"] = datetime.utcnow()
                self.import_history.append(transaction)
                raise

        except Exception as e:
            logger.error(f"Multi-sheet project import failed: {e}", exc_info=True)
            raise CalculationError(f"Multi-sheet project import failed: {str(e)}")

    def generate_import_template(
        self,
        template_type: str,
        data_type: str,
        format: str = "xlsx",
        project_id: Optional[str] = None,
    ) -> str:
        """
        Generate import template file.

        Args:
            template_type: Type of template (global, project)
            data_type: Type of data or catalog
            format: File format (xlsx, csv, json)
            project_id: Project ID for project templates (optional)

        Returns:
            Path to generated template file

        Raises:
            InvalidInputError: If parameters are invalid
        """
        logger.info(f"Generating {template_type} template for {data_type}")

        try:
            if template_type == "global":
                return self.global_importer.get_import_template(data_type, format)
            elif template_type == "project":
                if not project_id:
                    raise InvalidInputError("Project ID required for project templates")
                project_importer = ProjectImporter(project_id)
                return project_importer.get_project_import_template(data_type, format)
            else:
                raise InvalidInputError(f"Invalid template type: {template_type}")

        except Exception as e:
            logger.error(f"Template generation failed: {e}")
            raise CalculationError(f"Template generation failed: {str(e)}")

    def get_import_history(
        self,
        project_id: Optional[str] = None,
        import_type: Optional[str] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Get import history with optional filtering.

        Args:
            project_id: Filter by project ID (optional)
            import_type: Filter by import type (optional)
            limit: Maximum number of records to return

        Returns:
            List of import history records
        """
        logger.debug(f"Getting import history: project_id={project_id}, type={import_type}")

        filtered_history = self.import_history

        # Apply filters
        if project_id:
            filtered_history = [h for h in filtered_history if h.get("project_id") == project_id]

        if import_type:
            filtered_history = [h for h in filtered_history if h.get("import_type") == import_type]

        # Sort by created_at descending and limit
        filtered_history.sort(key=lambda x: x["created_at"], reverse=True)
        return filtered_history[:limit]

    def get_import_status(self, transaction_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a specific import transaction.

        Args:
            transaction_id: Transaction ID to look up

        Returns:
            Transaction record or None if not found
        """
        logger.debug(f"Getting import status for transaction: {transaction_id}")

        for transaction in self.import_history:
            if transaction["transaction_id"] == transaction_id:
                return transaction

        return None

    def cleanup_temp_files(self, max_age_hours: int = 24) -> int:
        """
        Clean up temporary files older than specified age.

        Args:
            max_age_hours: Maximum age of files to keep in hours

        Returns:
            Number of files cleaned up
        """
        logger.info(f"Cleaning up temporary files older than {max_age_hours} hours")

        try:
            temp_dir = Path(tempfile.gettempdir())
            current_time = datetime.utcnow()
            cleanup_count = 0

            # Look for import-related temp files
            for file_path in temp_dir.glob("*import_template*"):
                try:
                    file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_age.total_seconds() > max_age_hours * 3600:
                        file_path.unlink()
                        cleanup_count += 1
                except Exception as e:
                    logger.warning(f"Failed to clean up file {file_path}: {e}")

            logger.info(f"Cleaned up {cleanup_count} temporary files")
            return cleanup_count

        except Exception as e:
            logger.error(f"Temp file cleanup failed: {e}")
            return 0

    def _create_transaction_record(
        self,
        import_type: str,
        file_path: str,
        transaction_id: Optional[str] = None,
        **kwargs,
    ) -> Dict[str, Any]:
        """Create a transaction record for tracking imports."""
        from uuid import uuid4

        if transaction_id is None:
            transaction_id = str(uuid4())

        transaction = {
            "transaction_id": transaction_id,
            "import_type": import_type,
            "file_path": file_path,
            "status": "in_progress",
            "created_at": datetime.utcnow(),
            "completed_at": None,
            "result": None,
            "error": None,
        }

        # Add additional fields from kwargs
        transaction.update(kwargs)

        return transaction
