# backend/core/data_import/mappers/catalog_data_mapper.py
"""
Catalog Data Mapper.

This module provides mapping functionality for converting imported data
to global catalog ORM models (cables, materials, standards).
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from uuid import uuid4

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class CatalogDataMapper:
    """
    Mapper for converting imported data to global catalog ORM models.
    """

    def __init__(self):
        """Initialize the catalog data mapper."""
        self.mapping_rules = self._load_mapping_rules()
        logger.debug("CatalogDataMapper initialized")

    def map_cable_catalog_data(
        self,
        raw_data: List[Dict[str, Any]],
        field_mapping: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Map raw cable catalog data to Cable model format.

        Args:
            raw_data: Raw imported cable data
            field_mapping: Custom field mapping (optional)

        Returns:
            List of mapped cable data ready for ORM creation

        Raises:
            CalculationError: If mapping fails
        """
        logger.info(f"Mapping {len(raw_data)} cable catalog records")

        try:
            mapped_data = []
            default_mapping = self.mapping_rules["cables"]
            mapping = field_mapping or default_mapping

            for row_idx, record in enumerate(raw_data):
                try:
                    mapped_record = self._map_single_record(
                        record, mapping, "cable", row_idx + 1
                    )
                    
                    # Add catalog-specific fields
                    mapped_record["is_global"] = True
                    mapped_record["created_at"] = datetime.utcnow()
                    mapped_record["updated_at"] = datetime.utcnow()
                    
                    # Generate ID if not provided
                    if "id" not in mapped_record or not mapped_record["id"]:
                        mapped_record["id"] = str(uuid4())

                    # Apply cable-specific transformations
                    mapped_record = self._transform_cable_data(mapped_record)
                    
                    mapped_data.append(mapped_record)

                except Exception as e:
                    logger.error(f"Failed to map cable record {row_idx + 1}: {e}")
                    raise CalculationError(f"Cable mapping failed at row {row_idx + 1}: {str(e)}")

            logger.info(f"Successfully mapped {len(mapped_data)} cable records")
            return mapped_data

        except Exception as e:
            logger.error(f"Cable catalog mapping failed: {e}")
            raise CalculationError(f"Cable catalog mapping failed: {str(e)}")

    def map_material_catalog_data(
        self,
        raw_data: List[Dict[str, Any]],
        field_mapping: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Map raw material catalog data to Material model format.

        Args:
            raw_data: Raw imported material data
            field_mapping: Custom field mapping (optional)

        Returns:
            List of mapped material data ready for ORM creation
        """
        logger.info(f"Mapping {len(raw_data)} material catalog records")

        try:
            mapped_data = []
            default_mapping = self.mapping_rules["materials"]
            mapping = field_mapping or default_mapping

            for row_idx, record in enumerate(raw_data):
                try:
                    mapped_record = self._map_single_record(
                        record, mapping, "material", row_idx + 1
                    )
                    
                    # Add catalog-specific fields
                    mapped_record["is_global"] = True
                    mapped_record["created_at"] = datetime.utcnow()
                    mapped_record["updated_at"] = datetime.utcnow()
                    
                    # Generate ID if not provided
                    if "id" not in mapped_record or not mapped_record["id"]:
                        mapped_record["id"] = str(uuid4())

                    # Apply material-specific transformations
                    mapped_record = self._transform_material_data(mapped_record)
                    
                    mapped_data.append(mapped_record)

                except Exception as e:
                    logger.error(f"Failed to map material record {row_idx + 1}: {e}")
                    raise CalculationError(f"Material mapping failed at row {row_idx + 1}: {str(e)}")

            logger.info(f"Successfully mapped {len(mapped_data)} material records")
            return mapped_data

        except Exception as e:
            logger.error(f"Material catalog mapping failed: {e}")
            raise CalculationError(f"Material catalog mapping failed: {str(e)}")

    def map_standards_data(
        self,
        raw_data: List[Dict[str, Any]],
        field_mapping: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Map raw standards data to Standards model format.

        Args:
            raw_data: Raw imported standards data
            field_mapping: Custom field mapping (optional)

        Returns:
            List of mapped standards data ready for ORM creation
        """
        logger.info(f"Mapping {len(raw_data)} standards records")

        try:
            mapped_data = []
            default_mapping = self.mapping_rules["standards"]
            mapping = field_mapping or default_mapping

            for row_idx, record in enumerate(raw_data):
                try:
                    mapped_record = self._map_single_record(
                        record, mapping, "standard", row_idx + 1
                    )
                    
                    # Add catalog-specific fields
                    mapped_record["is_global"] = True
                    mapped_record["created_at"] = datetime.utcnow()
                    mapped_record["updated_at"] = datetime.utcnow()
                    
                    # Generate ID if not provided
                    if "id" not in mapped_record or not mapped_record["id"]:
                        mapped_record["id"] = str(uuid4())

                    # Apply standards-specific transformations
                    mapped_record = self._transform_standards_data(mapped_record)
                    
                    mapped_data.append(mapped_record)

                except Exception as e:
                    logger.error(f"Failed to map standards record {row_idx + 1}: {e}")
                    raise CalculationError(f"Standards mapping failed at row {row_idx + 1}: {str(e)}")

            logger.info(f"Successfully mapped {len(mapped_data)} standards records")
            return mapped_data

        except Exception as e:
            logger.error(f"Standards catalog mapping failed: {e}")
            raise CalculationError(f"Standards catalog mapping failed: {str(e)}")

    def _map_single_record(
        self,
        record: Dict[str, Any],
        mapping: Dict[str, str],
        entity_type: str,
        row_number: int,
    ) -> Dict[str, Any]:
        """Map a single record using the provided mapping."""
        mapped_record = {}

        for source_field, target_field in mapping.items():
            if source_field in record:
                value = record[source_field]
                
                # Apply data type conversions
                converted_value = self._convert_field_value(
                    value, target_field, entity_type, row_number
                )
                
                mapped_record[target_field] = converted_value
            else:
                # Handle missing fields based on requirements
                if target_field in self._get_required_fields(entity_type):
                    logger.warning(f"Required field '{source_field}' missing in row {row_number}")

        return mapped_record

    def _convert_field_value(
        self,
        value: Any,
        field_name: str,
        entity_type: str,
        row_number: int,
    ) -> Any:
        """Convert field value to appropriate type."""
        if value is None or value == "":
            return None

        try:
            # Numeric fields
            if field_name in [
                "power_per_meter", "voltage", "current_rating", "resistance_per_meter",
                "thermal_conductivity", "density", "specific_heat", "max_temperature",
                "cost_per_meter", "cost_factor"
            ]:
                return float(value) if value is not None else None
            
            # Integer fields
            elif field_name in ["version", "revision", "year"]:
                return int(value) if value is not None else None
            
            # Boolean fields
            elif field_name in [
                "is_self_regulating", "is_constant_wattage", "is_active", 
                "is_hazardous_area_rated", "is_global"
            ]:
                if isinstance(value, bool):
                    return value
                elif isinstance(value, str):
                    return value.lower() in ("true", "yes", "1", "on")
                else:
                    return bool(value)
            
            # String fields (default)
            else:
                return str(value).strip() if value is not None else None

        except (ValueError, TypeError) as e:
            logger.warning(f"Type conversion failed for field '{field_name}' in row {row_number}: {e}")
            return str(value) if value is not None else None

    def _transform_cable_data(self, mapped_record: Dict[str, Any]) -> Dict[str, Any]:
        """Apply cable-specific data transformations."""
        # Normalize cable type
        if "type" in mapped_record and mapped_record["type"]:
            cable_type = mapped_record["type"].lower().replace(" ", "_")
            mapped_record["type"] = cable_type

        # Calculate derived fields
        if ("power_per_meter" in mapped_record and "voltage" in mapped_record and
            mapped_record["power_per_meter"] and mapped_record["voltage"]):
            # Calculate current rating if not provided
            if "current_rating" not in mapped_record or not mapped_record["current_rating"]:
                current = mapped_record["power_per_meter"] / mapped_record["voltage"]
                mapped_record["current_rating"] = current

        # Set default values
        if "status" not in mapped_record:
            mapped_record["status"] = "active"

        if "manufacturer" not in mapped_record:
            mapped_record["manufacturer"] = "Unknown"

        return mapped_record

    def _transform_material_data(self, mapped_record: Dict[str, Any]) -> Dict[str, Any]:
        """Apply material-specific data transformations."""
        # Normalize material name
        if "name" in mapped_record and mapped_record["name"]:
            material_name = mapped_record["name"].lower().replace(" ", "_")
            mapped_record["normalized_name"] = material_name

        # Set material category if not provided
        if "category" not in mapped_record or not mapped_record["category"]:
            # Infer category from name
            name = mapped_record.get("name", "").lower()
            if "steel" in name:
                mapped_record["category"] = "metal"
            elif "insulation" in name or "foam" in name:
                mapped_record["category"] = "insulation"
            elif "plastic" in name or "pvc" in name:
                mapped_record["category"] = "polymer"
            else:
                mapped_record["category"] = "other"

        # Set default values
        if "status" not in mapped_record:
            mapped_record["status"] = "active"

        return mapped_record

    def _transform_standards_data(self, mapped_record: Dict[str, Any]) -> Dict[str, Any]:
        """Apply standards-specific data transformations."""
        # Normalize standard code
        if "code" in mapped_record and mapped_record["code"]:
            standard_code = mapped_record["code"].upper().replace(" ", "_")
            mapped_record["code"] = standard_code

        # Set organization if not provided
        if "organization" not in mapped_record or not mapped_record["organization"]:
            code = mapped_record.get("code", "")
            if code.startswith("IEC"):
                mapped_record["organization"] = "IEC"
            elif code.startswith("IEEE"):
                mapped_record["organization"] = "IEEE"
            elif code.startswith("NEMA"):
                mapped_record["organization"] = "NEMA"
            elif code.startswith("TR"):
                mapped_record["organization"] = "CENELEC"
            else:
                mapped_record["organization"] = "Unknown"

        # Set default values
        if "status" not in mapped_record:
            mapped_record["status"] = "active"

        if "scope" not in mapped_record:
            mapped_record["scope"] = "General"

        return mapped_record

    def _get_required_fields(self, entity_type: str) -> List[str]:
        """Get list of required fields for entity type."""
        required_fields = {
            "cable": ["name", "type", "power_per_meter", "voltage"],
            "material": ["name", "thermal_conductivity"],
            "standard": ["code", "title", "organization"],
        }
        return required_fields.get(entity_type, [])

    def _load_mapping_rules(self) -> Dict[str, Dict[str, str]]:
        """Load default mapping rules for different catalog entity types."""
        return {
            "cables": {
                "cable_name": "name",
                "cable_type": "type",
                "manufacturer": "manufacturer",
                "model": "model",
                "power_per_meter": "power_per_meter",
                "voltage": "voltage",
                "current_rating": "current_rating",
                "resistance_per_meter": "resistance_per_meter",
                "max_temperature": "max_temperature",
                "min_temperature": "min_temperature",
                "is_self_regulating": "is_self_regulating",
                "is_constant_wattage": "is_constant_wattage",
                "hazardous_area_rating": "hazardous_area_rating",
                "cost_per_meter": "cost_per_meter",
                "description": "description",
            },
            "materials": {
                "material_name": "name",
                "material_type": "category",
                "thermal_conductivity": "thermal_conductivity",
                "density": "density",
                "specific_heat": "specific_heat",
                "max_temperature": "max_temperature",
                "cost_factor": "cost_factor",
                "supplier": "supplier",
                "description": "description",
            },
            "standards": {
                "standard_code": "code",
                "standard_title": "title",
                "organization": "organization",
                "version": "version",
                "year": "year",
                "scope": "scope",
                "status": "status",
                "description": "description",
            },
        }

    def create_catalog_mapping_template(self, catalog_type: str) -> Dict[str, str]:
        """
        Create a mapping template for the specified catalog type.

        Args:
            catalog_type: Type of catalog (cables, materials, standards)

        Returns:
            Dict with default mapping template
        """
        logger.debug(f"Creating catalog mapping template for: {catalog_type}")

        if catalog_type not in self.mapping_rules:
            raise InvalidInputError(f"Unknown catalog type: {catalog_type}")

        template = self.mapping_rules[catalog_type].copy()
        
        # Add metadata
        template["_metadata"] = {
            "catalog_type": catalog_type,
            "is_global": True,
            "created_at": datetime.utcnow().isoformat(),
            "description": f"Default mapping template for {catalog_type} catalog",
        }

        return template

    def validate_catalog_mapping(
        self,
        mapping: Dict[str, str],
        available_fields: List[str],
        catalog_type: str,
    ) -> Dict[str, Any]:
        """
        Validate a catalog field mapping.

        Args:
            mapping: Field mapping to validate
            available_fields: List of available source fields
            catalog_type: Type of catalog being mapped

        Returns:
            Dict with validation results
        """
        logger.debug(f"Validating catalog mapping for {catalog_type}")

        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "missing_fields": [],
            "unmapped_fields": [],
        }

        required_fields = self._get_required_fields(catalog_type)
        
        # Check for missing required mappings
        for source_field, target_field in mapping.items():
            if source_field not in available_fields:
                validation_result["missing_fields"].append(source_field)
                if target_field in required_fields:
                    validation_result["errors"].append(
                        f"Required field '{source_field}' not found in source data"
                    )
                    validation_result["is_valid"] = False

        # Check for unmapped available fields
        mapped_fields = set(mapping.keys())
        available_set = set(available_fields)
        unmapped = available_set - mapped_fields
        
        if unmapped:
            validation_result["unmapped_fields"] = list(unmapped)
            validation_result["warnings"].append(
                f"Available fields not mapped: {list(unmapped)}"
            )

        return validation_result
