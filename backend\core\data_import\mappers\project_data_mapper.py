# backend/core/data_import/mappers/project_data_mapper.py
"""
Project Data Mapper.

This module provides mapping functionality for converting imported data
to project-specific ORM models.
"""

import logging
from typing import Dict, List, Any, Optional, Type
from datetime import datetime
from uuid import uuid4

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ProjectDataMapper:
    """
    Mapper for converting imported data to project-specific ORM models.
    """

    def __init__(self, project_id: str):
        """
        Initialize the project data mapper.

        Args:
            project_id: ID of the target project
        """
        self.project_id = project_id
        self.mapping_rules = self._load_mapping_rules()
        logger.debug(f"ProjectDataMapper initialized for project: {project_id}")

    def map_pipes_data(
        self,
        raw_data: List[Dict[str, Any]],
        field_mapping: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Map raw pipe data to Pipe model format.

        Args:
            raw_data: Raw imported pipe data
            field_mapping: Custom field mapping (optional)

        Returns:
            List of mapped pipe data ready for ORM creation

        Raises:
            CalculationError: If mapping fails
        """
        logger.info(f"Mapping {len(raw_data)} pipe records")

        try:
            mapped_data = []
            default_mapping = self.mapping_rules["pipes"]
            
            # Use custom mapping if provided, otherwise use default
            mapping = field_mapping or default_mapping

            for row_idx, record in enumerate(raw_data):
                try:
                    mapped_record = self._map_single_record(
                        record, mapping, "pipe", row_idx + 1
                    )
                    
                    # Add project-specific fields
                    mapped_record["project_id"] = self.project_id
                    mapped_record["created_at"] = datetime.utcnow()
                    mapped_record["updated_at"] = datetime.utcnow()
                    
                    # Generate ID if not provided
                    if "id" not in mapped_record or not mapped_record["id"]:
                        mapped_record["id"] = str(uuid4())

                    # Apply pipe-specific transformations
                    mapped_record = self._transform_pipe_data(mapped_record)
                    
                    mapped_data.append(mapped_record)

                except Exception as e:
                    logger.error(f"Failed to map pipe record {row_idx + 1}: {e}")
                    raise CalculationError(f"Pipe mapping failed at row {row_idx + 1}: {str(e)}")

            logger.info(f"Successfully mapped {len(mapped_data)} pipe records")
            return mapped_data

        except Exception as e:
            logger.error(f"Pipe data mapping failed: {e}")
            raise CalculationError(f"Pipe data mapping failed: {str(e)}")

    def map_components_data(
        self,
        raw_data: List[Dict[str, Any]],
        field_mapping: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Map raw component data to Component model format.

        Args:
            raw_data: Raw imported component data
            field_mapping: Custom field mapping (optional)

        Returns:
            List of mapped component data ready for ORM creation
        """
        logger.info(f"Mapping {len(raw_data)} component records")

        try:
            mapped_data = []
            default_mapping = self.mapping_rules["components"]
            mapping = field_mapping or default_mapping

            for row_idx, record in enumerate(raw_data):
                try:
                    mapped_record = self._map_single_record(
                        record, mapping, "component", row_idx + 1
                    )
                    
                    # Add project-specific fields
                    mapped_record["project_id"] = self.project_id
                    mapped_record["created_at"] = datetime.utcnow()
                    mapped_record["updated_at"] = datetime.utcnow()
                    
                    # Generate ID if not provided
                    if "id" not in mapped_record or not mapped_record["id"]:
                        mapped_record["id"] = str(uuid4())

                    # Apply component-specific transformations
                    mapped_record = self._transform_component_data(mapped_record)
                    
                    mapped_data.append(mapped_record)

                except Exception as e:
                    logger.error(f"Failed to map component record {row_idx + 1}: {e}")
                    raise CalculationError(f"Component mapping failed at row {row_idx + 1}: {str(e)}")

            logger.info(f"Successfully mapped {len(mapped_data)} component records")
            return mapped_data

        except Exception as e:
            logger.error(f"Component data mapping failed: {e}")
            raise CalculationError(f"Component data mapping failed: {str(e)}")

    def map_heat_tracing_circuits_data(
        self,
        raw_data: List[Dict[str, Any]],
        field_mapping: Optional[Dict[str, str]] = None,
    ) -> List[Dict[str, Any]]:
        """
        Map raw heat tracing circuit data to HeatTracingCircuit model format.

        Args:
            raw_data: Raw imported circuit data
            field_mapping: Custom field mapping (optional)

        Returns:
            List of mapped circuit data ready for ORM creation
        """
        logger.info(f"Mapping {len(raw_data)} heat tracing circuit records")

        try:
            mapped_data = []
            default_mapping = self.mapping_rules["heat_tracing_circuits"]
            mapping = field_mapping or default_mapping

            for row_idx, record in enumerate(raw_data):
                try:
                    mapped_record = self._map_single_record(
                        record, mapping, "heat_tracing_circuit", row_idx + 1
                    )
                    
                    # Add project-specific fields
                    mapped_record["project_id"] = self.project_id
                    mapped_record["created_at"] = datetime.utcnow()
                    mapped_record["updated_at"] = datetime.utcnow()
                    
                    # Generate ID if not provided
                    if "id" not in mapped_record or not mapped_record["id"]:
                        mapped_record["id"] = str(uuid4())

                    # Apply circuit-specific transformations
                    mapped_record = self._transform_circuit_data(mapped_record)
                    
                    mapped_data.append(mapped_record)

                except Exception as e:
                    logger.error(f"Failed to map circuit record {row_idx + 1}: {e}")
                    raise CalculationError(f"Circuit mapping failed at row {row_idx + 1}: {str(e)}")

            logger.info(f"Successfully mapped {len(mapped_data)} circuit records")
            return mapped_data

        except Exception as e:
            logger.error(f"Circuit data mapping failed: {e}")
            raise CalculationError(f"Circuit data mapping failed: {str(e)}")

    def _map_single_record(
        self,
        record: Dict[str, Any],
        mapping: Dict[str, str],
        entity_type: str,
        row_number: int,
    ) -> Dict[str, Any]:
        """Map a single record using the provided mapping."""
        mapped_record = {}

        for source_field, target_field in mapping.items():
            if source_field in record:
                value = record[source_field]
                
                # Apply data type conversions
                converted_value = self._convert_field_value(
                    value, target_field, entity_type, row_number
                )
                
                mapped_record[target_field] = converted_value
            else:
                # Handle missing fields based on requirements
                if target_field in self._get_required_fields(entity_type):
                    logger.warning(f"Required field '{source_field}' missing in row {row_number}")

        return mapped_record

    def _convert_field_value(
        self,
        value: Any,
        field_name: str,
        entity_type: str,
        row_number: int,
    ) -> Any:
        """Convert field value to appropriate type."""
        if value is None or value == "":
            return None

        try:
            # Numeric fields
            if field_name in ["diameter", "length", "thickness", "temperature", "pressure"]:
                return float(value) if value is not None else None
            
            # Integer fields
            elif field_name in ["quantity", "count"]:
                return int(value) if value is not None else None
            
            # Boolean fields
            elif field_name in ["is_insulated", "is_traced", "is_active"]:
                if isinstance(value, bool):
                    return value
                elif isinstance(value, str):
                    return value.lower() in ("true", "yes", "1", "on")
                else:
                    return bool(value)
            
            # String fields (default)
            else:
                return str(value).strip() if value is not None else None

        except (ValueError, TypeError) as e:
            logger.warning(f"Type conversion failed for field '{field_name}' in row {row_number}: {e}")
            return str(value) if value is not None else None

    def _transform_pipe_data(self, mapped_record: Dict[str, Any]) -> Dict[str, Any]:
        """Apply pipe-specific data transformations."""
        # Normalize material names
        if "material" in mapped_record and mapped_record["material"]:
            material = mapped_record["material"].lower().replace(" ", "_")
            mapped_record["material"] = material

        # Calculate derived fields
        if "diameter" in mapped_record and "length" in mapped_record:
            diameter = mapped_record["diameter"]
            length = mapped_record["length"]
            if diameter and length:
                # Calculate surface area
                import math
                surface_area = math.pi * diameter * length
                mapped_record["surface_area"] = surface_area

        # Set default values
        if "status" not in mapped_record:
            mapped_record["status"] = "active"

        return mapped_record

    def _transform_component_data(self, mapped_record: Dict[str, Any]) -> Dict[str, Any]:
        """Apply component-specific data transformations."""
        # Normalize component type
        if "component_type" in mapped_record and mapped_record["component_type"]:
            comp_type = mapped_record["component_type"].lower().replace(" ", "_")
            mapped_record["component_type"] = comp_type

        # Set default values
        if "status" not in mapped_record:
            mapped_record["status"] = "active"

        return mapped_record

    def _transform_circuit_data(self, mapped_record: Dict[str, Any]) -> Dict[str, Any]:
        """Apply circuit-specific data transformations."""
        # Calculate power density if not provided
        if ("total_power" in mapped_record and "circuit_length" in mapped_record and
            mapped_record["total_power"] and mapped_record["circuit_length"]):
            power_density = mapped_record["total_power"] / mapped_record["circuit_length"]
            mapped_record["power_density"] = power_density

        # Set default values
        if "status" not in mapped_record:
            mapped_record["status"] = "design"

        if "control_method" not in mapped_record:
            mapped_record["control_method"] = "automatic"

        return mapped_record

    def _get_required_fields(self, entity_type: str) -> List[str]:
        """Get list of required fields for entity type."""
        required_fields = {
            "pipe": ["name", "diameter", "length"],
            "component": ["name", "component_type"],
            "heat_tracing_circuit": ["name", "circuit_length"],
        }
        return required_fields.get(entity_type, [])

    def _load_mapping_rules(self) -> Dict[str, Dict[str, str]]:
        """Load default mapping rules for different entity types."""
        return {
            "pipes": {
                "pipe_name": "name",
                "pipe_id": "tag",
                "diameter": "diameter",
                "length": "length",
                "material": "material",
                "insulation_type": "insulation_type",
                "insulation_thickness": "insulation_thickness",
                "design_temperature": "design_temperature",
                "operating_temperature": "operating_temperature",
                "design_pressure": "design_pressure",
                "operating_pressure": "operating_pressure",
                "fluid_type": "fluid_type",
                "description": "description",
            },
            "components": {
                "component_name": "name",
                "component_id": "tag",
                "component_type": "component_type",
                "size": "size",
                "material": "material",
                "pipe_id": "pipe_id",
                "description": "description",
            },
            "heat_tracing_circuits": {
                "circuit_name": "name",
                "circuit_id": "tag",
                "circuit_length": "circuit_length",
                "cable_type": "cable_type",
                "power_per_meter": "power_per_meter",
                "total_power": "total_power",
                "supply_voltage": "supply_voltage",
                "control_method": "control_method",
                "pipe_id": "pipe_id",
                "description": "description",
            },
        }

    def create_mapping_template(self, entity_type: str) -> Dict[str, str]:
        """
        Create a mapping template for the specified entity type.

        Args:
            entity_type: Type of entity (pipes, components, circuits)

        Returns:
            Dict with default mapping template
        """
        logger.debug(f"Creating mapping template for: {entity_type}")

        if entity_type not in self.mapping_rules:
            raise InvalidInputError(f"Unknown entity type: {entity_type}")

        template = self.mapping_rules[entity_type].copy()
        
        # Add metadata
        template["_metadata"] = {
            "entity_type": entity_type,
            "project_id": self.project_id,
            "created_at": datetime.utcnow().isoformat(),
            "description": f"Default mapping template for {entity_type}",
        }

        return template

    def validate_mapping(
        self,
        mapping: Dict[str, str],
        available_fields: List[str],
        entity_type: str,
    ) -> Dict[str, Any]:
        """
        Validate a field mapping against available fields and requirements.

        Args:
            mapping: Field mapping to validate
            available_fields: List of available source fields
            entity_type: Type of entity being mapped

        Returns:
            Dict with validation results
        """
        logger.debug(f"Validating mapping for {entity_type}")

        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "missing_fields": [],
            "unmapped_fields": [],
        }

        required_fields = self._get_required_fields(entity_type)
        
        # Check for missing required mappings
        for source_field, target_field in mapping.items():
            if source_field not in available_fields:
                validation_result["missing_fields"].append(source_field)
                if target_field in required_fields:
                    validation_result["errors"].append(
                        f"Required field '{source_field}' not found in source data"
                    )
                    validation_result["is_valid"] = False

        # Check for unmapped available fields
        mapped_fields = set(mapping.keys())
        available_set = set(available_fields)
        unmapped = available_set - mapped_fields
        
        if unmapped:
            validation_result["unmapped_fields"] = list(unmapped)
            validation_result["warnings"].append(
                f"Available fields not mapped: {list(unmapped)}"
            )

        return validation_result
