# backend/core/data_import/parsers/csv_parser.py
"""
CSV File Parser.

This module provides parsing functionality for CSV files,
supporting various delimiters, encoding detection, and data validation.
"""

import logging
import csv
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import pandas as pd
import chardet

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class CSVParser:
    """
    Parser for CSV files with automatic delimiter detection and data validation.
    """

    def __init__(self):
        """Initialize the CSV parser."""
        self.supported_extensions = ['.csv', '.tsv', '.txt']
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.common_delimiters = [',', ';', '\t', '|', ':']
        logger.debug("CSVParser initialized")

    def parse_file(
        self,
        file_path: Union[str, Path],
        delimiter: Optional[str] = None,
        encoding: Optional[str] = None,
        header_row: int = 0,
        skip_rows: int = 0,
    ) -> Dict[str, Any]:
        """
        Parse CSV file and extract data.

        Args:
            file_path: Path to the CSV file
            delimiter: CSV delimiter (auto-detect if None)
            encoding: File encoding (auto-detect if None)
            header_row: Row containing column headers (0-indexed)
            skip_rows: Number of rows to skip at the beginning

        Returns:
            Dict containing parsed data and metadata

        Raises:
            InvalidInputError: If file is invalid or cannot be read
            CalculationError: If parsing fails
        """
        logger.info(f"Parsing CSV file: {file_path}")

        try:
            file_path = Path(file_path)
            
            # Validate file
            self._validate_file(file_path)

            # Detect encoding if not provided
            if encoding is None:
                encoding = self._detect_encoding(file_path)

            # Detect delimiter if not provided
            if delimiter is None:
                delimiter = self._detect_delimiter(file_path, encoding)

            # Read CSV using pandas for efficiency
            df = pd.read_csv(
                file_path,
                delimiter=delimiter,
                encoding=encoding,
                header=header_row,
                skiprows=skip_rows,
                na_values=['', 'NULL', 'null', 'N/A', 'n/a', 'NA', 'na'],
                keep_default_na=True,
            )

            # Clean column names
            df.columns = df.columns.astype(str).str.strip()

            # Remove completely empty rows
            df = df.dropna(how='all')

            # Convert to list of dictionaries
            data = df.to_dict('records')

            # Clean data - convert NaN to None
            cleaned_data = []
            for row in data:
                cleaned_row = {}
                for key, value in row.items():
                    if pd.isna(value):
                        cleaned_row[key] = None
                    else:
                        cleaned_row[key] = value
                cleaned_data.append(cleaned_row)

            result = {
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "data": cleaned_data,
                "columns": list(df.columns),
                "row_count": len(cleaned_data),
                "metadata": {
                    "parser": "CSVParser",
                    "delimiter": delimiter,
                    "encoding": encoding,
                    "header_row": header_row,
                    "skip_rows": skip_rows,
                    "original_row_count": len(df),
                    "column_count": len(df.columns),
                },
            }

            logger.info(f"Successfully parsed CSV: {len(cleaned_data)} rows, {len(df.columns)} columns")
            return result

        except UnicodeDecodeError as e:
            logger.error(f"Encoding error: {e}")
            raise InvalidInputError(f"File encoding error: {str(e)}")
        except pd.errors.EmptyDataError:
            logger.warning("CSV file is empty")
            return {
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "data": [],
                "columns": [],
                "row_count": 0,
                "metadata": {"parser": "CSVParser", "empty_file": True},
            }
        except Exception as e:
            logger.error(f"CSV parsing failed: {e}", exc_info=True)
            raise CalculationError(f"CSV parsing failed: {str(e)}")

    def parse_with_column_mapping(
        self,
        file_path: Union[str, Path],
        column_mapping: Dict[str, str],
        delimiter: Optional[str] = None,
        encoding: Optional[str] = None,
    ) -> List[Dict[str, Any]]:
        """
        Parse CSV file with column mapping.

        Args:
            file_path: Path to the CSV file
            column_mapping: Mapping of CSV columns to field names
            delimiter: CSV delimiter (auto-detect if None)
            encoding: File encoding (auto-detect if None)

        Returns:
            List of dictionaries with mapped data

        Raises:
            InvalidInputError: If required columns are missing
            CalculationError: If parsing fails
        """
        logger.debug(f"Parsing CSV with column mapping: {len(column_mapping)} columns")

        try:
            # Parse the file
            parsed_data = self.parse_file(file_path, delimiter, encoding)
            
            # Check if all required columns exist
            available_columns = parsed_data["columns"]
            missing_columns = []
            
            for csv_col in column_mapping.keys():
                if csv_col not in available_columns:
                    missing_columns.append(csv_col)

            if missing_columns:
                raise InvalidInputError(
                    f"Missing required columns: {missing_columns}. "
                    f"Available columns: {available_columns}"
                )

            # Apply column mapping
            mapped_data = []
            for row in parsed_data["data"]:
                mapped_row = {}
                for csv_col, field_name in column_mapping.items():
                    mapped_row[field_name] = row.get(csv_col, None)
                mapped_data.append(mapped_row)

            logger.debug(f"Mapped {len(mapped_data)} rows with {len(column_mapping)} columns")
            return mapped_data

        except Exception as e:
            logger.error(f"CSV column mapping failed: {e}")
            raise CalculationError(f"CSV column mapping failed: {str(e)}")

    def get_file_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get information about the CSV file without parsing all data.

        Args:
            file_path: Path to the CSV file

        Returns:
            Dict with file information
        """
        logger.debug(f"Getting CSV file info: {file_path}")

        try:
            file_path = Path(file_path)
            self._validate_file(file_path)

            # Detect encoding
            encoding = self._detect_encoding(file_path)
            
            # Detect delimiter
            delimiter = self._detect_delimiter(file_path, encoding)

            # Read just the header and a few rows
            sample_df = pd.read_csv(
                file_path,
                delimiter=delimiter,
                encoding=encoding,
                nrows=5,  # Read only first 5 rows
            )

            # Count total rows (approximate)
            with open(file_path, 'r', encoding=encoding) as f:
                total_rows = sum(1 for _ in f) - 1  # Subtract header row

            return {
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "encoding": encoding,
                "delimiter": delimiter,
                "columns": list(sample_df.columns),
                "column_count": len(sample_df.columns),
                "estimated_row_count": total_rows,
                "sample_data": sample_df.head(3).to_dict('records'),
            }

        except Exception as e:
            logger.error(f"Failed to get CSV file info: {e}")
            raise InvalidInputError(f"Failed to get CSV file info: {str(e)}")

    def validate_data_types(
        self,
        data: List[Dict[str, Any]],
        type_mapping: Dict[str, str],
    ) -> Dict[str, Any]:
        """
        Validate data types in parsed CSV data.

        Args:
            data: Parsed CSV data
            type_mapping: Mapping of column names to expected types

        Returns:
            Dict with validation results
        """
        logger.debug(f"Validating data types for {len(data)} rows")

        validation_errors = []
        type_conversion_map = {
            'int': int,
            'float': float,
            'str': str,
            'bool': bool,
        }

        for row_idx, row in enumerate(data):
            for column, expected_type in type_mapping.items():
                if column in row and row[column] is not None:
                    value = row[column]
                    
                    try:
                        if expected_type in type_conversion_map:
                            # Try to convert to expected type
                            type_conversion_map[expected_type](value)
                    except (ValueError, TypeError) as e:
                        validation_errors.append({
                            "row": row_idx + 1,
                            "column": column,
                            "value": value,
                            "expected_type": expected_type,
                            "error": str(e),
                        })

        return {
            "is_valid": len(validation_errors) == 0,
            "error_count": len(validation_errors),
            "errors": validation_errors,
            "validated_rows": len(data),
        }

    def _detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding."""
        logger.debug(f"Detecting encoding for: {file_path}")

        try:
            # Read a sample of the file
            with open(file_path, 'rb') as f:
                sample = f.read(10000)  # Read first 10KB

            # Use chardet to detect encoding
            result = chardet.detect(sample)
            encoding = result['encoding']
            confidence = result['confidence']

            logger.debug(f"Detected encoding: {encoding} (confidence: {confidence:.2f})")

            # Fallback to utf-8 if confidence is low
            if confidence < 0.7:
                logger.warning(f"Low confidence in encoding detection, using utf-8")
                encoding = 'utf-8'

            return encoding

        except Exception as e:
            logger.warning(f"Encoding detection failed: {e}, using utf-8")
            return 'utf-8'

    def _detect_delimiter(self, file_path: Path, encoding: str) -> str:
        """Detect CSV delimiter."""
        logger.debug(f"Detecting delimiter for: {file_path}")

        try:
            # Read first few lines
            with open(file_path, 'r', encoding=encoding) as f:
                sample_lines = []
                for i, line in enumerate(f):
                    if i >= 5:  # Read first 5 lines
                        break
                    sample_lines.append(line)

            sample_text = '\n'.join(sample_lines)

            # Use csv.Sniffer to detect delimiter
            sniffer = csv.Sniffer()
            try:
                dialect = sniffer.sniff(sample_text, delimiters=',;\t|:')
                delimiter = dialect.delimiter
                logger.debug(f"Detected delimiter: '{delimiter}'")
                return delimiter
            except csv.Error:
                # Fallback: count occurrences of common delimiters
                delimiter_counts = {}
                for delim in self.common_delimiters:
                    delimiter_counts[delim] = sample_text.count(delim)

                # Choose delimiter with highest count
                best_delimiter = max(delimiter_counts, key=delimiter_counts.get)
                
                if delimiter_counts[best_delimiter] > 0:
                    logger.debug(f"Fallback delimiter detection: '{best_delimiter}'")
                    return best_delimiter
                else:
                    logger.warning("No delimiter detected, using comma")
                    return ','

        except Exception as e:
            logger.warning(f"Delimiter detection failed: {e}, using comma")
            return ','

    def _validate_file(self, file_path: Path) -> None:
        """Validate CSV file."""
        if not file_path.exists():
            raise InvalidInputError(f"File not found: {file_path}")

        if not file_path.is_file():
            raise InvalidInputError(f"Path is not a file: {file_path}")

        if file_path.suffix.lower() not in self.supported_extensions:
            raise InvalidInputError(
                f"Unsupported file extension: {file_path.suffix}. "
                f"Supported: {self.supported_extensions}"
            )

        # Check file size
        if file_path.stat().st_size > self.max_file_size:
            raise InvalidInputError(
                f"File too large: {file_path.stat().st_size} bytes "
                f"(max: {self.max_file_size})"
            )

        # Check if file is readable
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1)  # Try to read first character
        except UnicodeDecodeError:
            # File might have different encoding, which is fine
            pass
        except Exception as e:
            raise InvalidInputError(f"Cannot read file: {str(e)}")

    def convert_data_types(
        self,
        data: List[Dict[str, Any]],
        type_mapping: Dict[str, str],
        strict: bool = False,
    ) -> List[Dict[str, Any]]:
        """
        Convert data types in parsed CSV data.

        Args:
            data: Parsed CSV data
            type_mapping: Mapping of column names to target types
            strict: Whether to raise errors on conversion failures

        Returns:
            List with converted data types
        """
        logger.debug(f"Converting data types for {len(data)} rows")

        type_conversion_map = {
            'int': int,
            'float': float,
            'str': str,
            'bool': lambda x: str(x).lower() in ('true', '1', 'yes', 'on'),
        }

        converted_data = []
        conversion_errors = []

        for row_idx, row in enumerate(data):
            converted_row = row.copy()
            
            for column, target_type in type_mapping.items():
                if column in row and row[column] is not None:
                    value = row[column]
                    
                    try:
                        if target_type in type_conversion_map:
                            converted_value = type_conversion_map[target_type](value)
                            converted_row[column] = converted_value
                    except (ValueError, TypeError) as e:
                        error_info = {
                            "row": row_idx + 1,
                            "column": column,
                            "value": value,
                            "target_type": target_type,
                            "error": str(e),
                        }
                        conversion_errors.append(error_info)
                        
                        if strict:
                            raise CalculationError(
                                f"Type conversion failed at row {row_idx + 1}, "
                                f"column '{column}': {str(e)}"
                            )
                        else:
                            # Keep original value on conversion failure
                            logger.warning(f"Type conversion failed: {error_info}")

            converted_data.append(converted_row)

        if conversion_errors and not strict:
            logger.warning(f"Type conversion completed with {len(conversion_errors)} errors")

        return converted_data
