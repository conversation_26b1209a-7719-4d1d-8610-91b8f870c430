# backend/core/data_import/parsers/json_parser.py
"""
JSON File Parser.

This module provides parsing functionality for JSON files,
supporting nested structures, validation, and error handling.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import jsonschema
from jsonschema import ValidationError

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class JSONParser:
    """
    Parser for JSON files with schema validation and data extraction.
    """

    def __init__(self):
        """Initialize the JSON parser."""
        self.supported_extensions = ['.json', '.jsonl']
        self.max_file_size = 50 * 1024 * 1024  # 50MB
        logger.debug("JSONParser initialized")

    def parse_file(
        self,
        file_path: Union[str, Path],
        schema: Optional[Dict[str, Any]] = None,
        encoding: str = 'utf-8',
    ) -> Dict[str, Any]:
        """
        Parse JSON file and extract data.

        Args:
            file_path: Path to the JSON file
            schema: JSON schema for validation (optional)
            encoding: File encoding

        Returns:
            Dict containing parsed data and metadata

        Raises:
            InvalidInputError: If file is invalid or cannot be read
            CalculationError: If parsing fails
        """
        logger.info(f"Parsing JSON file: {file_path}")

        try:
            file_path = Path(file_path)
            
            # Validate file
            self._validate_file(file_path)

            # Read and parse JSON
            with open(file_path, 'r', encoding=encoding) as f:
                data = json.load(f)

            # Validate against schema if provided
            validation_result = None
            if schema:
                validation_result = self.validate_against_schema(data, schema)

            result = {
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "data": data,
                "data_type": type(data).__name__,
                "validation": validation_result,
                "metadata": {
                    "parser": "JSONParser",
                    "encoding": encoding,
                    "schema_validated": schema is not None,
                },
            }

            # Add structure analysis
            if isinstance(data, dict):
                result["metadata"]["keys"] = list(data.keys())
                result["metadata"]["key_count"] = len(data.keys())
            elif isinstance(data, list):
                result["metadata"]["item_count"] = len(data)
                if data and isinstance(data[0], dict):
                    result["metadata"]["item_keys"] = list(data[0].keys())

            logger.info(f"Successfully parsed JSON file: {file_path}")
            return result

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            raise InvalidInputError(f"Invalid JSON format: {str(e)}")
        except UnicodeDecodeError as e:
            logger.error(f"Encoding error: {e}")
            raise InvalidInputError(f"File encoding error: {str(e)}")
        except Exception as e:
            logger.error(f"JSON parsing failed: {e}", exc_info=True)
            raise CalculationError(f"JSON parsing failed: {str(e)}")

    def parse_jsonl_file(
        self,
        file_path: Union[str, Path],
        schema: Optional[Dict[str, Any]] = None,
        encoding: str = 'utf-8',
    ) -> Dict[str, Any]:
        """
        Parse JSON Lines file (one JSON object per line).

        Args:
            file_path: Path to the JSONL file
            schema: JSON schema for validation (optional)
            encoding: File encoding

        Returns:
            Dict containing parsed data and metadata
        """
        logger.info(f"Parsing JSONL file: {file_path}")

        try:
            file_path = Path(file_path)
            self._validate_file(file_path)

            data = []
            validation_errors = []
            line_number = 0

            with open(file_path, 'r', encoding=encoding) as f:
                for line in f:
                    line_number += 1
                    line = line.strip()
                    
                    if not line:  # Skip empty lines
                        continue

                    try:
                        json_obj = json.loads(line)
                        
                        # Validate against schema if provided
                        if schema:
                            try:
                                jsonschema.validate(json_obj, schema)
                            except ValidationError as ve:
                                validation_errors.append({
                                    "line": line_number,
                                    "error": str(ve),
                                })
                        
                        data.append(json_obj)

                    except json.JSONDecodeError as e:
                        logger.warning(f"Invalid JSON on line {line_number}: {e}")
                        validation_errors.append({
                            "line": line_number,
                            "error": f"JSON decode error: {str(e)}",
                        })

            result = {
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "data": data,
                "data_type": "list",
                "validation_errors": validation_errors,
                "metadata": {
                    "parser": "JSONParser",
                    "format": "jsonl",
                    "encoding": encoding,
                    "total_lines": line_number,
                    "valid_objects": len(data),
                    "error_count": len(validation_errors),
                },
            }

            logger.info(f"Parsed JSONL file: {len(data)} objects, {len(validation_errors)} errors")
            return result

        except Exception as e:
            logger.error(f"JSONL parsing failed: {e}", exc_info=True)
            raise CalculationError(f"JSONL parsing failed: {str(e)}")

    def validate_against_schema(
        self,
        data: Any,
        schema: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Validate JSON data against a schema.

        Args:
            data: JSON data to validate
            schema: JSON schema

        Returns:
            Dict with validation results
        """
        logger.debug("Validating JSON data against schema")

        try:
            jsonschema.validate(data, schema)
            
            return {
                "is_valid": True,
                "errors": [],
                "schema_version": schema.get("$schema", "unknown"),
            }

        except ValidationError as e:
            logger.warning(f"Schema validation failed: {e}")
            
            return {
                "is_valid": False,
                "errors": [str(e)],
                "schema_version": schema.get("$schema", "unknown"),
                "error_path": list(e.absolute_path) if e.absolute_path else [],
            }

        except Exception as e:
            logger.error(f"Schema validation error: {e}")
            return {
                "is_valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "schema_version": schema.get("$schema", "unknown"),
            }

    def extract_nested_data(
        self,
        data: Dict[str, Any],
        path: str,
        default: Any = None,
    ) -> Any:
        """
        Extract data from nested JSON structure using dot notation.

        Args:
            data: JSON data
            path: Dot-separated path (e.g., "project.components.pipes")
            default: Default value if path not found

        Returns:
            Extracted data or default value
        """
        logger.debug(f"Extracting nested data from path: {path}")

        try:
            keys = path.split('.')
            current = data

            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                elif isinstance(current, list) and key.isdigit():
                    index = int(key)
                    if 0 <= index < len(current):
                        current = current[index]
                    else:
                        return default
                else:
                    return default

            return current

        except Exception as e:
            logger.warning(f"Failed to extract nested data: {e}")
            return default

    def flatten_json(
        self,
        data: Dict[str, Any],
        separator: str = '.',
        prefix: str = '',
    ) -> Dict[str, Any]:
        """
        Flatten nested JSON structure.

        Args:
            data: JSON data to flatten
            separator: Separator for nested keys
            prefix: Prefix for keys

        Returns:
            Flattened dictionary
        """
        logger.debug("Flattening JSON structure")

        def _flatten(obj, parent_key=''):
            items = []
            
            if isinstance(obj, dict):
                for key, value in obj.items():
                    new_key = f"{parent_key}{separator}{key}" if parent_key else key
                    items.extend(_flatten(value, new_key).items())
            elif isinstance(obj, list):
                for i, value in enumerate(obj):
                    new_key = f"{parent_key}{separator}{i}" if parent_key else str(i)
                    items.extend(_flatten(value, new_key).items())
            else:
                return {parent_key: obj}
            
            return dict(items)

        try:
            flattened = _flatten(data, prefix)
            logger.debug(f"Flattened {len(data)} keys to {len(flattened)} keys")
            return flattened

        except Exception as e:
            logger.error(f"JSON flattening failed: {e}")
            raise CalculationError(f"JSON flattening failed: {str(e)}")

    def convert_to_table_format(
        self,
        data: List[Dict[str, Any]],
        columns: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Convert list of JSON objects to table format.

        Args:
            data: List of JSON objects
            columns: Specific columns to extract (None for all)

        Returns:
            Dict with table-formatted data
        """
        logger.debug(f"Converting {len(data)} JSON objects to table format")

        try:
            if not data:
                return {
                    "columns": [],
                    "data": [],
                    "row_count": 0,
                }

            # Determine columns
            if columns is None:
                all_keys = set()
                for item in data:
                    if isinstance(item, dict):
                        all_keys.update(item.keys())
                columns = sorted(list(all_keys))

            # Convert to table format
            table_data = []
            for item in data:
                if isinstance(item, dict):
                    row = {}
                    for col in columns:
                        row[col] = item.get(col, None)
                    table_data.append(row)

            result = {
                "columns": columns,
                "data": table_data,
                "row_count": len(table_data),
                "metadata": {
                    "original_count": len(data),
                    "column_count": len(columns),
                },
            }

            logger.debug(f"Converted to table: {len(table_data)} rows, {len(columns)} columns")
            return result

        except Exception as e:
            logger.error(f"Table conversion failed: {e}")
            raise CalculationError(f"Table conversion failed: {str(e)}")

    def _validate_file(self, file_path: Path) -> None:
        """Validate JSON file."""
        if not file_path.exists():
            raise InvalidInputError(f"File not found: {file_path}")

        if not file_path.is_file():
            raise InvalidInputError(f"Path is not a file: {file_path}")

        if file_path.suffix.lower() not in self.supported_extensions:
            raise InvalidInputError(
                f"Unsupported file extension: {file_path.suffix}. "
                f"Supported: {self.supported_extensions}"
            )

        # Check file size
        if file_path.stat().st_size > self.max_file_size:
            raise InvalidInputError(
                f"File too large: {file_path.stat().st_size} bytes "
                f"(max: {self.max_file_size})"
            )

        # Check if file is readable
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                f.read(1)  # Try to read first character
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as f:
                    f.read(1)
            except Exception as e:
                raise InvalidInputError(f"File encoding error: {str(e)}")
        except Exception as e:
            raise InvalidInputError(f"Cannot read file: {str(e)}")

    def create_schema_from_sample(
        self,
        sample_data: Dict[str, Any],
        required_fields: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Create a JSON schema from sample data.

        Args:
            sample_data: Sample JSON data
            required_fields: List of required field names

        Returns:
            Generated JSON schema
        """
        logger.debug("Creating JSON schema from sample data")

        def _infer_type(value):
            if value is None:
                return {"type": "null"}
            elif isinstance(value, bool):
                return {"type": "boolean"}
            elif isinstance(value, int):
                return {"type": "integer"}
            elif isinstance(value, float):
                return {"type": "number"}
            elif isinstance(value, str):
                return {"type": "string"}
            elif isinstance(value, list):
                if value:
                    item_type = _infer_type(value[0])
                    return {"type": "array", "items": item_type}
                else:
                    return {"type": "array"}
            elif isinstance(value, dict):
                properties = {}
                for k, v in value.items():
                    properties[k] = _infer_type(v)
                return {"type": "object", "properties": properties}
            else:
                return {"type": "string"}

        try:
            schema = {
                "$schema": "http://json-schema.org/draft-07/schema#",
                "type": "object",
                "properties": {},
            }

            # Generate properties
            for key, value in sample_data.items():
                schema["properties"][key] = _infer_type(value)

            # Add required fields
            if required_fields:
                schema["required"] = required_fields

            logger.debug(f"Generated schema with {len(schema['properties'])} properties")
            return schema

        except Exception as e:
            logger.error(f"Schema generation failed: {e}")
            raise CalculationError(f"Schema generation failed: {str(e)}")
