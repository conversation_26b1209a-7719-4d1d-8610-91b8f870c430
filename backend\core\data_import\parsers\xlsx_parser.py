# backend/core/data_import/parsers/xlsx_parser.py
"""
XLSX File Parser.

This module provides parsing functionality for Excel XLSX files,
supporting multiple sheets, data validation, and error handling.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import pandas as pd
from openpyxl import load_workbook
from openpyxl.utils.exceptions import InvalidFileException

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class XLSXParser:
    """
    Parser for Excel XLSX files with comprehensive data extraction and validation.
    """

    def __init__(self):
        """Initialize the XLSX parser."""
        self.supported_extensions = ['.xlsx', '.xls']
        logger.debug("XLSXParser initialized")

    def parse_file(
        self,
        file_path: Union[str, Path],
        sheet_name: Optional[str] = None,
        header_row: int = 0,
        data_start_row: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Parse XLSX file and extract data.

        Args:
            file_path: Path to the XLSX file
            sheet_name: Specific sheet to parse (None for all sheets)
            header_row: Row containing column headers (0-indexed)
            data_start_row: Row where data starts (None to auto-detect)

        Returns:
            Dict containing parsed data and metadata

        Raises:
            InvalidInputError: If file is invalid or cannot be read
            CalculationError: If parsing fails
        """
        logger.info(f"Parsing XLSX file: {file_path}")

        try:
            file_path = Path(file_path)
            
            # Validate file
            self._validate_file(file_path)

            # Load workbook to get sheet information
            workbook = load_workbook(filename=file_path, read_only=True, data_only=True)
            
            result = {
                "file_path": str(file_path),
                "file_size": file_path.stat().st_size,
                "sheet_names": workbook.sheetnames,
                "sheets_data": {},
                "metadata": {
                    "parser": "XLSXParser",
                    "total_sheets": len(workbook.sheetnames),
                },
            }

            # Parse specific sheet or all sheets
            if sheet_name:
                if sheet_name not in workbook.sheetnames:
                    raise InvalidInputError(f"Sheet '{sheet_name}' not found in workbook")
                sheets_to_parse = [sheet_name]
            else:
                sheets_to_parse = workbook.sheetnames

            for sheet in sheets_to_parse:
                logger.debug(f"Parsing sheet: {sheet}")
                sheet_data = self._parse_sheet(
                    file_path, sheet, header_row, data_start_row
                )
                result["sheets_data"][sheet] = sheet_data

            workbook.close()

            logger.info(f"Successfully parsed {len(sheets_to_parse)} sheets from {file_path}")
            return result

        except InvalidInputError:
            raise
        except Exception as e:
            logger.error(f"XLSX parsing failed: {e}", exc_info=True)
            raise CalculationError(f"XLSX parsing failed: {str(e)}")

    def parse_sheet_data(
        self,
        file_path: Union[str, Path],
        sheet_name: str,
        column_mapping: Dict[str, str],
        header_row: int = 0,
        data_start_row: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Parse specific sheet with column mapping.

        Args:
            file_path: Path to the XLSX file
            sheet_name: Name of the sheet to parse
            column_mapping: Mapping of Excel columns to field names
            header_row: Row containing column headers
            data_start_row: Row where data starts

        Returns:
            List of dictionaries with mapped data

        Raises:
            InvalidInputError: If file or sheet is invalid
            CalculationError: If parsing fails
        """
        logger.debug(f"Parsing sheet '{sheet_name}' with column mapping")

        try:
            # Parse the sheet
            sheet_data = self._parse_sheet(file_path, sheet_name, header_row, data_start_row)
            
            # Apply column mapping
            mapped_data = []
            for row in sheet_data["data"]:
                mapped_row = {}
                for excel_col, field_name in column_mapping.items():
                    if excel_col in row:
                        mapped_row[field_name] = row[excel_col]
                    else:
                        logger.warning(f"Column '{excel_col}' not found in row")
                        mapped_row[field_name] = None
                
                mapped_data.append(mapped_row)

            logger.debug(f"Mapped {len(mapped_data)} rows with {len(column_mapping)} columns")
            return mapped_data

        except Exception as e:
            logger.error(f"Sheet data parsing failed: {e}")
            raise CalculationError(f"Sheet data parsing failed: {str(e)}")

    def get_sheet_info(self, file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Get information about sheets in the XLSX file.

        Args:
            file_path: Path to the XLSX file

        Returns:
            Dict with sheet information

        Raises:
            InvalidInputError: If file is invalid
        """
        logger.debug(f"Getting sheet info for: {file_path}")

        try:
            file_path = Path(file_path)
            self._validate_file(file_path)

            workbook = load_workbook(filename=file_path, read_only=True)
            
            sheet_info = {}
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                sheet_info[sheet_name] = {
                    "max_row": worksheet.max_row,
                    "max_column": worksheet.max_column,
                    "has_data": worksheet.max_row > 1,
                }

            workbook.close()

            return {
                "file_path": str(file_path),
                "total_sheets": len(workbook.sheetnames),
                "sheet_names": list(sheet_info.keys()),
                "sheet_details": sheet_info,
            }

        except Exception as e:
            logger.error(f"Failed to get sheet info: {e}")
            raise InvalidInputError(f"Failed to get sheet info: {str(e)}")

    def _parse_sheet(
        self,
        file_path: Path,
        sheet_name: str,
        header_row: int,
        data_start_row: Optional[int],
    ) -> Dict[str, Any]:
        """Parse individual sheet data."""
        try:
            # Use pandas for efficient data reading
            df = pd.read_excel(
                file_path,
                sheet_name=sheet_name,
                header=header_row,
                engine='openpyxl'
            )

            # Clean column names
            df.columns = df.columns.astype(str).str.strip()

            # Determine data start row
            if data_start_row is not None:
                df = df.iloc[data_start_row - header_row - 1:]

            # Remove completely empty rows
            df = df.dropna(how='all')

            # Convert to list of dictionaries
            data = df.to_dict('records')

            # Clean data - convert NaN to None
            cleaned_data = []
            for row in data:
                cleaned_row = {}
                for key, value in row.items():
                    if pd.isna(value):
                        cleaned_row[key] = None
                    else:
                        cleaned_row[key] = value
                cleaned_data.append(cleaned_row)

            return {
                "sheet_name": sheet_name,
                "columns": list(df.columns),
                "row_count": len(cleaned_data),
                "data": cleaned_data,
                "metadata": {
                    "header_row": header_row,
                    "data_start_row": data_start_row,
                    "original_row_count": len(df),
                },
            }

        except Exception as e:
            logger.error(f"Sheet parsing failed for '{sheet_name}': {e}")
            raise CalculationError(f"Sheet parsing failed: {str(e)}")

    def _validate_file(self, file_path: Path) -> None:
        """Validate XLSX file."""
        if not file_path.exists():
            raise InvalidInputError(f"File not found: {file_path}")

        if not file_path.is_file():
            raise InvalidInputError(f"Path is not a file: {file_path}")

        if file_path.suffix.lower() not in self.supported_extensions:
            raise InvalidInputError(
                f"Unsupported file extension: {file_path.suffix}. "
                f"Supported: {self.supported_extensions}"
            )

        # Check file size (limit to 100MB)
        max_size = 100 * 1024 * 1024  # 100MB
        if file_path.stat().st_size > max_size:
            raise InvalidInputError(f"File too large: {file_path.stat().st_size} bytes (max: {max_size})")

        # Try to open the file to validate it's a valid Excel file
        try:
            workbook = load_workbook(filename=file_path, read_only=True)
            workbook.close()
        except InvalidFileException as e:
            raise InvalidInputError(f"Invalid Excel file: {str(e)}")

    def extract_table_data(
        self,
        file_path: Union[str, Path],
        sheet_name: str,
        table_range: str,
        has_headers: bool = True,
    ) -> Dict[str, Any]:
        """
        Extract data from a specific table range.

        Args:
            file_path: Path to the XLSX file
            sheet_name: Name of the sheet
            table_range: Excel range (e.g., "A1:D10")
            has_headers: Whether the range includes headers

        Returns:
            Dict with extracted table data
        """
        logger.debug(f"Extracting table data from range {table_range}")

        try:
            workbook = load_workbook(filename=file_path, read_only=True, data_only=True)
            worksheet = workbook[sheet_name]

            # Parse range
            cells = worksheet[table_range]
            
            data = []
            headers = []

            # Extract data from cells
            for row_idx, row in enumerate(cells):
                row_data = []
                for cell in row:
                    value = cell.value
                    row_data.append(value)

                if row_idx == 0 and has_headers:
                    headers = [str(val) if val is not None else f"Column_{i}" for i, val in enumerate(row_data)]
                else:
                    if has_headers:
                        # Create dictionary with headers
                        row_dict = dict(zip(headers, row_data))
                        data.append(row_dict)
                    else:
                        data.append(row_data)

            workbook.close()

            return {
                "sheet_name": sheet_name,
                "table_range": table_range,
                "headers": headers if has_headers else None,
                "data": data,
                "row_count": len(data),
            }

        except Exception as e:
            logger.error(f"Table extraction failed: {e}")
            raise CalculationError(f"Table extraction failed: {str(e)}")

    def validate_required_columns(
        self,
        sheet_data: Dict[str, Any],
        required_columns: List[str],
    ) -> Dict[str, Any]:
        """
        Validate that required columns exist in sheet data.

        Args:
            sheet_data: Parsed sheet data
            required_columns: List of required column names

        Returns:
            Dict with validation results
        """
        logger.debug(f"Validating {len(required_columns)} required columns")

        available_columns = sheet_data.get("columns", [])
        missing_columns = []
        
        for col in required_columns:
            if col not in available_columns:
                missing_columns.append(col)

        validation_result = {
            "is_valid": len(missing_columns) == 0,
            "missing_columns": missing_columns,
            "available_columns": available_columns,
            "required_columns": required_columns,
        }

        if missing_columns:
            logger.warning(f"Missing required columns: {missing_columns}")
        else:
            logger.debug("All required columns found")

        return validation_result
