# backend/core/data_import/project_importer.py
"""
Project Data Importer.

This module handles importing project-specific data such as pipes,
components, and heat tracing circuits.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import tempfile

from .parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ars<PERSON>, CSVParser
from .validators import ImportDataValidator
from .mappers import ProjectDataMapper
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ProjectImporter:
    """
    Handles importing project-specific data with validation and error handling.
    """

    def __init__(self, project_id: str):
        """
        Initialize the project importer.

        Args:
            project_id: ID of the target project
        """
        self.project_id = project_id
        self.xlsx_parser = XLSXParser()
        self.json_parser = JSONParser()
        self.csv_parser = CSVParser()
        self.validator = ImportDataValidator()
        self.mapper = ProjectDataMapper(project_id)
        
        self.supported_formats = {
            '.xlsx': self.xlsx_parser,
            '.xls': self.xlsx_parser,
            '.json': self.json_parser,
            '.jsonl': self.json_parser,
            '.csv': self.csv_parser,
            '.tsv': self.csv_parser,
            '.txt': self.csv_parser,
        }
        
        logger.debug(f"ProjectImporter initialized for project: {project_id}")

    def import_pipes_data(
        self,
        file_path: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
    ) -> Dict[str, Any]:
        """
        Import pipes data from file.

        Args:
            file_path: Path to the import file
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing

        Returns:
            Dict with import results

        Raises:
            InvalidInputError: If file or data is invalid
            CalculationError: If import process fails
        """
        logger.info(f"Importing pipes data from: {file_path}")

        try:
            # Parse file
            parsed_data = self._parse_file(file_path, sheet_name)
            
            # Extract data
            raw_data = self._extract_data_from_parsed(parsed_data, sheet_name)
            logger.info(f"Parsed {len(raw_data)} pipe records")

            # Map data
            mapped_data = self.mapper.map_pipes_data(raw_data, field_mapping)

            # Validate data
            validation_result = self._validate_project_data(mapped_data, "pipes")

            if not validation_result.is_valid:
                logger.error(f"Pipes data validation failed: {validation_result.error_count} errors")
                if not validate_only:
                    raise CalculationError(f"Validation failed: {validation_result.error_count} errors")

            result = {
                "import_type": "pipes",
                "project_id": self.project_id,
                "file_path": file_path,
                "total_records": len(raw_data),
                "mapped_records": len(mapped_data),
                "validation": validation_result.to_dict(),
                "data": mapped_data if validate_only else None,
                "success": validation_result.is_valid,
            }

            if not validate_only and validation_result.is_valid:
                # TODO: Save to database
                logger.info(f"Pipes import completed: {len(mapped_data)} records")
                result["imported_records"] = len(mapped_data)

            return result

        except Exception as e:
            logger.error(f"Pipes import failed: {e}", exc_info=True)
            raise CalculationError(f"Pipes import failed: {str(e)}")

    def import_components_data(
        self,
        file_path: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
    ) -> Dict[str, Any]:
        """
        Import components data from file.

        Args:
            file_path: Path to the import file
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing

        Returns:
            Dict with import results
        """
        logger.info(f"Importing components data from: {file_path}")

        try:
            # Parse file
            parsed_data = self._parse_file(file_path, sheet_name)
            
            # Extract data
            raw_data = self._extract_data_from_parsed(parsed_data, sheet_name)
            logger.info(f"Parsed {len(raw_data)} component records")

            # Map data
            mapped_data = self.mapper.map_components_data(raw_data, field_mapping)

            # Validate data
            validation_result = self._validate_project_data(mapped_data, "components")

            if not validation_result.is_valid:
                logger.error(f"Components data validation failed: {validation_result.error_count} errors")
                if not validate_only:
                    raise CalculationError(f"Validation failed: {validation_result.error_count} errors")

            result = {
                "import_type": "components",
                "project_id": self.project_id,
                "file_path": file_path,
                "total_records": len(raw_data),
                "mapped_records": len(mapped_data),
                "validation": validation_result.to_dict(),
                "data": mapped_data if validate_only else None,
                "success": validation_result.is_valid,
            }

            if not validate_only and validation_result.is_valid:
                # TODO: Save to database
                logger.info(f"Components import completed: {len(mapped_data)} records")
                result["imported_records"] = len(mapped_data)

            return result

        except Exception as e:
            logger.error(f"Components import failed: {e}", exc_info=True)
            raise CalculationError(f"Components import failed: {str(e)}")

    def import_heat_tracing_circuits_data(
        self,
        file_path: str,
        field_mapping: Optional[Dict[str, str]] = None,
        sheet_name: Optional[str] = None,
        validate_only: bool = False,
        reference_data: Optional[Dict[str, List[Dict[str, Any]]]] = None,
    ) -> Dict[str, Any]:
        """
        Import heat tracing circuits data from file.

        Args:
            file_path: Path to the import file
            field_mapping: Custom field mapping (optional)
            sheet_name: Sheet name for Excel files (optional)
            validate_only: Only validate without importing
            reference_data: Reference data for validation (pipes, cables, etc.)

        Returns:
            Dict with import results
        """
        logger.info(f"Importing heat tracing circuits data from: {file_path}")

        try:
            # Parse file
            parsed_data = self._parse_file(file_path, sheet_name)
            
            # Extract data
            raw_data = self._extract_data_from_parsed(parsed_data, sheet_name)
            logger.info(f"Parsed {len(raw_data)} circuit records")

            # Map data
            mapped_data = self.mapper.map_heat_tracing_circuits_data(raw_data, field_mapping)

            # Validate data
            validation_result = self._validate_project_data(mapped_data, "heat_tracing_circuits")

            # Additional referential integrity validation if reference data provided
            if reference_data:
                ref_validation = self.validator.validate_referential_integrity(
                    mapped_data, reference_data, "heat_tracing_circuits"
                )
                # Combine validation results
                validation_result.errors.extend(ref_validation.errors)
                validation_result.warnings.extend(ref_validation.warnings)
                validation_result.error_count += ref_validation.error_count
                validation_result.warning_count += ref_validation.warning_count
                if not ref_validation.is_valid:
                    validation_result.is_valid = False

            if not validation_result.is_valid:
                logger.error(f"Circuits data validation failed: {validation_result.error_count} errors")
                if not validate_only:
                    raise CalculationError(f"Validation failed: {validation_result.error_count} errors")

            result = {
                "import_type": "heat_tracing_circuits",
                "project_id": self.project_id,
                "file_path": file_path,
                "total_records": len(raw_data),
                "mapped_records": len(mapped_data),
                "validation": validation_result.to_dict(),
                "data": mapped_data if validate_only else None,
                "success": validation_result.is_valid,
            }

            if not validate_only and validation_result.is_valid:
                # TODO: Save to database
                logger.info(f"Circuits import completed: {len(mapped_data)} records")
                result["imported_records"] = len(mapped_data)

            return result

        except Exception as e:
            logger.error(f"Circuits import failed: {e}", exc_info=True)
            raise CalculationError(f"Circuits import failed: {str(e)}")

    def import_multi_sheet_project_data(
        self,
        file_path: str,
        sheet_mappings: Dict[str, Dict[str, Any]],
        validate_only: bool = False,
    ) -> Dict[str, Any]:
        """
        Import data from multiple sheets in a single file.

        Args:
            file_path: Path to the Excel file
            sheet_mappings: Dict mapping sheet names to import configurations
            validate_only: Only validate without importing

        Returns:
            Dict with combined import results

        Example sheet_mappings:
        {
            "Pipes": {
                "data_type": "pipes",
                "field_mapping": {"pipe_name": "name", ...}
            },
            "Components": {
                "data_type": "components", 
                "field_mapping": {"comp_name": "name", ...}
            }
        }
        """
        logger.info(f"Importing multi-sheet project data from: {file_path}")

        try:
            # Parse entire file
            parsed_data = self._parse_file(file_path)
            
            if "sheets_data" not in parsed_data:
                raise CalculationError("File does not contain multiple sheets")

            results = {
                "import_type": "multi_sheet_project",
                "project_id": self.project_id,
                "file_path": file_path,
                "sheet_results": {},
                "total_success": True,
                "summary": {
                    "total_sheets": 0,
                    "successful_sheets": 0,
                    "total_records": 0,
                    "imported_records": 0,
                },
            }

            for sheet_name, config in sheet_mappings.items():
                if sheet_name not in parsed_data["sheets_data"]:
                    logger.warning(f"Sheet '{sheet_name}' not found in file")
                    continue

                try:
                    data_type = config["data_type"]
                    field_mapping = config.get("field_mapping")
                    
                    # Extract sheet data
                    sheet_data = parsed_data["sheets_data"][sheet_name]["data"]
                    
                    # Import based on data type
                    if data_type == "pipes":
                        sheet_result = self._import_sheet_data_as_pipes(
                            sheet_data, field_mapping, validate_only
                        )
                    elif data_type == "components":
                        sheet_result = self._import_sheet_data_as_components(
                            sheet_data, field_mapping, validate_only
                        )
                    elif data_type == "heat_tracing_circuits":
                        sheet_result = self._import_sheet_data_as_circuits(
                            sheet_data, field_mapping, validate_only
                        )
                    else:
                        raise InvalidInputError(f"Unknown data type: {data_type}")

                    results["sheet_results"][sheet_name] = sheet_result
                    results["summary"]["total_sheets"] += 1
                    results["summary"]["total_records"] += sheet_result["total_records"]
                    
                    if sheet_result["success"]:
                        results["summary"]["successful_sheets"] += 1
                        if not validate_only:
                            results["summary"]["imported_records"] += sheet_result.get("imported_records", 0)
                    else:
                        results["total_success"] = False

                except Exception as e:
                    logger.error(f"Failed to import sheet '{sheet_name}': {e}")
                    results["sheet_results"][sheet_name] = {
                        "success": False,
                        "error": str(e),
                    }
                    results["total_success"] = False

            logger.info(f"Multi-sheet import completed: {results['summary']}")
            return results

        except Exception as e:
            logger.error(f"Multi-sheet import failed: {e}", exc_info=True)
            raise CalculationError(f"Multi-sheet import failed: {str(e)}")

    def get_project_import_template(self, data_type: str, format: str = "xlsx") -> str:
        """
        Generate import template file for specified project data type.

        Args:
            data_type: Type of data (pipes, components, heat_tracing_circuits)
            format: File format (xlsx, csv, json)

        Returns:
            Path to generated template file
        """
        logger.info(f"Generating {format} template for {data_type}")

        try:
            # Get mapping template
            mapping_template = self.mapper.create_mapping_template(data_type)
            
            # Remove metadata
            if "_metadata" in mapping_template:
                del mapping_template["_metadata"]

            # Create template file
            temp_dir = tempfile.mkdtemp()
            template_path = Path(temp_dir) / f"{data_type}_import_template.{format}"

            if format.lower() == "xlsx":
                self._create_xlsx_template(template_path, mapping_template, data_type)
            elif format.lower() == "csv":
                self._create_csv_template(template_path, mapping_template)
            elif format.lower() == "json":
                self._create_json_template(template_path, mapping_template, data_type)
            else:
                raise InvalidInputError(f"Unsupported template format: {format}")

            logger.info(f"Template created: {template_path}")
            return str(template_path)

        except Exception as e:
            logger.error(f"Template generation failed: {e}")
            raise CalculationError(f"Template generation failed: {str(e)}")

    def _parse_file(self, file_path: str, sheet_name: Optional[str] = None) -> Dict[str, Any]:
        """Parse file using appropriate parser."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise InvalidInputError(f"File not found: {file_path}")

        file_ext = file_path.suffix.lower()
        
        if file_ext not in self.supported_formats:
            raise InvalidInputError(f"Unsupported file format: {file_ext}")

        parser = self.supported_formats[file_ext]
        
        if file_ext in ['.xlsx', '.xls']:
            return parser.parse_file(file_path, sheet_name)
        elif file_ext in ['.json', '.jsonl']:
            return parser.parse_file(file_path)
        else:  # CSV and variants
            return parser.parse_file(file_path)

    def _extract_data_from_parsed(self, parsed_data: Dict[str, Any], sheet_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Extract data list from parsed data structure."""
        if isinstance(parsed_data.get("data"), list):
            return parsed_data["data"]
        elif "sheets_data" in parsed_data:
            sheet_key = sheet_name or list(parsed_data["sheets_data"].keys())[0]
            return parsed_data["sheets_data"][sheet_key]["data"]
        else:
            raise CalculationError("No data found in file")

    def _validate_project_data(self, data: List[Dict[str, Any]], data_type: str):
        """Validate project data."""
        return self.validator.validate_business_rules(data, data_type, {"project_id": self.project_id})

    def _import_sheet_data_as_pipes(self, sheet_data: List[Dict[str, Any]], field_mapping: Optional[Dict[str, str]], validate_only: bool) -> Dict[str, Any]:
        """Import sheet data as pipes."""
        mapped_data = self.mapper.map_pipes_data(sheet_data, field_mapping)
        validation_result = self._validate_project_data(mapped_data, "pipes")
        
        return {
            "data_type": "pipes",
            "total_records": len(sheet_data),
            "mapped_records": len(mapped_data),
            "validation": validation_result.to_dict(),
            "success": validation_result.is_valid,
            "imported_records": len(mapped_data) if validation_result.is_valid and not validate_only else 0,
        }

    def _import_sheet_data_as_components(self, sheet_data: List[Dict[str, Any]], field_mapping: Optional[Dict[str, str]], validate_only: bool) -> Dict[str, Any]:
        """Import sheet data as components."""
        mapped_data = self.mapper.map_components_data(sheet_data, field_mapping)
        validation_result = self._validate_project_data(mapped_data, "components")
        
        return {
            "data_type": "components",
            "total_records": len(sheet_data),
            "mapped_records": len(mapped_data),
            "validation": validation_result.to_dict(),
            "success": validation_result.is_valid,
            "imported_records": len(mapped_data) if validation_result.is_valid and not validate_only else 0,
        }

    def _import_sheet_data_as_circuits(self, sheet_data: List[Dict[str, Any]], field_mapping: Optional[Dict[str, str]], validate_only: bool) -> Dict[str, Any]:
        """Import sheet data as heat tracing circuits."""
        mapped_data = self.mapper.map_heat_tracing_circuits_data(sheet_data, field_mapping)
        validation_result = self._validate_project_data(mapped_data, "heat_tracing_circuits")
        
        return {
            "data_type": "heat_tracing_circuits",
            "total_records": len(sheet_data),
            "mapped_records": len(mapped_data),
            "validation": validation_result.to_dict(),
            "success": validation_result.is_valid,
            "imported_records": len(mapped_data) if validation_result.is_valid and not validate_only else 0,
        }

    def _create_xlsx_template(self, template_path: Path, mapping: Dict[str, str], data_type: str):
        """Create Excel template file."""
        import pandas as pd
        
        headers = list(mapping.keys())
        sample_data = {}
        for header in headers:
            sample_data[header] = [f"Sample {header} 1", f"Sample {header} 2"]

        df = pd.DataFrame(sample_data)
        
        with pd.ExcelWriter(template_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=data_type, index=False)

    def _create_csv_template(self, template_path: Path, mapping: Dict[str, str]):
        """Create CSV template file."""
        import csv
        
        headers = list(mapping.keys())
        
        with open(template_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(headers)
            writer.writerow([f"Sample {header} 1" for header in headers])
            writer.writerow([f"Sample {header} 2" for header in headers])

    def _create_json_template(self, template_path: Path, mapping: Dict[str, str], data_type: str):
        """Create JSON template file."""
        import json
        
        sample_data = {
            "project_id": self.project_id,
            "data_type": data_type,
            "data": []
        }
        
        for i in range(2):
            sample_record = {}
            for key in mapping.keys():
                sample_record[key] = f"Sample {key} {i + 1}"
            sample_data["data"].append(sample_record)

        with open(template_path, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, indent=2)
