# backend/core/data_import/validators/import_data_validator.py
"""
Import Data Validator.

This module provides comprehensive validation for imported data,
including schema validation, business rules, and referential integrity.
"""

import logging
from typing import Dict, List, Any, Optional, Set
from pydantic import BaseModel, ValidationError
from datetime import datetime

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self):
        self.is_valid = True
        self.errors = []
        self.warnings = []
        self.info = []
        self.validated_count = 0
        self.error_count = 0
        self.warning_count = 0

    def add_error(self, message: str, row: Optional[int] = None, field: Optional[str] = None):
        """Add validation error."""
        self.errors.append({
            "type": "error",
            "message": message,
            "row": row,
            "field": field,
            "timestamp": datetime.now().isoformat(),
        })
        self.error_count += 1
        self.is_valid = False

    def add_warning(self, message: str, row: Optional[int] = None, field: Optional[str] = None):
        """Add validation warning."""
        self.warnings.append({
            "type": "warning",
            "message": message,
            "row": row,
            "field": field,
            "timestamp": datetime.now().isoformat(),
        })
        self.warning_count += 1

    def add_info(self, message: str, row: Optional[int] = None, field: Optional[str] = None):
        """Add validation info."""
        self.info.append({
            "type": "info",
            "message": message,
            "row": row,
            "field": field,
            "timestamp": datetime.now().isoformat(),
        })

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "is_valid": self.is_valid,
            "validated_count": self.validated_count,
            "error_count": self.error_count,
            "warning_count": self.warning_count,
            "errors": self.errors,
            "warnings": self.warnings,
            "info": self.info,
        }


class ImportDataValidator:
    """
    Comprehensive validator for imported data with schema and business rule validation.
    """

    def __init__(self):
        """Initialize the validator."""
        self.business_rules = self._load_business_rules()
        logger.debug("ImportDataValidator initialized")

    def validate_against_schema(
        self,
        data: List[Dict[str, Any]],
        schema_model: BaseModel,
        strict: bool = True,
    ) -> ValidationResult:
        """
        Validate data against Pydantic schema.

        Args:
            data: List of data records to validate
            schema_model: Pydantic model for validation
            strict: Whether to stop on first error

        Returns:
            ValidationResult with validation details
        """
        logger.info(f"Validating {len(data)} records against schema")

        result = ValidationResult()
        result.validated_count = len(data)

        for row_idx, record in enumerate(data):
            try:
                # Validate using Pydantic model
                validated_record = schema_model(**record)
                result.add_info(f"Record {row_idx + 1} validated successfully")

            except ValidationError as e:
                for error in e.errors():
                    field_path = '.'.join(str(loc) for loc in error['loc'])
                    error_msg = f"Schema validation failed: {error['msg']}"
                    result.add_error(error_msg, row_idx + 1, field_path)

                if strict and result.error_count > 0:
                    break

            except Exception as e:
                result.add_error(
                    f"Unexpected validation error: {str(e)}",
                    row_idx + 1
                )
                if strict:
                    break

        logger.info(f"Schema validation completed: {result.error_count} errors, {result.warning_count} warnings")
        return result

    def validate_business_rules(
        self,
        data: List[Dict[str, Any]],
        data_type: str,
        project_context: Optional[Dict[str, Any]] = None,
    ) -> ValidationResult:
        """
        Validate data against business rules.

        Args:
            data: List of data records to validate
            data_type: Type of data (pipes, components, cables, etc.)
            project_context: Project-specific context for validation

        Returns:
            ValidationResult with validation details
        """
        logger.info(f"Validating {len(data)} {data_type} records against business rules")

        result = ValidationResult()
        result.validated_count = len(data)

        # Get business rules for data type
        rules = self.business_rules.get(data_type, {})

        for row_idx, record in enumerate(data):
            try:
                # Apply data type specific rules
                if data_type == "pipes":
                    self._validate_pipe_rules(record, result, row_idx + 1)
                elif data_type == "components":
                    self._validate_component_rules(record, result, row_idx + 1)
                elif data_type == "cables":
                    self._validate_cable_rules(record, result, row_idx + 1)
                elif data_type == "heat_tracing_circuits":
                    self._validate_circuit_rules(record, result, row_idx + 1, project_context)

                # Apply general rules
                self._validate_general_rules(record, result, row_idx + 1)

            except Exception as e:
                result.add_error(
                    f"Business rule validation error: {str(e)}",
                    row_idx + 1
                )

        logger.info(f"Business rule validation completed: {result.error_count} errors")
        return result

    def validate_referential_integrity(
        self,
        data: List[Dict[str, Any]],
        reference_data: Dict[str, List[Dict[str, Any]]],
        data_type: str,
    ) -> ValidationResult:
        """
        Validate referential integrity between data sets.

        Args:
            data: Primary data to validate
            reference_data: Reference data sets
            data_type: Type of primary data

        Returns:
            ValidationResult with validation details
        """
        logger.info(f"Validating referential integrity for {len(data)} {data_type} records")

        result = ValidationResult()
        result.validated_count = len(data)

        # Build reference lookup tables
        reference_lookups = {}
        for ref_type, ref_records in reference_data.items():
            reference_lookups[ref_type] = set()
            for record in ref_records:
                if 'id' in record:
                    reference_lookups[ref_type].add(record['id'])
                elif 'name' in record:
                    reference_lookups[ref_type].add(record['name'])

        # Validate references
        for row_idx, record in enumerate(data):
            try:
                if data_type == "heat_tracing_circuits":
                    self._validate_circuit_references(record, reference_lookups, result, row_idx + 1)
                elif data_type == "components":
                    self._validate_component_references(record, reference_lookups, result, row_idx + 1)

            except Exception as e:
                result.add_error(
                    f"Referential integrity error: {str(e)}",
                    row_idx + 1
                )

        logger.info(f"Referential integrity validation completed: {result.error_count} errors")
        return result

    def validate_data_consistency(
        self,
        data: List[Dict[str, Any]],
        consistency_rules: Dict[str, Any],
    ) -> ValidationResult:
        """
        Validate data consistency within the dataset.

        Args:
            data: Data to validate
            consistency_rules: Rules for consistency checking

        Returns:
            ValidationResult with validation details
        """
        logger.info(f"Validating data consistency for {len(data)} records")

        result = ValidationResult()
        result.validated_count = len(data)

        # Check for duplicates
        seen_keys = set()
        unique_fields = consistency_rules.get("unique_fields", [])

        for row_idx, record in enumerate(data):
            try:
                # Check uniqueness
                for field in unique_fields:
                    if field in record and record[field] is not None:
                        key = f"{field}:{record[field]}"
                        if key in seen_keys:
                            result.add_error(
                                f"Duplicate value in field '{field}': {record[field]}",
                                row_idx + 1,
                                field
                            )
                        else:
                            seen_keys.add(key)

                # Check cross-field consistency
                self._validate_cross_field_consistency(record, result, row_idx + 1)

            except Exception as e:
                result.add_error(
                    f"Consistency validation error: {str(e)}",
                    row_idx + 1
                )

        logger.info(f"Data consistency validation completed: {result.error_count} errors")
        return result

    def _validate_pipe_rules(self, record: Dict[str, Any], result: ValidationResult, row: int):
        """Validate pipe-specific business rules."""
        # Diameter validation
        if 'diameter' in record:
            diameter = record['diameter']
            if diameter is not None:
                if diameter <= 0:
                    result.add_error("Pipe diameter must be positive", row, "diameter")
                elif diameter > 2.0:  # 2 meters seems excessive
                    result.add_warning("Unusually large pipe diameter", row, "diameter")

        # Length validation
        if 'length' in record:
            length = record['length']
            if length is not None:
                if length <= 0:
                    result.add_error("Pipe length must be positive", row, "length")
                elif length > 1000:  # 1km seems excessive for single pipe
                    result.add_warning("Unusually long pipe segment", row, "length")

        # Material validation
        if 'material' in record:
            material = record['material']
            if material:
                valid_materials = ['carbon_steel', 'stainless_steel_304', 'stainless_steel_316', 
                                'copper', 'aluminum', 'pvc', 'hdpe']
                if material.lower() not in valid_materials:
                    result.add_warning(f"Unknown pipe material: {material}", row, "material")

    def _validate_component_rules(self, record: Dict[str, Any], result: ValidationResult, row: int):
        """Validate component-specific business rules."""
        # Component type validation
        if 'component_type' in record:
            comp_type = record['component_type']
            if comp_type:
                valid_types = ['valve', 'fitting', 'flange', 'tee', 'elbow', 'reducer', 'instrument']
                if comp_type.lower() not in valid_types:
                    result.add_warning(f"Unknown component type: {comp_type}", row, "component_type")

        # Size validation
        if 'size' in record:
            size = record['size']
            if size is not None and size <= 0:
                result.add_error("Component size must be positive", row, "size")

    def _validate_cable_rules(self, record: Dict[str, Any], result: ValidationResult, row: int):
        """Validate cable-specific business rules."""
        # Power validation
        if 'power_per_meter' in record:
            power = record['power_per_meter']
            if power is not None:
                if power <= 0:
                    result.add_error("Cable power per meter must be positive", row, "power_per_meter")
                elif power > 100:  # 100 W/m seems excessive
                    result.add_warning("Very high power density cable", row, "power_per_meter")

        # Voltage validation
        if 'voltage' in record:
            voltage = record['voltage']
            if voltage is not None:
                if voltage <= 0:
                    result.add_error("Cable voltage must be positive", row, "voltage")
                elif voltage > 1000:
                    result.add_warning("High voltage cable", row, "voltage")

    def _validate_circuit_rules(
        self, 
        record: Dict[str, Any], 
        result: ValidationResult, 
        row: int,
        project_context: Optional[Dict[str, Any]],
    ):
        """Validate heat tracing circuit-specific business rules."""
        # Circuit length validation
        if 'circuit_length' in record:
            length = record['circuit_length']
            if length is not None:
                if length <= 0:
                    result.add_error("Circuit length must be positive", row, "circuit_length")
                elif length > 500:  # 500m seems like a reasonable circuit limit
                    result.add_warning("Very long heat tracing circuit", row, "circuit_length")

        # Power validation
        if 'total_power' in record:
            power = record['total_power']
            if power is not None:
                if power <= 0:
                    result.add_error("Circuit power must be positive", row, "total_power")

    def _validate_general_rules(self, record: Dict[str, Any], result: ValidationResult, row: int):
        """Validate general business rules applicable to all data types."""
        # Check for required fields that shouldn't be empty
        required_fields = ['name', 'id']
        for field in required_fields:
            if field in record:
                value = record[field]
                if value is None or (isinstance(value, str) and value.strip() == ""):
                    result.add_error(f"Required field '{field}' is empty", row, field)

        # Check for reasonable temperature ranges
        temp_fields = ['temperature', 'design_temperature', 'operating_temperature']
        for field in temp_fields:
            if field in record:
                temp = record[field]
                if temp is not None:
                    if temp < -50 or temp > 500:  # Reasonable engineering range
                        result.add_warning(f"Temperature outside typical range: {temp}°C", row, field)

    def _validate_circuit_references(
        self,
        record: Dict[str, Any],
        reference_lookups: Dict[str, Set],
        result: ValidationResult,
        row: int,
    ):
        """Validate circuit references to other entities."""
        # Check pipe references
        if 'pipe_id' in record and record['pipe_id']:
            if 'pipes' in reference_lookups:
                if record['pipe_id'] not in reference_lookups['pipes']:
                    result.add_error(f"Referenced pipe not found: {record['pipe_id']}", row, "pipe_id")

        # Check cable references
        if 'cable_type' in record and record['cable_type']:
            if 'cables' in reference_lookups:
                if record['cable_type'] not in reference_lookups['cables']:
                    result.add_warning(f"Referenced cable type not found: {record['cable_type']}", row, "cable_type")

    def _validate_component_references(
        self,
        record: Dict[str, Any],
        reference_lookups: Dict[str, Set],
        result: ValidationResult,
        row: int,
    ):
        """Validate component references to other entities."""
        # Check pipe references
        if 'pipe_id' in record and record['pipe_id']:
            if 'pipes' in reference_lookups:
                if record['pipe_id'] not in reference_lookups['pipes']:
                    result.add_error(f"Referenced pipe not found: {record['pipe_id']}", row, "pipe_id")

    def _validate_cross_field_consistency(self, record: Dict[str, Any], result: ValidationResult, row: int):
        """Validate consistency between related fields."""
        # Example: If insulation thickness is specified, insulation type should also be specified
        if 'insulation_thickness' in record and record['insulation_thickness']:
            if 'insulation_type' not in record or not record['insulation_type']:
                result.add_warning(
                    "Insulation thickness specified without insulation type",
                    row,
                    "insulation_type"
                )

        # Example: If design temperature > operating temperature, warn
        if ('design_temperature' in record and 'operating_temperature' in record and
            record['design_temperature'] is not None and record['operating_temperature'] is not None):
            if record['design_temperature'] < record['operating_temperature']:
                result.add_warning(
                    "Design temperature is lower than operating temperature",
                    row,
                    "design_temperature"
                )

    def _load_business_rules(self) -> Dict[str, Any]:
        """Load business rules configuration."""
        # In a real implementation, this would load from configuration files
        return {
            "pipes": {
                "diameter_range": {"min": 0.01, "max": 2.0},
                "length_range": {"min": 0.1, "max": 1000.0},
                "valid_materials": ["carbon_steel", "stainless_steel", "copper", "pvc"],
            },
            "components": {
                "valid_types": ["valve", "fitting", "flange", "instrument"],
                "size_range": {"min": 0.01, "max": 2.0},
            },
            "cables": {
                "power_range": {"min": 1.0, "max": 100.0},
                "voltage_range": {"min": 12.0, "max": 1000.0},
            },
            "circuits": {
                "length_range": {"min": 1.0, "max": 500.0},
                "power_range": {"min": 10.0, "max": 50000.0},
            },
        }
