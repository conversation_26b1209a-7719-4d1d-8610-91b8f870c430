# backend/core/reports/__init__.py
"""
Reports Layer - Document generation and data export functionality.

This module provides comprehensive reporting capabilities for the Ultimate
Electrical Designer, supporting multiple output formats and professional
document generation.

The reports layer is organized into specialized sub-packages:
- templates: Document templates and template management
- generators: Report generation engines for different formats
- data_preparation: Data aggregation and preparation for reports
- export: Export functionality for various formats

All report operations follow professional document standards and provide
comprehensive customization options for different engineering requirements.
"""

from .report_service import ReportService
from .document_generator import DocumentGenerator
from .data_exporter import DataExporter

__all__ = [
    "ReportService",
    "DocumentGenerator", 
    "DataExporter",
]
