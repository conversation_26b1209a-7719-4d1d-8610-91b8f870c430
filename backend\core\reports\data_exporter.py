# backend/core/reports/data_exporter.py
"""
Data Exporter.

This module provides data export functionality for various formats
including CSV, JSON, and Excel with comprehensive formatting options.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import tempfile
from datetime import datetime
import json
import csv
import zipfile

from .data_preparation import ExportDataFormatter
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class DataExporter:
    """
    Provides comprehensive data export functionality for various formats.
    """

    def __init__(self):
        """Initialize the data exporter."""
        self.formatter = ExportDataFormatter()
        logger.debug("DataExporter initialized")

    def export_to_csv(
        self,
        data: Dict[str, Any],
        export_type: str,
        output_path: str,
        delimiter: str = ",",
        include_metadata: bool = True,
    ) -> str:
        """
        Export data to CSV format.

        Args:
            data: Data to export
            export_type: Type of export (calculation_report, cable_schedule, etc.)
            output_path: Path to save CSV file(s)
            delimiter: CSV delimiter
            include_metadata: Whether to include metadata file

        Returns:
            Path to exported file or directory

        Raises:
            CalculationError: If export fails
        """
        logger.info(f"Exporting {export_type} data to CSV: {output_path}")

        try:
            # Format data for CSV export
            csv_data = self.formatter.format_for_csv_export(data, export_type, delimiter)

            output_path_obj = Path(output_path)
            
            if len(csv_data) == 1:
                # Single CSV file
                csv_file_data = csv_data[0]
                self._write_csv_file(csv_file_data, output_path_obj, delimiter)
                result_path = str(output_path_obj)
            else:
                # Multiple CSV files - create directory
                output_dir = output_path_obj.with_suffix('')
                output_dir.mkdir(parents=True, exist_ok=True)
                
                for csv_file_data in csv_data:
                    file_path = output_dir / csv_file_data["filename"]
                    self._write_csv_file(csv_file_data, file_path, delimiter)
                
                result_path = str(output_dir)

            # Create metadata file if requested
            if include_metadata:
                metadata_path = Path(result_path).parent / "export_metadata.json" if len(csv_data) == 1 else Path(result_path) / "export_metadata.json"
                self._create_metadata_file(data, export_type, "csv", metadata_path)

            logger.info(f"CSV export completed: {result_path}")
            return result_path

        except Exception as e:
            logger.error(f"CSV export failed: {e}")
            raise CalculationError(f"CSV export failed: {str(e)}")

    def export_to_json(
        self,
        data: Dict[str, Any],
        export_type: str,
        output_path: str,
        pretty_print: bool = True,
        include_schema: bool = False,
    ) -> str:
        """
        Export data to JSON format.

        Args:
            data: Data to export
            export_type: Type of export
            output_path: Path to save JSON file
            pretty_print: Whether to format JSON for readability
            include_schema: Whether to include JSON schema

        Returns:
            Path to exported JSON file

        Raises:
            CalculationError: If export fails
        """
        logger.info(f"Exporting {export_type} data to JSON: {output_path}")

        try:
            # Format data for JSON export
            json_data = self.formatter.format_for_json_export(data, export_type, pretty_print)

            # Write JSON file
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as f:
                if pretty_print:
                    json.dump(json_data, f, indent=2, ensure_ascii=False)
                else:
                    json.dump(json_data, f, ensure_ascii=False)

            # Create schema file if requested
            if include_schema:
                schema_path = output_file.with_suffix('.schema.json')
                schema = self._generate_json_schema(json_data)
                with open(schema_path, 'w', encoding='utf-8') as f:
                    json.dump(schema, f, indent=2)

            logger.info(f"JSON export completed: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"JSON export failed: {e}")
            raise CalculationError(f"JSON export failed: {str(e)}")

    def export_to_excel(
        self,
        data: Dict[str, Any],
        export_type: str,
        output_path: str,
        include_formatting: bool = True,
        include_charts: bool = False,
    ) -> str:
        """
        Export data to Excel format.

        Args:
            data: Data to export
            export_type: Type of export
            output_path: Path to save Excel file
            include_formatting: Whether to include formatting
            include_charts: Whether to include charts

        Returns:
            Path to exported Excel file

        Raises:
            CalculationError: If export fails
        """
        logger.info(f"Exporting {export_type} data to Excel: {output_path}")

        try:
            # Format data for Excel export
            excel_data = self.formatter.format_for_excel_export(data, export_type, include_formatting)

            # Use Excel generator to create file
            from .generators import ExcelGenerator
            excel_generator = ExcelGenerator()
            
            result_path = excel_generator.generate_excel_report(
                excel_data["sheets"], output_path, 
                excel_data.get("workbook_name"), include_charts
            )

            logger.info(f"Excel export completed: {result_path}")
            return result_path

        except Exception as e:
            logger.error(f"Excel export failed: {e}")
            raise CalculationError(f"Excel export failed: {str(e)}")

    def export_multi_format_package(
        self,
        data: Dict[str, Any],
        export_type: str,
        output_directory: str,
        formats: Optional[List[str]] = None,
        create_archive: bool = False,
    ) -> Union[str, Dict[str, str]]:
        """
        Export data in multiple formats.

        Args:
            data: Data to export
            export_type: Type of export
            output_directory: Directory to save files
            formats: List of formats to export (default: all)
            create_archive: Whether to create ZIP archive

        Returns:
            Path to archive or dict of format->path mappings

        Raises:
            CalculationError: If export fails
        """
        logger.info(f"Exporting {export_type} data in multiple formats")

        try:
            # Set default formats
            if formats is None:
                formats = ["csv", "json", "excel"]

            # Create output directory
            output_dir = Path(output_directory)
            output_dir.mkdir(parents=True, exist_ok=True)

            exported_files = {}

            # Export each format
            for format_type in formats:
                try:
                    if format_type.lower() == "csv":
                        file_path = self.export_to_csv(
                            data, export_type, 
                            str(output_dir / f"{export_type}.csv")
                        )
                        exported_files["csv"] = file_path

                    elif format_type.lower() == "json":
                        file_path = self.export_to_json(
                            data, export_type,
                            str(output_dir / f"{export_type}.json")
                        )
                        exported_files["json"] = file_path

                    elif format_type.lower() == "excel":
                        file_path = self.export_to_excel(
                            data, export_type,
                            str(output_dir / f"{export_type}.xlsx")
                        )
                        exported_files["excel"] = file_path

                except Exception as e:
                    logger.error(f"Failed to export {format_type}: {e}")
                    exported_files[format_type] = f"Error: {str(e)}"

            # Create archive if requested
            if create_archive:
                archive_path = output_dir.with_suffix('.zip')
                self._create_archive(output_dir, archive_path)
                logger.info(f"Multi-format export archive created: {archive_path}")
                return str(archive_path)
            else:
                logger.info(f"Multi-format export completed: {len(exported_files)} files")
                return exported_files

        except Exception as e:
            logger.error(f"Multi-format export failed: {e}")
            raise CalculationError(f"Multi-format export failed: {str(e)}")

    def export_filtered_data(
        self,
        data: Dict[str, Any],
        export_type: str,
        filters: Dict[str, Any],
        output_path: str,
        format_type: str = "csv",
    ) -> str:
        """
        Export filtered subset of data.

        Args:
            data: Original data
            export_type: Type of export
            filters: Filters to apply
            output_path: Output file path
            format_type: Export format

        Returns:
            Path to exported file

        Raises:
            CalculationError: If export fails
        """
        logger.info(f"Exporting filtered {export_type} data")

        try:
            # Apply filters to data
            filtered_data = self._apply_filters(data, filters)

            # Export filtered data
            if format_type.lower() == "csv":
                return self.export_to_csv(filtered_data, export_type, output_path)
            elif format_type.lower() == "json":
                return self.export_to_json(filtered_data, export_type, output_path)
            elif format_type.lower() == "excel":
                return self.export_to_excel(filtered_data, export_type, output_path)
            else:
                raise InvalidInputError(f"Unsupported export format: {format_type}")

        except Exception as e:
            logger.error(f"Filtered export failed: {e}")
            raise CalculationError(f"Filtered export failed: {str(e)}")

    def get_export_summary(
        self,
        data: Dict[str, Any],
        export_type: str,
    ) -> Dict[str, Any]:
        """
        Get summary of data to be exported.

        Args:
            data: Data to analyze
            export_type: Type of export

        Returns:
            Dict with export summary information
        """
        logger.debug(f"Getting export summary for {export_type}")

        try:
            summary = {
                "export_type": export_type,
                "data_summary": {},
                "estimated_sizes": {},
                "available_formats": ["csv", "json", "excel"],
                "recommended_format": "excel",
            }

            # Analyze data structure
            if export_type == "calculation_report":
                circuits = data.get("circuits", [])
                summary["data_summary"] = {
                    "total_circuits": len(circuits),
                    "design_parameters": len(data.get("design_parameters", [])),
                    "has_summary_data": "summary_data" in data,
                }
                
            elif export_type == "cable_schedule":
                cables = data.get("cables_data", [])
                summary["data_summary"] = {
                    "total_cables": len(cables),
                    "total_length": sum(c.get("length", 0) for c in cables),
                    "total_power": sum(c.get("total_power", 0) for c in cables),
                }

            # Estimate file sizes (rough estimates)
            record_count = max(
                len(data.get("circuits", [])),
                len(data.get("cables_data", [])),
                len(data.get("load_data", [])),
                len(data.get("materials_data", [])),
                1
            )
            
            summary["estimated_sizes"] = {
                "csv": f"{record_count * 0.1:.1f} KB",
                "json": f"{record_count * 0.2:.1f} KB", 
                "excel": f"{record_count * 0.3:.1f} KB",
            }

            return summary

        except Exception as e:
            logger.error(f"Failed to get export summary: {e}")
            return {"error": str(e)}

    def _write_csv_file(
        self,
        csv_file_data: Dict[str, Any],
        file_path: Path,
        delimiter: str,
    ):
        """Write CSV file data to disk."""
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f, delimiter=delimiter)
            
            # Write headers
            headers = csv_file_data.get("headers", [])
            if headers:
                writer.writerow(headers)
            
            # Write data
            data = csv_file_data.get("data", [])
            for row in data:
                if isinstance(row, dict):
                    # Convert dict to list based on headers
                    row_data = [row.get(header, "") for header in headers]
                    writer.writerow(row_data)
                else:
                    writer.writerow(row)

    def _create_metadata_file(
        self,
        data: Dict[str, Any],
        export_type: str,
        format_type: str,
        metadata_path: Path,
    ):
        """Create metadata file for export."""
        metadata = {
            "export_info": {
                "export_type": export_type,
                "format": format_type,
                "exported_at": datetime.now().isoformat(),
                "generator": "Ultimate Electrical Designer",
                "version": "1.0",
            },
            "data_info": {
                "project_name": data.get("project_name", "Unknown"),
                "project_number": data.get("project_number", "Unknown"),
                "record_counts": self._count_records(data),
            },
        }

        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2)

    def _count_records(self, data: Dict[str, Any]) -> Dict[str, int]:
        """Count records in data."""
        counts = {}
        
        list_fields = ["circuits", "cables_data", "load_data", "materials_data", "design_parameters"]
        for field in list_fields:
            if field in data and isinstance(data[field], list):
                counts[field] = len(data[field])
        
        return counts

    def _generate_json_schema(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate basic JSON schema for data."""
        # This is a simplified schema generator
        # In a real implementation, you might use a library like jsonschema
        
        def infer_type(value):
            if isinstance(value, bool):
                return "boolean"
            elif isinstance(value, int):
                return "integer"
            elif isinstance(value, float):
                return "number"
            elif isinstance(value, str):
                return "string"
            elif isinstance(value, list):
                return "array"
            elif isinstance(value, dict):
                return "object"
            else:
                return "string"

        def build_schema(obj):
            if isinstance(obj, dict):
                properties = {}
                for key, value in obj.items():
                    properties[key] = build_schema(value)
                return {
                    "type": "object",
                    "properties": properties
                }
            elif isinstance(obj, list) and obj:
                return {
                    "type": "array",
                    "items": build_schema(obj[0])
                }
            else:
                return {"type": infer_type(obj)}

        return {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "title": "Export Data Schema",
            **build_schema(json_data)
        }

    def _apply_filters(self, data: Dict[str, Any], filters: Dict[str, Any]) -> Dict[str, Any]:
        """Apply filters to data."""
        filtered_data = data.copy()

        # Apply filters to list fields
        for field_name, field_filters in filters.items():
            if field_name in filtered_data and isinstance(filtered_data[field_name], list):
                filtered_list = []
                
                for item in filtered_data[field_name]:
                    include_item = True
                    
                    for filter_key, filter_value in field_filters.items():
                        if filter_key in item:
                            item_value = item[filter_key]
                            
                            # Simple filter logic
                            if isinstance(filter_value, dict):
                                # Range filter
                                if "min" in filter_value and item_value < filter_value["min"]:
                                    include_item = False
                                if "max" in filter_value and item_value > filter_value["max"]:
                                    include_item = False
                            else:
                                # Exact match filter
                                if item_value != filter_value:
                                    include_item = False
                    
                    if include_item:
                        filtered_list.append(item)
                
                filtered_data[field_name] = filtered_list

        return filtered_data

    def _create_archive(self, source_dir: Path, archive_path: Path):
        """Create ZIP archive of directory."""
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in source_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(source_dir)
                    zipf.write(file_path, arcname)
