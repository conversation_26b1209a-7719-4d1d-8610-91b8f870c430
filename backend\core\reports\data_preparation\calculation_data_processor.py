# backend/core/reports/data_preparation/calculation_data_processor.py
"""
Calculation Data Processor.

This module processes calculation results and engineering data for report generation,
including data validation, formatting, and engineering calculations.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import math

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class CalculationDataProcessor:
    """
    Processes calculation results and engineering data for comprehensive reporting.
    """

    def __init__(self):
        """Initialize the calculation data processor."""
        logger.debug("CalculationDataProcessor initialized")

    def process_heat_loss_calculations(
        self,
        circuits_data: List[Dict[str, Any]],
        environmental_conditions: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """
        Process heat loss calculations for circuits.

        Args:
            circuits_data: Circuit data with pipe information
            environmental_conditions: Environmental conditions for calculations

        Returns:
            List of processed calculation results

        Raises:
            CalculationError: If processing fails
        """
        logger.info(f"Processing heat loss calculations for {len(circuits_data)} circuits")

        try:
            processed_results = []

            for circuit in circuits_data:
                try:
                    # Extract circuit parameters
                    pipe_data = self._extract_pipe_parameters(circuit)
                    
                    # Calculate heat loss
                    heat_loss_result = self._calculate_circuit_heat_loss(
                        pipe_data, environmental_conditions
                    )
                    
                    # Process and format results
                    processed_result = {
                        "circuit_id": circuit.get("id", "Unknown"),
                        "pipe_tag": circuit.get("pipe_tag", "Unknown"),
                        "circuit_length": circuit.get("circuit_length", 0),
                        
                        # Heat loss calculations
                        "heat_loss_per_meter": heat_loss_result["heat_loss_per_meter"],
                        "total_heat_loss": heat_loss_result["total_heat_loss"],
                        "heat_loss_breakdown": heat_loss_result["breakdown"],
                        
                        # Environmental conditions
                        "ambient_temperature": environmental_conditions.get("ambient_temperature", 0),
                        "maintain_temperature": environmental_conditions.get("maintain_temperature", 60),
                        "wind_speed": environmental_conditions.get("wind_speed", 0),
                        
                        # Pipe parameters
                        "pipe_diameter": pipe_data["diameter"],
                        "pipe_material": pipe_data["material"],
                        "insulation_type": pipe_data["insulation_type"],
                        "insulation_thickness": pipe_data["insulation_thickness"],
                        
                        # Calculation metadata
                        "calculation_method": heat_loss_result["method"],
                        "calculation_date": datetime.now().isoformat(),
                        "calculation_valid": heat_loss_result["is_valid"],
                    }
                    
                    processed_results.append(processed_result)

                except Exception as e:
                    logger.error(f"Failed to process circuit {circuit.get('id', 'Unknown')}: {e}")
                    # Add error entry
                    processed_results.append({
                        "circuit_id": circuit.get("id", "Unknown"),
                        "calculation_valid": False,
                        "error": str(e),
                    })

            logger.info(f"Processed {len(processed_results)} calculation results")
            return processed_results

        except Exception as e:
            logger.error(f"Heat loss calculation processing failed: {e}")
            raise CalculationError(f"Heat loss calculation processing failed: {str(e)}")

    def process_power_requirements(
        self,
        circuits_data: List[Dict[str, Any]],
        heat_loss_results: List[Dict[str, Any]],
        safety_factors: Dict[str, float],
    ) -> List[Dict[str, Any]]:
        """
        Process power requirements for circuits.

        Args:
            circuits_data: Circuit data
            heat_loss_results: Heat loss calculation results
            safety_factors: Safety factors for different conditions

        Returns:
            List of processed power requirement results
        """
        logger.info("Processing power requirements")

        try:
            power_results = []

            for circuit in circuits_data:
                circuit_id = circuit.get("id", "Unknown")
                
                # Find corresponding heat loss result
                heat_loss_data = next(
                    (hl for hl in heat_loss_results if hl.get("circuit_id") == circuit_id),
                    {}
                )

                if not heat_loss_data.get("calculation_valid", False):
                    logger.warning(f"Skipping power calculation for circuit {circuit_id} - invalid heat loss data")
                    continue

                try:
                    # Calculate required power
                    power_result = self._calculate_power_requirements(
                        circuit, heat_loss_data, safety_factors
                    )
                    
                    # Select appropriate cable
                    cable_selection = self._select_heat_tracing_cable(
                        power_result["required_power_per_meter"],
                        circuit.get("circuit_length", 0),
                        circuit.get("application_type", "standard")
                    )
                    
                    processed_result = {
                        "circuit_id": circuit_id,
                        "pipe_tag": circuit.get("pipe_tag", "Unknown"),
                        
                        # Power calculations
                        "heat_loss_per_meter": heat_loss_data.get("heat_loss_per_meter", 0),
                        "required_power_per_meter": power_result["required_power_per_meter"],
                        "safety_factor_applied": power_result["safety_factor"],
                        "total_required_power": power_result["total_required_power"],
                        
                        # Cable selection
                        "selected_cable_type": cable_selection["cable_type"],
                        "cable_power_per_meter": cable_selection["power_per_meter"],
                        "cable_voltage": cable_selection["voltage"],
                        "total_installed_power": cable_selection["total_power"],
                        
                        # Performance metrics
                        "power_margin": cable_selection["power_margin"],
                        "power_density_ratio": cable_selection["power_density_ratio"],
                        "selection_valid": cable_selection["is_valid"],
                        
                        # Calculation metadata
                        "calculation_date": datetime.now().isoformat(),
                    }
                    
                    power_results.append(processed_result)

                except Exception as e:
                    logger.error(f"Failed to process power for circuit {circuit_id}: {e}")

            logger.info(f"Processed power requirements for {len(power_results)} circuits")
            return power_results

        except Exception as e:
            logger.error(f"Power requirements processing failed: {e}")
            raise CalculationError(f"Power requirements processing failed: {str(e)}")

    def process_electrical_load_analysis(
        self,
        circuits_data: List[Dict[str, Any]],
        power_results: List[Dict[str, Any]],
        panel_data: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Process electrical load analysis.

        Args:
            circuits_data: Circuit data
            power_results: Power calculation results
            panel_data: Electrical panel data

        Returns:
            Dict with electrical load analysis results
        """
        logger.info("Processing electrical load analysis")

        try:
            # Group circuits by panel
            circuits_by_panel = {}
            for circuit in circuits_data:
                panel_id = circuit.get("panel_id", "Unknown")
                if panel_id not in circuits_by_panel:
                    circuits_by_panel[panel_id] = []
                circuits_by_panel[panel_id].append(circuit)

            # Calculate loads for each panel
            panel_loads = []
            total_connected_load = 0
            total_operating_load = 0

            for panel_id, panel_circuits in circuits_by_panel.items():
                # Find panel information
                panel_info = next((p for p in panel_data if p.get("id") == panel_id), {})
                
                # Calculate panel loads
                panel_load_result = self._calculate_panel_loads(
                    panel_circuits, power_results, panel_info
                )
                
                panel_loads.append(panel_load_result)
                total_connected_load += panel_load_result["connected_load_kw"]
                total_operating_load += panel_load_result["operating_load_kw"]

            # Calculate diversity factors
            diversity_analysis = self._calculate_diversity_factors(panel_loads)

            # Perform load flow analysis
            load_flow_results = self._perform_load_flow_analysis(panel_loads, panel_data)

            analysis_results = {
                "panel_loads": panel_loads,
                "total_connected_load_kw": total_connected_load,
                "total_operating_load_kw": total_operating_load,
                "diversity_analysis": diversity_analysis,
                "load_flow_results": load_flow_results,
                "analysis_date": datetime.now().isoformat(),
                "total_panels": len(panel_loads),
                "total_circuits": sum(len(circuits) for circuits in circuits_by_panel.values()),
            }

            logger.info(f"Completed electrical load analysis for {len(panel_loads)} panels")
            return analysis_results

        except Exception as e:
            logger.error(f"Electrical load analysis failed: {e}")
            raise CalculationError(f"Electrical load analysis failed: {str(e)}")

    def validate_calculation_results(
        self,
        calculation_results: List[Dict[str, Any]],
        validation_criteria: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Validate calculation results against engineering criteria.

        Args:
            calculation_results: Calculation results to validate
            validation_criteria: Validation criteria and limits

        Returns:
            Dict with validation results
        """
        logger.info("Validating calculation results")

        try:
            validation_results = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "info": [],
                "validated_count": len(calculation_results),
            }

            for result in calculation_results:
                circuit_id = result.get("circuit_id", "Unknown")
                
                # Validate heat loss calculations
                if "heat_loss_per_meter" in result:
                    heat_loss = result["heat_loss_per_meter"]
                    if heat_loss <= 0:
                        validation_results["errors"].append(
                            f"Circuit {circuit_id}: Invalid heat loss value ({heat_loss} W/m)"
                        )
                        validation_results["is_valid"] = False
                    elif heat_loss > validation_criteria.get("max_heat_loss_per_meter", 200):
                        validation_results["warnings"].append(
                            f"Circuit {circuit_id}: High heat loss ({heat_loss:.1f} W/m)"
                        )

                # Validate power requirements
                if "required_power_per_meter" in result:
                    power = result["required_power_per_meter"]
                    if power <= 0:
                        validation_results["errors"].append(
                            f"Circuit {circuit_id}: Invalid power requirement ({power} W/m)"
                        )
                        validation_results["is_valid"] = False

                # Validate safety factors
                if "safety_factor_applied" in result:
                    safety_factor = result["safety_factor_applied"]
                    min_safety = validation_criteria.get("min_safety_factor", 1.1)
                    max_safety = validation_criteria.get("max_safety_factor", 2.0)
                    
                    if safety_factor < min_safety:
                        validation_results["warnings"].append(
                            f"Circuit {circuit_id}: Low safety factor ({safety_factor:.2f})"
                        )
                    elif safety_factor > max_safety:
                        validation_results["warnings"].append(
                            f"Circuit {circuit_id}: High safety factor ({safety_factor:.2f})"
                        )

            logger.info(f"Validation completed: {len(validation_results['errors'])} errors, {len(validation_results['warnings'])} warnings")
            return validation_results

        except Exception as e:
            logger.error(f"Calculation validation failed: {e}")
            raise CalculationError(f"Calculation validation failed: {str(e)}")

    def _extract_pipe_parameters(self, circuit: Dict[str, Any]) -> Dict[str, Any]:
        """Extract pipe parameters from circuit data."""
        return {
            "diameter": circuit.get("pipe_diameter", 0.1),  # Default 100mm
            "material": circuit.get("pipe_material", "carbon_steel"),
            "insulation_type": circuit.get("insulation_type", "mineral_wool"),
            "insulation_thickness": circuit.get("insulation_thickness", 50),  # Default 50mm
            "length": circuit.get("circuit_length", 0),
        }

    def _calculate_circuit_heat_loss(
        self,
        pipe_data: Dict[str, Any],
        environmental_conditions: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Calculate heat loss for a circuit."""
        try:
            # Temperature difference
            temp_diff = (
                environmental_conditions.get("maintain_temperature", 60) -
                environmental_conditions.get("ambient_temperature", 0)
            )

            # Simplified heat loss calculation (W/m)
            # This would use the actual heat loss calculation from the calculations module
            diameter = pipe_data["diameter"]
            insulation_thickness = pipe_data["insulation_thickness"] / 1000  # Convert to meters
            
            # Thermal resistances (simplified)
            r_insulation = insulation_thickness / (0.04 * math.pi * diameter)  # Simplified
            r_convection = 1 / (10 * math.pi * diameter)  # Simplified convection
            
            total_resistance = r_insulation + r_convection
            heat_loss_per_meter = temp_diff / total_resistance

            total_heat_loss = heat_loss_per_meter * pipe_data["length"]

            return {
                "heat_loss_per_meter": heat_loss_per_meter,
                "total_heat_loss": total_heat_loss,
                "method": "simplified_thermal_resistance",
                "is_valid": heat_loss_per_meter > 0,
                "breakdown": {
                    "temperature_difference": temp_diff,
                    "thermal_resistance": total_resistance,
                    "insulation_resistance": r_insulation,
                    "convection_resistance": r_convection,
                },
            }

        except Exception as e:
            logger.error(f"Heat loss calculation failed: {e}")
            return {
                "heat_loss_per_meter": 0,
                "total_heat_loss": 0,
                "method": "error",
                "is_valid": False,
                "error": str(e),
            }

    def _calculate_power_requirements(
        self,
        circuit: Dict[str, Any],
        heat_loss_data: Dict[str, Any],
        safety_factors: Dict[str, float],
    ) -> Dict[str, Any]:
        """Calculate power requirements for a circuit."""
        heat_loss_per_meter = heat_loss_data.get("heat_loss_per_meter", 0)
        circuit_length = circuit.get("circuit_length", 0)
        
        # Determine safety factor based on application
        application_type = circuit.get("application_type", "standard")
        safety_factor = safety_factors.get(application_type, 1.3)
        
        # Calculate required power
        required_power_per_meter = heat_loss_per_meter * safety_factor
        total_required_power = required_power_per_meter * circuit_length

        return {
            "required_power_per_meter": required_power_per_meter,
            "total_required_power": total_required_power,
            "safety_factor": safety_factor,
            "application_type": application_type,
        }

    def _select_heat_tracing_cable(
        self,
        required_power_per_meter: float,
        circuit_length: float,
        application_type: str,
    ) -> Dict[str, Any]:
        """Select appropriate heat tracing cable."""
        # Simplified cable selection (would use actual cable database)
        standard_cables = [
            {"type": "Self-Regulating 10W/m", "power": 10, "voltage": 240},
            {"type": "Self-Regulating 16W/m", "power": 16, "voltage": 240},
            {"type": "Self-Regulating 20W/m", "power": 20, "voltage": 240},
            {"type": "Self-Regulating 25W/m", "power": 25, "voltage": 240},
            {"type": "Self-Regulating 30W/m", "power": 30, "voltage": 240},
        ]

        # Find suitable cable
        selected_cable = None
        for cable in standard_cables:
            if cable["power"] >= required_power_per_meter:
                selected_cable = cable
                break

        if not selected_cable:
            # Use highest power cable if none sufficient
            selected_cable = standard_cables[-1]

        total_power = selected_cable["power"] * circuit_length
        power_margin = selected_cable["power"] - required_power_per_meter
        power_density_ratio = selected_cable["power"] / required_power_per_meter

        return {
            "cable_type": selected_cable["type"],
            "power_per_meter": selected_cable["power"],
            "voltage": selected_cable["voltage"],
            "total_power": total_power,
            "power_margin": power_margin,
            "power_density_ratio": power_density_ratio,
            "is_valid": power_margin >= 0,
        }

    def _calculate_panel_loads(
        self,
        panel_circuits: List[Dict[str, Any]],
        power_results: List[Dict[str, Any]],
        panel_info: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Calculate loads for a panel."""
        panel_id = panel_info.get("id", "Unknown")
        
        # Sum up circuit powers
        connected_load = 0
        for circuit in panel_circuits:
            circuit_id = circuit.get("id")
            power_data = next(
                (pr for pr in power_results if pr.get("circuit_id") == circuit_id),
                {}
            )
            connected_load += power_data.get("total_installed_power", 0)

        # Convert to kW
        connected_load_kw = connected_load / 1000

        # Apply load factor
        load_factor = panel_info.get("load_factor", 0.8)
        operating_load_kw = connected_load_kw * load_factor

        return {
            "panel_id": panel_id,
            "panel_name": panel_info.get("name", panel_id),
            "circuit_count": len(panel_circuits),
            "connected_load_kw": connected_load_kw,
            "operating_load_kw": operating_load_kw,
            "load_factor": load_factor,
            "peak_demand_kw": connected_load_kw,  # Assume worst case
        }

    def _calculate_diversity_factors(self, panel_loads: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate diversity factors for the system."""
        total_connected = sum(panel["connected_load_kw"] for panel in panel_loads)
        total_operating = sum(panel["operating_load_kw"] for panel in panel_loads)
        
        diversity_factor = total_operating / total_connected if total_connected > 0 else 0

        return {
            "system_diversity_factor": diversity_factor,
            "total_connected_load_kw": total_connected,
            "total_operating_load_kw": total_operating,
            "load_reduction_kw": total_connected - total_operating,
            "load_reduction_percent": (1 - diversity_factor) * 100,
        }

    def _perform_load_flow_analysis(
        self,
        panel_loads: List[Dict[str, Any]],
        panel_data: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Perform basic load flow analysis."""
        # Simplified load flow analysis
        total_load = sum(panel["operating_load_kw"] for panel in panel_loads)
        
        # Check panel capacities
        capacity_analysis = []
        for panel_load in panel_loads:
            panel_id = panel_load["panel_id"]
            panel_info = next((p for p in panel_data if p.get("id") == panel_id), {})
            
            capacity = panel_info.get("capacity_kw", 100)  # Default 100kW
            utilization = panel_load["operating_load_kw"] / capacity * 100
            
            capacity_analysis.append({
                "panel_id": panel_id,
                "capacity_kw": capacity,
                "load_kw": panel_load["operating_load_kw"],
                "utilization_percent": utilization,
                "available_capacity_kw": capacity - panel_load["operating_load_kw"],
                "is_overloaded": utilization > 80,  # 80% threshold
            })

        return {
            "total_system_load_kw": total_load,
            "panel_capacity_analysis": capacity_analysis,
            "overloaded_panels": [ca for ca in capacity_analysis if ca["is_overloaded"]],
            "system_utilization_percent": total_load / sum(ca["capacity_kw"] for ca in capacity_analysis) * 100,
        }
