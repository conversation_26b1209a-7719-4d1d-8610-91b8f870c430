# backend/core/reports/data_preparation/export_data_formatter.py
"""
Export Data Formatter.

This module formats data for various export formats including Excel, CSV, and JSON,
with proper formatting, styling, and data organization.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pathlib import Path
import json
import csv

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ExportDataFormatter:
    """
    Formats data for export to various file formats with proper styling and organization.
    """

    def __init__(self):
        """Initialize the export data formatter."""
        logger.debug("ExportDataFormatter initialized")

    def format_for_excel_export(
        self,
        data: Dict[str, Any],
        export_type: str,
        include_formatting: bool = True,
    ) -> Dict[str, Any]:
        """
        Format data for Excel export with proper structure and formatting.

        Args:
            data: Data to format
            export_type: Type of export (calculation_report, cable_schedule, etc.)
            include_formatting: Whether to include Excel formatting

        Returns:
            Dict with formatted data for Excel export

        Raises:
            CalculationError: If formatting fails
        """
        logger.debug(f"Formatting data for Excel export: {export_type}")

        try:
            if export_type == "calculation_report":
                return self._format_calculation_report_excel(data, include_formatting)
            elif export_type == "cable_schedule":
                return self._format_cable_schedule_excel(data, include_formatting)
            elif export_type == "load_summary":
                return self._format_load_summary_excel(data, include_formatting)
            elif export_type == "material_takeoff":
                return self._format_material_takeoff_excel(data, include_formatting)
            else:
                raise InvalidInputError(f"Unknown export type: {export_type}")

        except Exception as e:
            logger.error(f"Excel formatting failed: {e}")
            raise CalculationError(f"Excel formatting failed: {str(e)}")

    def format_for_csv_export(
        self,
        data: Dict[str, Any],
        export_type: str,
        delimiter: str = ",",
    ) -> List[Dict[str, Any]]:
        """
        Format data for CSV export.

        Args:
            data: Data to format
            export_type: Type of export
            delimiter: CSV delimiter

        Returns:
            List of CSV sheet data

        Raises:
            CalculationError: If formatting fails
        """
        logger.debug(f"Formatting data for CSV export: {export_type}")

        try:
            if export_type == "calculation_report":
                return self._format_calculation_report_csv(data)
            elif export_type == "cable_schedule":
                return self._format_cable_schedule_csv(data)
            elif export_type == "load_summary":
                return self._format_load_summary_csv(data)
            elif export_type == "material_takeoff":
                return self._format_material_takeoff_csv(data)
            else:
                raise InvalidInputError(f"Unknown export type: {export_type}")

        except Exception as e:
            logger.error(f"CSV formatting failed: {e}")
            raise CalculationError(f"CSV formatting failed: {str(e)}")

    def format_for_json_export(
        self,
        data: Dict[str, Any],
        export_type: str,
        pretty_print: bool = True,
    ) -> Dict[str, Any]:
        """
        Format data for JSON export.

        Args:
            data: Data to format
            export_type: Type of export
            pretty_print: Whether to format JSON for readability

        Returns:
            Dict with formatted JSON data

        Raises:
            CalculationError: If formatting fails
        """
        logger.debug(f"Formatting data for JSON export: {export_type}")

        try:
            # Add metadata
            formatted_data = {
                "export_metadata": {
                    "export_type": export_type,
                    "generated_at": datetime.now().isoformat(),
                    "generator": "Ultimate Electrical Designer",
                    "version": "1.0",
                },
                "data": data,
            }

            # Clean data for JSON serialization
            cleaned_data = self._clean_data_for_json(formatted_data)

            logger.debug("Data formatted for JSON export")
            return cleaned_data

        except Exception as e:
            logger.error(f"JSON formatting failed: {e}")
            raise CalculationError(f"JSON formatting failed: {str(e)}")

    def format_summary_statistics(
        self,
        data: Dict[str, Any],
        include_charts_data: bool = False,
    ) -> Dict[str, Any]:
        """
        Format summary statistics for dashboard display.

        Args:
            data: Raw data
            include_charts_data: Whether to include chart data

        Returns:
            Dict with formatted summary statistics
        """
        logger.debug("Formatting summary statistics")

        try:
            summary = {
                "project_overview": self._extract_project_overview(data),
                "key_metrics": self._calculate_key_metrics(data),
                "performance_indicators": self._calculate_performance_indicators(data),
                "summary_tables": self._create_summary_tables(data),
            }

            if include_charts_data:
                summary["charts_data"] = self._prepare_charts_data(data)

            return summary

        except Exception as e:
            logger.error(f"Summary statistics formatting failed: {e}")
            raise CalculationError(f"Summary statistics formatting failed: {str(e)}")

    def _format_calculation_report_excel(
        self,
        data: Dict[str, Any],
        include_formatting: bool,
    ) -> Dict[str, Any]:
        """Format calculation report for Excel export."""
        sheets = {}

        # Project Information Sheet
        sheets["Project_Info"] = {
            "data": [
                ["Project Name", data.get("project_name", "")],
                ["Project Number", data.get("project_number", "")],
                ["Calculation Date", data.get("calculation_date", "")],
                ["Engineer", data.get("engineer_name", "")],
                ["Revision", data.get("revision_number", "")],
            ],
            "headers": ["Parameter", "Value"],
        }

        # Design Parameters Sheet
        design_params = data.get("design_parameters", [])
        sheets["Design_Parameters"] = {
            "data": [
                [param.get("name", ""), param.get("value", ""), param.get("unit", ""), param.get("notes", "")]
                for param in design_params
            ],
            "headers": ["Parameter", "Value", "Unit", "Notes"],
        }

        # Circuits Summary Sheet
        circuits = data.get("circuits", [])
        sheets["Circuits_Summary"] = {
            "data": [
                [
                    circuit.get("circuit_id", ""),
                    circuit.get("pipe_tag", ""),
                    circuit.get("length", 0),
                    circuit.get("heat_loss_per_meter", 0),
                    circuit.get("cable_power_per_meter", 0),
                    circuit.get("total_power", 0),
                    circuit.get("status", ""),
                ]
                for circuit in circuits
            ],
            "headers": ["Circuit ID", "Pipe Tag", "Length (m)", "Heat Loss (W/m)", "Cable Power (W/m)", "Total Power (W)", "Status"],
        }

        # Summary Statistics Sheet
        summary_data = data.get("summary_data", {})
        sheets["Summary"] = {
            "data": [
                ["Total Length (m)", summary_data.get("total_length", 0)],
                ["Total Heat Loss (W)", summary_data.get("total_heat_loss", 0)],
                ["Total Power (W)", summary_data.get("total_power", 0)],
                ["Average Power Density (W/m)", summary_data.get("average_power_density", 0)],
                ["Safety Factor", summary_data.get("safety_factor", 0)],
            ],
            "headers": ["Metric", "Value"],
        }

        if include_formatting:
            # Add formatting information
            for sheet_name, sheet_data in sheets.items():
                sheet_data["formatting"] = self._get_excel_formatting(sheet_name)

        return {"sheets": sheets, "workbook_name": f"{data.get('project_name', 'Project')}_Calculation_Report"}

    def _format_cable_schedule_excel(
        self,
        data: Dict[str, Any],
        include_formatting: bool,
    ) -> Dict[str, Any]:
        """Format cable schedule for Excel export."""
        cables_data = data.get("cables_data", [])
        
        sheet_data = {
            "data": [
                [
                    cable.get("circuit_id", ""),
                    cable.get("pipe_tag", ""),
                    cable.get("cable_type", ""),
                    cable.get("power_per_meter", 0),
                    cable.get("voltage", 0),
                    cable.get("length", 0),
                    cable.get("total_power", 0),
                    cable.get("panel", ""),
                    cable.get("breaker", ""),
                    cable.get("notes", ""),
                ]
                for cable in cables_data
            ],
            "headers": ["Circuit ID", "Pipe Tag", "Cable Type", "Power (W/m)", "Voltage (V)", "Length (m)", "Total Power (W)", "Panel", "Breaker", "Notes"],
        }

        # Add totals row
        if cables_data:
            total_length = sum(cable.get("length", 0) for cable in cables_data)
            total_power = sum(cable.get("total_power", 0) for cable in cables_data)
            
            sheet_data["data"].append([
                "TOTAL", "", "", "", "", total_length, total_power, "", "", ""
            ])

        sheets = {"Cable_Schedule": sheet_data}

        if include_formatting:
            sheets["Cable_Schedule"]["formatting"] = self._get_excel_formatting("Cable_Schedule")

        return {"sheets": sheets, "workbook_name": f"{data.get('project_name', 'Project')}_Cable_Schedule"}

    def _format_load_summary_excel(
        self,
        data: Dict[str, Any],
        include_formatting: bool,
    ) -> Dict[str, Any]:
        """Format load summary for Excel export."""
        load_data = data.get("load_data", [])
        
        sheet_data = {
            "data": [
                [
                    load.get("panel_name", ""),
                    load.get("circuit_count", 0),
                    load.get("connected_load", 0),
                    load.get("operating_load", 0),
                    load.get("load_factor", 0),
                ]
                for load in load_data
            ],
            "headers": ["Panel", "Circuits", "Connected Load (kW)", "Operating Load (kW)", "Load Factor"],
        }

        # Add totals row
        if load_data:
            total_circuits = sum(load.get("circuit_count", 0) for load in load_data)
            total_connected = data.get("total_connected_load", 0)
            total_operating = data.get("total_operating_load", 0)
            overall_factor = total_operating / total_connected if total_connected > 0 else 0
            
            sheet_data["data"].append([
                "TOTAL", total_circuits, total_connected, total_operating, overall_factor
            ])

        sheets = {"Load_Summary": sheet_data}

        if include_formatting:
            sheets["Load_Summary"]["formatting"] = self._get_excel_formatting("Load_Summary")

        return {"sheets": sheets, "workbook_name": f"{data.get('project_name', 'Project')}_Load_Summary"}

    def _format_material_takeoff_excel(
        self,
        data: Dict[str, Any],
        include_formatting: bool,
    ) -> Dict[str, Any]:
        """Format material takeoff for Excel export."""
        materials_data = data.get("materials_data", [])
        
        sheet_data = {
            "data": [],
            "headers": ["Item Code", "Description", "Unit", "Quantity", "Unit Cost", "Total Cost", "Notes"],
        }

        for material in materials_data:
            if material.get("is_category_header", False):
                # Add category header row
                sheet_data["data"].append([material.get("category", ""), "", "", "", "", "", ""])
            else:
                sheet_data["data"].append([
                    material.get("item_code", ""),
                    material.get("description", ""),
                    material.get("unit", ""),
                    material.get("quantity", 0),
                    material.get("unit_cost", 0),
                    material.get("total_cost", 0),
                    material.get("notes", ""),
                ])

        # Add total cost row
        total_cost = data.get("total_cost", 0)
        if total_cost > 0:
            sheet_data["data"].append([
                "", "TOTAL MATERIAL COST", "", "", "", total_cost, ""
            ])

        sheets = {"Material_Takeoff": sheet_data}

        if include_formatting:
            sheets["Material_Takeoff"]["formatting"] = self._get_excel_formatting("Material_Takeoff")

        return {"sheets": sheets, "workbook_name": f"{data.get('project_name', 'Project')}_Material_Takeoff"}

    def _format_calculation_report_csv(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format calculation report for CSV export."""
        csv_files = []

        # Circuits data
        circuits = data.get("circuits", [])
        if circuits:
            csv_files.append({
                "filename": "circuits_summary.csv",
                "data": circuits,
                "headers": ["circuit_id", "pipe_tag", "length", "heat_loss_per_meter", "cable_power_per_meter", "total_power", "status"],
            })

        # Design parameters
        design_params = data.get("design_parameters", [])
        if design_params:
            csv_files.append({
                "filename": "design_parameters.csv",
                "data": design_params,
                "headers": ["name", "value", "unit", "notes"],
            })

        return csv_files

    def _format_cable_schedule_csv(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format cable schedule for CSV export."""
        cables_data = data.get("cables_data", [])
        
        return [{
            "filename": "cable_schedule.csv",
            "data": cables_data,
            "headers": ["circuit_id", "pipe_tag", "cable_type", "power_per_meter", "voltage", "length", "total_power", "panel", "breaker", "notes"],
        }]

    def _format_load_summary_csv(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format load summary for CSV export."""
        load_data = data.get("load_data", [])
        
        return [{
            "filename": "load_summary.csv",
            "data": load_data,
            "headers": ["panel_name", "circuit_count", "connected_load", "operating_load", "load_factor"],
        }]

    def _format_material_takeoff_csv(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format material takeoff for CSV export."""
        # Filter out category headers for CSV
        materials_data = [
            material for material in data.get("materials_data", [])
            if not material.get("is_category_header", False)
        ]
        
        return [{
            "filename": "material_takeoff.csv",
            "data": materials_data,
            "headers": ["item_code", "description", "unit", "quantity", "unit_cost", "total_cost", "notes"],
        }]

    def _get_excel_formatting(self, sheet_name: str) -> Dict[str, Any]:
        """Get Excel formatting configuration for a sheet."""
        base_formatting = {
            "header_style": {
                "font": {"bold": True, "color": "FFFFFF"},
                "fill": {"fgColor": "2C5AA0"},
                "alignment": {"horizontal": "center"},
            },
            "data_style": {
                "alignment": {"horizontal": "left"},
            },
            "number_formats": {
                "currency": "$#,##0.00",
                "decimal": "#,##0.00",
                "integer": "#,##0",
                "percentage": "0.0%",
            },
        }

        # Sheet-specific formatting
        if sheet_name == "Circuits_Summary":
            base_formatting["column_formats"] = {
                "C": "decimal",  # Length
                "D": "decimal",  # Heat Loss
                "E": "decimal",  # Cable Power
                "F": "integer",  # Total Power
            }
        elif sheet_name == "Material_Takeoff":
            base_formatting["column_formats"] = {
                "E": "currency",  # Unit Cost
                "F": "currency",  # Total Cost
            }

        return base_formatting

    def _clean_data_for_json(self, data: Any) -> Any:
        """Clean data for JSON serialization."""
        if isinstance(data, dict):
            return {key: self._clean_data_for_json(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._clean_data_for_json(item) for item in data]
        elif isinstance(data, (int, float, str, bool)) or data is None:
            return data
        elif hasattr(data, 'isoformat'):  # datetime objects
            return data.isoformat()
        else:
            return str(data)

    def _extract_project_overview(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract project overview information."""
        return {
            "project_name": data.get("project_name", "Unknown"),
            "project_number": data.get("project_number", "N/A"),
            "total_circuits": data.get("total_circuits", 0),
            "calculation_date": data.get("calculation_date", ""),
            "engineer": data.get("engineer_name", "Unknown"),
        }

    def _calculate_key_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate key metrics from data."""
        summary_data = data.get("summary_data", {})
        
        return {
            "total_length_m": summary_data.get("total_length", 0),
            "total_power_kw": summary_data.get("total_power", 0) / 1000,
            "average_power_density": summary_data.get("average_power_density", 0),
            "safety_factor": summary_data.get("safety_factor", 0),
            "total_circuits": summary_data.get("circuit_count", 0),
        }

    def _calculate_performance_indicators(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance indicators."""
        circuits = data.get("circuits", [])
        
        if not circuits:
            return {}

        # Calculate statistics
        power_densities = [c.get("cable_power_per_meter", 0) for c in circuits]
        lengths = [c.get("length", 0) for c in circuits]
        
        return {
            "min_power_density": min(power_densities) if power_densities else 0,
            "max_power_density": max(power_densities) if power_densities else 0,
            "avg_circuit_length": sum(lengths) / len(lengths) if lengths else 0,
            "longest_circuit": max(lengths) if lengths else 0,
            "shortest_circuit": min(lengths) if lengths else 0,
        }

    def _create_summary_tables(self, data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Create summary tables for dashboard."""
        circuits = data.get("circuits", [])
        
        # Top 5 longest circuits
        longest_circuits = sorted(circuits, key=lambda x: x.get("length", 0), reverse=True)[:5]
        
        # Top 5 highest power circuits
        highest_power = sorted(circuits, key=lambda x: x.get("total_power", 0), reverse=True)[:5]

        return {
            "longest_circuits": longest_circuits,
            "highest_power_circuits": highest_power,
        }

    def _prepare_charts_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for charts and visualizations."""
        circuits = data.get("circuits", [])
        
        # Power distribution chart data
        power_ranges = {"0-500W": 0, "500-1000W": 0, "1000-2000W": 0, "2000W+": 0}
        
        for circuit in circuits:
            power = circuit.get("total_power", 0)
            if power < 500:
                power_ranges["0-500W"] += 1
            elif power < 1000:
                power_ranges["500-1000W"] += 1
            elif power < 2000:
                power_ranges["1000-2000W"] += 1
            else:
                power_ranges["2000W+"] += 1

        # Length distribution
        length_data = [circuit.get("length", 0) for circuit in circuits]
        
        return {
            "power_distribution": power_ranges,
            "length_histogram": {
                "data": length_data,
                "bins": 10,
            },
            "power_vs_length": [
                {"length": c.get("length", 0), "power": c.get("total_power", 0)}
                for c in circuits
            ],
        }
