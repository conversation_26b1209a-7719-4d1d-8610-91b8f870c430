# backend/core/reports/data_preparation/report_data_aggregator.py
"""
Report Data Aggregator.

This module aggregates and prepares data from various sources for report generation,
including project data, calculations, and catalog information.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from collections import defaultdict

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ReportDataAggregator:
    """
    Aggregates data from multiple sources for comprehensive report generation.
    """

    def __init__(self):
        """Initialize the report data aggregator."""
        logger.debug("ReportDataAggregator initialized")

    def aggregate_heat_tracing_report_data(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
        design_parameters: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Aggregate data for heat tracing calculation report.

        Args:
            project_data: Project information
            circuits_data: Heat tracing circuits data
            calculations_data: Calculation results
            design_parameters: Design parameters and assumptions

        Returns:
            Dict with aggregated report data

        Raises:
            CalculationError: If aggregation fails
        """
        logger.info("Aggregating heat tracing report data")

        try:
            # Validate inputs
            if not project_data:
                raise InvalidInputError("Project data is required")
            
            if not circuits_data:
                raise InvalidInputError("Circuits data is required")

            # Aggregate circuit summary data
            circuit_summary = self._aggregate_circuit_summary(circuits_data, calculations_data)

            # Calculate overall summary statistics
            summary_stats = self._calculate_summary_statistics(circuits_data, calculations_data)

            # Prepare design parameters for display
            formatted_parameters = self._format_design_parameters(design_parameters)

            # Combine all data
            report_data = {
                # Project information
                "project_name": project_data.get("name", "Unknown Project"),
                "project_number": project_data.get("number", "N/A"),
                "project_description": project_data.get("description", ""),
                "calculation_date": datetime.now().strftime('%Y-%m-%d'),
                "engineer_name": project_data.get("engineer", "Unknown"),
                "revision_number": project_data.get("revision", "1"),
                
                # Technical data
                "circuits": circuit_summary,
                "design_parameters": formatted_parameters,
                "summary_data": summary_stats,
                "calculation_results": calculations_data,
                
                # Metadata
                "total_circuits": len(circuits_data),
                "report_generated_at": datetime.now().isoformat(),
            }

            logger.info(f"Aggregated data for {len(circuits_data)} circuits")
            return report_data

        except Exception as e:
            logger.error(f"Heat tracing report data aggregation failed: {e}")
            raise CalculationError(f"Heat tracing report data aggregation failed: {str(e)}")

    def aggregate_cable_schedule_data(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        cable_data: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Aggregate data for cable schedule report.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            cable_data: Cable catalog data

        Returns:
            Dict with aggregated cable schedule data
        """
        logger.info("Aggregating cable schedule data")

        try:
            # Combine circuit and cable information
            cables_with_details = []
            
            for circuit in circuits_data:
                # Find matching cable data
                cable_info = self._find_cable_info(circuit, cable_data)
                
                cable_entry = {
                    "circuit_id": circuit.get("id", "Unknown"),
                    "pipe_tag": circuit.get("pipe_tag", "Unknown"),
                    "cable_type": cable_info.get("name", "Unknown"),
                    "power_per_meter": cable_info.get("power_per_meter", 0),
                    "voltage": cable_info.get("voltage", 0),
                    "length": circuit.get("circuit_length", 0),
                    "total_power": circuit.get("total_power", 0),
                    "panel": circuit.get("panel_id", ""),
                    "breaker": circuit.get("breaker_id", ""),
                    "notes": circuit.get("notes", ""),
                }
                
                cables_with_details.append(cable_entry)

            # Calculate totals
            total_length = sum(cable["length"] for cable in cables_with_details)
            total_power = sum(cable["total_power"] for cable in cables_with_details)

            report_data = {
                "project_name": project_data.get("name", "Unknown Project"),
                "project_number": project_data.get("number", "N/A"),
                "schedule_date": datetime.now().strftime('%Y-%m-%d'),
                "cables_data": cables_with_details,
                "total_length": total_length,
                "total_power": total_power,
                "total_circuits": len(cables_with_details),
            }

            logger.info(f"Aggregated cable schedule for {len(cables_with_details)} circuits")
            return report_data

        except Exception as e:
            logger.error(f"Cable schedule data aggregation failed: {e}")
            raise CalculationError(f"Cable schedule data aggregation failed: {str(e)}")

    def aggregate_load_summary_data(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        panel_data: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Aggregate data for electrical load summary.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            panel_data: Electrical panel data

        Returns:
            Dict with aggregated load summary data
        """
        logger.info("Aggregating load summary data")

        try:
            # Group circuits by panel
            circuits_by_panel = defaultdict(list)
            for circuit in circuits_data:
                panel_id = circuit.get("panel_id", "Unknown")
                circuits_by_panel[panel_id].append(circuit)

            # Calculate loads for each panel
            panel_loads = []
            total_connected_load = 0
            total_operating_load = 0

            for panel_id, panel_circuits in circuits_by_panel.items():
                # Find panel information
                panel_info = next((p for p in panel_data if p.get("id") == panel_id), {})
                
                # Calculate panel loads
                connected_load = sum(circuit.get("total_power", 0) for circuit in panel_circuits) / 1000  # Convert to kW
                operating_load = connected_load * panel_info.get("load_factor", 0.8)  # Apply load factor
                
                panel_load = {
                    "panel_name": panel_info.get("name", panel_id),
                    "circuit_count": len(panel_circuits),
                    "connected_load": connected_load,
                    "operating_load": operating_load,
                    "load_factor": panel_info.get("load_factor", 0.8),
                }
                
                panel_loads.append(panel_load)
                total_connected_load += connected_load
                total_operating_load += operating_load

            report_data = {
                "project_name": project_data.get("name", "Unknown Project"),
                "project_number": project_data.get("number", "N/A"),
                "summary_date": datetime.now().strftime('%Y-%m-%d'),
                "load_data": panel_loads,
                "total_connected_load": total_connected_load,
                "total_operating_load": total_operating_load,
                "diversity_factor": project_data.get("diversity_factor", 0.8),
                "safety_factor": project_data.get("safety_factor", 1.25),
            }

            logger.info(f"Aggregated load summary for {len(panel_loads)} panels")
            return report_data

        except Exception as e:
            logger.error(f"Load summary data aggregation failed: {e}")
            raise CalculationError(f"Load summary data aggregation failed: {str(e)}")

    def aggregate_material_takeoff_data(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        cable_data: List[Dict[str, Any]],
        material_catalog: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Aggregate data for material takeoff report.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            cable_data: Cable data
            material_catalog: Material catalog

        Returns:
            Dict with aggregated material takeoff data
        """
        logger.info("Aggregating material takeoff data")

        try:
            materials_list = []
            total_cost = 0

            # Group materials by category
            material_categories = {
                "Heat Tracing Cables": [],
                "Electrical Components": [],
                "Installation Materials": [],
                "Control & Monitoring": [],
            }

            # Calculate cable requirements
            cable_requirements = self._calculate_cable_requirements(circuits_data, cable_data)
            
            for cable_req in cable_requirements:
                material_entry = {
                    "is_category_header": False,
                    "item_code": cable_req["cable_code"],
                    "description": cable_req["description"],
                    "unit": "m",
                    "quantity": cable_req["total_length"],
                    "unit_cost": cable_req["unit_cost"],
                    "total_cost": cable_req["total_cost"],
                    "notes": cable_req["notes"],
                }
                material_categories["Heat Tracing Cables"].append(material_entry)
                total_cost += cable_req["total_cost"]

            # Add electrical components
            electrical_components = self._calculate_electrical_components(circuits_data)
            for component in electrical_components:
                material_categories["Electrical Components"].append(component)
                total_cost += component.get("total_cost", 0)

            # Add installation materials
            installation_materials = self._calculate_installation_materials(circuits_data)
            for material in installation_materials:
                material_categories["Installation Materials"].append(material)
                total_cost += material.get("total_cost", 0)

            # Format materials list with category headers
            for category, items in material_categories.items():
                if items:  # Only add category if it has items
                    materials_list.append({
                        "is_category_header": True,
                        "category": category,
                    })
                    materials_list.extend(items)

            # Estimate labor hours
            labor_hours = self._estimate_labor_hours(circuits_data)

            report_data = {
                "project_name": project_data.get("name", "Unknown Project"),
                "project_number": project_data.get("number", "N/A"),
                "takeoff_date": datetime.now().strftime('%Y-%m-%d'),
                "materials_data": materials_list,
                "total_cost": total_cost,
                "labor_hours": labor_hours,
                "total_circuits": len(circuits_data),
            }

            logger.info(f"Aggregated material takeoff with {len(materials_list)} items")
            return report_data

        except Exception as e:
            logger.error(f"Material takeoff data aggregation failed: {e}")
            raise CalculationError(f"Material takeoff data aggregation failed: {str(e)}")

    def _aggregate_circuit_summary(
        self,
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """Aggregate circuit summary information."""
        circuit_summary = []
        
        for circuit in circuits_data:
            # Find corresponding calculation data
            calc_data = next(
                (calc for calc in calculations_data if calc.get("circuit_id") == circuit.get("id")),
                {}
            )
            
            summary_entry = {
                "circuit_id": circuit.get("id", "Unknown"),
                "pipe_tag": circuit.get("pipe_tag", "Unknown"),
                "length": circuit.get("circuit_length", 0),
                "heat_loss_per_meter": calc_data.get("heat_loss_per_meter", 0),
                "cable_power_per_meter": circuit.get("power_per_meter", 0),
                "total_power": circuit.get("total_power", 0),
                "status": circuit.get("status", "Unknown"),
            }
            
            circuit_summary.append(summary_entry)
        
        return circuit_summary

    def _calculate_summary_statistics(
        self,
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Calculate overall summary statistics."""
        total_length = sum(circuit.get("circuit_length", 0) for circuit in circuits_data)
        total_power = sum(circuit.get("total_power", 0) for circuit in circuits_data)
        total_heat_loss = sum(calc.get("total_heat_loss", 0) for calc in calculations_data)
        
        average_power_density = total_power / total_length if total_length > 0 else 0
        safety_factor = total_power / total_heat_loss if total_heat_loss > 0 else 1.0

        return {
            "total_length": total_length,
            "total_power": total_power,
            "total_heat_loss": total_heat_loss,
            "average_power_density": average_power_density,
            "safety_factor": safety_factor,
            "circuit_count": len(circuits_data),
        }

    def _format_design_parameters(self, design_parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Format design parameters for display."""
        formatted_params = []
        
        param_mapping = {
            "ambient_temperature": {"name": "Ambient Temperature", "unit": "°C"},
            "maintain_temperature": {"name": "Maintain Temperature", "unit": "°C"},
            "wind_speed": {"name": "Wind Speed", "unit": "m/s"},
            "pipe_material": {"name": "Pipe Material", "unit": "-"},
            "insulation_type": {"name": "Insulation Type", "unit": "-"},
            "insulation_thickness": {"name": "Insulation Thickness", "unit": "mm"},
        }
        
        for key, value in design_parameters.items():
            if key in param_mapping:
                param_info = param_mapping[key]
                formatted_params.append({
                    "name": param_info["name"],
                    "value": str(value),
                    "unit": param_info["unit"],
                    "notes": "",
                })
        
        return formatted_params

    def _find_cable_info(self, circuit: Dict[str, Any], cable_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Find cable information for a circuit."""
        cable_type = circuit.get("cable_type", "")
        
        for cable in cable_data:
            if cable.get("name") == cable_type or cable.get("id") == cable_type:
                return cable
        
        # Return default if not found
        return {
            "name": cable_type or "Unknown Cable",
            "power_per_meter": circuit.get("power_per_meter", 0),
            "voltage": 240,  # Default voltage
        }

    def _calculate_cable_requirements(
        self,
        circuits_data: List[Dict[str, Any]],
        cable_data: List[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """Calculate cable requirements by type."""
        cable_requirements = defaultdict(lambda: {"length": 0, "circuits": []})
        
        # Group circuits by cable type
        for circuit in circuits_data:
            cable_type = circuit.get("cable_type", "Unknown")
            cable_requirements[cable_type]["length"] += circuit.get("circuit_length", 0)
            cable_requirements[cable_type]["circuits"].append(circuit)

        # Format requirements
        requirements = []
        for cable_type, req_data in cable_requirements.items():
            cable_info = self._find_cable_info({"cable_type": cable_type}, cable_data)
            
            requirement = {
                "cable_code": cable_type,
                "description": cable_info.get("description", f"{cable_type} Heat Tracing Cable"),
                "total_length": req_data["length"],
                "unit_cost": cable_info.get("cost_per_meter", 25.0),  # Default cost
                "total_cost": req_data["length"] * cable_info.get("cost_per_meter", 25.0),
                "notes": f"For {len(req_data['circuits'])} circuits",
            }
            
            requirements.append(requirement)
        
        return requirements

    def _calculate_electrical_components(self, circuits_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate electrical components requirements."""
        components = []
        
        # Circuit breakers (one per circuit)
        components.append({
            "is_category_header": False,
            "item_code": "CB-20A",
            "description": "Circuit Breaker 20A, 1-pole",
            "unit": "ea",
            "quantity": len(circuits_data),
            "unit_cost": 45.0,
            "total_cost": len(circuits_data) * 45.0,
            "notes": "One per circuit",
        })
        
        # Contactors (estimate one per 4 circuits)
        contactor_count = max(1, len(circuits_data) // 4)
        components.append({
            "is_category_header": False,
            "item_code": "CONT-30A",
            "description": "Contactor 30A, 3-pole",
            "unit": "ea",
            "quantity": contactor_count,
            "unit_cost": 120.0,
            "total_cost": contactor_count * 120.0,
            "notes": "For circuit control",
        })
        
        return components

    def _calculate_installation_materials(self, circuits_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate installation materials requirements."""
        materials = []
        total_length = sum(circuit.get("circuit_length", 0) for circuit in circuits_data)
        
        # Cable ties (estimate 10 per meter)
        tie_count = int(total_length * 10)
        materials.append({
            "is_category_header": False,
            "item_code": "CT-200",
            "description": "Cable Ties 200mm, UV resistant",
            "unit": "ea",
            "quantity": tie_count,
            "unit_cost": 0.15,
            "total_cost": tie_count * 0.15,
            "notes": "For cable securing",
        })
        
        # Warning tape (estimate 1.5x cable length)
        tape_length = int(total_length * 1.5)
        materials.append({
            "is_category_header": False,
            "item_code": "WT-HT",
            "description": "Warning Tape - Heat Tracing",
            "unit": "m",
            "quantity": tape_length,
            "unit_cost": 0.50,
            "total_cost": tape_length * 0.50,
            "notes": "For identification",
        })
        
        return materials

    def _estimate_labor_hours(self, circuits_data: List[Dict[str, Any]]) -> float:
        """Estimate labor hours for installation."""
        total_length = sum(circuit.get("circuit_length", 0) for circuit in circuits_data)
        
        # Estimate 0.5 hours per meter of cable installation
        installation_hours = total_length * 0.5
        
        # Add electrical connection time (2 hours per circuit)
        connection_hours = len(circuits_data) * 2.0
        
        # Add testing and commissioning (10% of installation time)
        testing_hours = installation_hours * 0.1
        
        total_hours = installation_hours + connection_hours + testing_hours
        
        return round(total_hours, 1)
