# backend/core/reports/document_generator.py
"""
Document Generator.

This module provides the main document generation service that orchestrates
template rendering, data preparation, and output generation for various formats.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import tempfile
from datetime import datetime

from .templates import TemplateMana<PERSON>, DocumentTemplates, TemplateRenderer
from .data_preparation import ReportDataAggregator, CalculationDataProcessor, ExportDataFormatter
from .generators import PDFGenerator, ExcelGenerator, HTMLGenerator
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class DocumentGenerator:
    """
    Main document generation service that orchestrates all report generation components.
    """

    def __init__(self):
        """Initialize the document generator."""
        self.template_manager = TemplateManager()
        self.document_templates = DocumentTemplates()
        self.template_renderer = TemplateRenderer()
        self.data_aggregator = ReportDataAggregator()
        self.data_processor = CalculationDataProcessor()
        self.data_formatter = ExportDataFormatter()
        self.pdf_generator = PDFGenerator()
        self.excel_generator = ExcelGenerator()
        self.html_generator = HTMLGenerator()
        
        logger.debug("DocumentGenerator initialized")

    def generate_heat_tracing_calculation_report(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
        design_parameters: Dict[str, Any],
        output_format: str = "pdf",
        output_path: Optional[str] = None,
        template_name: Optional[str] = None,
    ) -> str:
        """
        Generate comprehensive heat tracing calculation report.

        Args:
            project_data: Project information
            circuits_data: Heat tracing circuits data
            calculations_data: Calculation results
            design_parameters: Design parameters and assumptions
            output_format: Output format (pdf, html, excel)
            output_path: Custom output path (optional)
            template_name: Custom template name (optional)

        Returns:
            Path to generated report file

        Raises:
            CalculationError: If report generation fails
        """
        logger.info(f"Generating heat tracing calculation report in {output_format} format")

        try:
            # Aggregate data for report
            report_data = self.data_aggregator.aggregate_heat_tracing_report_data(
                project_data, circuits_data, calculations_data, design_parameters
            )

            # Get template
            if template_name:
                template_data = self.template_manager.load_template(template_name, "html")
                template_content = template_data["content"]
            else:
                template_data = self.document_templates.get_heat_tracing_calculation_report_template()
                template_content = template_data["content"]

            # Generate output path if not provided
            if not output_path:
                project_name = project_data.get("name", "Project").replace(" ", "_")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{project_name}_Calculation_Report_{timestamp}"
                output_path = str(Path(tempfile.gettempdir()) / f"{filename}.{output_format}")

            # Generate report based on format
            if output_format.lower() == "pdf":
                report_path = self._generate_pdf_report(template_content, report_data, output_path)
            elif output_format.lower() == "html":
                report_path = self._generate_html_report(template_content, report_data, output_path)
            elif output_format.lower() == "excel":
                report_path = self._generate_excel_calculation_report(report_data, output_path)
            else:
                raise InvalidInputError(f"Unsupported output format: {output_format}")

            logger.info(f"Heat tracing calculation report generated: {report_path}")
            return report_path

        except Exception as e:
            logger.error(f"Heat tracing calculation report generation failed: {e}")
            raise CalculationError(f"Heat tracing calculation report generation failed: {str(e)}")

    def generate_cable_schedule_report(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        cable_data: List[Dict[str, Any]],
        output_format: str = "excel",
        output_path: Optional[str] = None,
    ) -> str:
        """
        Generate cable schedule report.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            cable_data: Cable catalog data
            output_format: Output format
            output_path: Custom output path

        Returns:
            Path to generated report file
        """
        logger.info(f"Generating cable schedule report in {output_format} format")

        try:
            # Aggregate data
            report_data = self.data_aggregator.aggregate_cable_schedule_data(
                project_data, circuits_data, cable_data
            )

            # Generate output path if not provided
            if not output_path:
                project_name = project_data.get("name", "Project").replace(" ", "_")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{project_name}_Cable_Schedule_{timestamp}"
                output_path = str(Path(tempfile.gettempdir()) / f"{filename}.{output_format}")

            # Generate report
            if output_format.lower() == "excel":
                report_path = self.excel_generator.create_cable_schedule_excel(report_data, output_path)
            elif output_format.lower() == "html":
                template_data = self.document_templates.get_cable_schedule_template()
                report_path = self._generate_html_report(template_data["content"], report_data, output_path)
            elif output_format.lower() == "pdf":
                template_data = self.document_templates.get_cable_schedule_template()
                report_path = self._generate_pdf_report(template_data["content"], report_data, output_path)
            else:
                raise InvalidInputError(f"Unsupported output format: {output_format}")

            logger.info(f"Cable schedule report generated: {report_path}")
            return report_path

        except Exception as e:
            logger.error(f"Cable schedule report generation failed: {e}")
            raise CalculationError(f"Cable schedule report generation failed: {str(e)}")

    def generate_load_summary_report(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        panel_data: List[Dict[str, Any]],
        output_format: str = "pdf",
        output_path: Optional[str] = None,
    ) -> str:
        """
        Generate electrical load summary report.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            panel_data: Electrical panel data
            output_format: Output format
            output_path: Custom output path

        Returns:
            Path to generated report file
        """
        logger.info(f"Generating load summary report in {output_format} format")

        try:
            # Aggregate data
            report_data = self.data_aggregator.aggregate_load_summary_data(
                project_data, circuits_data, panel_data
            )

            # Generate output path if not provided
            if not output_path:
                project_name = project_data.get("name", "Project").replace(" ", "_")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{project_name}_Load_Summary_{timestamp}"
                output_path = str(Path(tempfile.gettempdir()) / f"{filename}.{output_format}")

            # Generate report
            template_data = self.document_templates.get_load_summary_template()
            
            if output_format.lower() == "pdf":
                report_path = self._generate_pdf_report(template_data["content"], report_data, output_path)
            elif output_format.lower() == "html":
                report_path = self._generate_html_report(template_data["content"], report_data, output_path)
            elif output_format.lower() == "excel":
                formatted_data = self.data_formatter.format_for_excel_export(report_data, "load_summary")
                report_path = self.excel_generator.generate_excel_report(
                    formatted_data["sheets"], output_path, formatted_data["workbook_name"]
                )
            else:
                raise InvalidInputError(f"Unsupported output format: {output_format}")

            logger.info(f"Load summary report generated: {report_path}")
            return report_path

        except Exception as e:
            logger.error(f"Load summary report generation failed: {e}")
            raise CalculationError(f"Load summary report generation failed: {str(e)}")

    def generate_material_takeoff_report(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        cable_data: List[Dict[str, Any]],
        material_catalog: List[Dict[str, Any]],
        output_format: str = "excel",
        output_path: Optional[str] = None,
    ) -> str:
        """
        Generate material takeoff report.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            cable_data: Cable data
            material_catalog: Material catalog
            output_format: Output format
            output_path: Custom output path

        Returns:
            Path to generated report file
        """
        logger.info(f"Generating material takeoff report in {output_format} format")

        try:
            # Aggregate data
            report_data = self.data_aggregator.aggregate_material_takeoff_data(
                project_data, circuits_data, cable_data, material_catalog
            )

            # Generate output path if not provided
            if not output_path:
                project_name = project_data.get("name", "Project").replace(" ", "_")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{project_name}_Material_Takeoff_{timestamp}"
                output_path = str(Path(tempfile.gettempdir()) / f"{filename}.{output_format}")

            # Generate report
            template_data = self.document_templates.get_material_takeoff_template()
            
            if output_format.lower() == "excel":
                formatted_data = self.data_formatter.format_for_excel_export(report_data, "material_takeoff")
                report_path = self.excel_generator.generate_excel_report(
                    formatted_data["sheets"], output_path, formatted_data["workbook_name"]
                )
            elif output_format.lower() == "html":
                report_path = self._generate_html_report(template_data["content"], report_data, output_path)
            elif output_format.lower() == "pdf":
                report_path = self._generate_pdf_report(template_data["content"], report_data, output_path)
            else:
                raise InvalidInputError(f"Unsupported output format: {output_format}")

            logger.info(f"Material takeoff report generated: {report_path}")
            return report_path

        except Exception as e:
            logger.error(f"Material takeoff report generation failed: {e}")
            raise CalculationError(f"Material takeoff report generation failed: {str(e)}")

    def generate_dashboard_report(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
        output_path: Optional[str] = None,
        include_charts: bool = True,
    ) -> str:
        """
        Generate interactive dashboard report.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            calculations_data: Calculation results
            output_path: Custom output path
            include_charts: Whether to include charts

        Returns:
            Path to generated dashboard HTML file
        """
        logger.info("Generating dashboard report")

        try:
            # Aggregate data
            report_data = self.data_aggregator.aggregate_heat_tracing_report_data(
                project_data, circuits_data, calculations_data, {}
            )

            # Add summary statistics
            summary_stats = self.data_formatter.format_summary_statistics(
                report_data, include_charts_data=include_charts
            )
            report_data.update(summary_stats)

            # Generate output path if not provided
            if not output_path:
                project_name = project_data.get("name", "Project").replace(" ", "_")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{project_name}_Dashboard_{timestamp}.html"
                output_path = str(Path(tempfile.gettempdir()) / filename)

            # Generate dashboard
            report_path = self.html_generator.generate_dashboard_html(
                report_data, output_path, include_charts
            )

            logger.info(f"Dashboard report generated: {report_path}")
            return report_path

        except Exception as e:
            logger.error(f"Dashboard report generation failed: {e}")
            raise CalculationError(f"Dashboard report generation failed: {str(e)}")

    def generate_multi_format_report_package(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
        design_parameters: Dict[str, Any],
        output_directory: Optional[str] = None,
        formats: Optional[List[str]] = None,
    ) -> Dict[str, str]:
        """
        Generate complete report package in multiple formats.

        Args:
            project_data: Project information
            circuits_data: Circuits data
            calculations_data: Calculation results
            design_parameters: Design parameters
            output_directory: Output directory
            formats: List of formats to generate

        Returns:
            Dict mapping format names to file paths
        """
        logger.info("Generating multi-format report package")

        try:
            # Set defaults
            if formats is None:
                formats = ["pdf", "html", "excel"]

            if output_directory is None:
                project_name = project_data.get("name", "Project").replace(" ", "_")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_directory = str(Path(tempfile.gettempdir()) / f"{project_name}_Reports_{timestamp}")

            # Create output directory
            output_dir = Path(output_directory)
            output_dir.mkdir(parents=True, exist_ok=True)

            generated_files = {}

            # Generate each format
            for format_type in formats:
                try:
                    if format_type.lower() == "pdf":
                        output_path = output_dir / "calculation_report.pdf"
                        file_path = self.generate_heat_tracing_calculation_report(
                            project_data, circuits_data, calculations_data, design_parameters,
                            "pdf", str(output_path)
                        )
                        generated_files["pdf"] = file_path

                    elif format_type.lower() == "html":
                        output_path = output_dir / "calculation_report.html"
                        file_path = self.generate_heat_tracing_calculation_report(
                            project_data, circuits_data, calculations_data, design_parameters,
                            "html", str(output_path)
                        )
                        generated_files["html"] = file_path

                    elif format_type.lower() == "excel":
                        output_path = output_dir / "calculation_report.xlsx"
                        file_path = self.generate_heat_tracing_calculation_report(
                            project_data, circuits_data, calculations_data, design_parameters,
                            "excel", str(output_path)
                        )
                        generated_files["excel"] = file_path

                    elif format_type.lower() == "dashboard":
                        output_path = output_dir / "dashboard.html"
                        file_path = self.generate_dashboard_report(
                            project_data, circuits_data, calculations_data, str(output_path)
                        )
                        generated_files["dashboard"] = file_path

                except Exception as e:
                    logger.error(f"Failed to generate {format_type} format: {e}")
                    generated_files[format_type] = f"Error: {str(e)}"

            logger.info(f"Multi-format report package generated: {len(generated_files)} files")
            return generated_files

        except Exception as e:
            logger.error(f"Multi-format report package generation failed: {e}")
            raise CalculationError(f"Multi-format report package generation failed: {str(e)}")

    def _generate_pdf_report(self, template_content: str, data: Dict[str, Any], output_path: str) -> str:
        """Generate PDF report from template and data."""
        # Render template
        rendered_html = self.template_renderer.render_template(template_content, data, "html")
        
        # Add header and footer
        html_with_header_footer = self.pdf_generator.add_header_footer(rendered_html)
        
        # Generate PDF
        return self.pdf_generator.generate_pdf_from_html(html_with_header_footer, output_path)

    def _generate_html_report(self, template_content: str, data: Dict[str, Any], output_path: str) -> str:
        """Generate HTML report from template and data."""
        return self.html_generator.generate_html_report(
            template_content, data, output_path, include_interactive=True
        )

    def _generate_excel_calculation_report(self, data: Dict[str, Any], output_path: str) -> str:
        """Generate Excel calculation report."""
        return self.excel_generator.create_calculation_report_excel(data, output_path)

    def get_available_templates(self) -> List[Dict[str, Any]]:
        """
        Get list of available document templates.

        Returns:
            List of available templates with metadata
        """
        logger.debug("Getting available document templates")

        try:
            # Get predefined templates
            predefined_templates = self.document_templates.get_available_templates()
            
            # Get custom templates
            custom_templates = self.template_manager.get_available_templates()

            # Combine and format
            all_templates = []
            
            # Add predefined templates
            for template in predefined_templates:
                template["source"] = "predefined"
                all_templates.append(template)

            # Add custom templates
            for template in custom_templates:
                template["source"] = "custom"
                all_templates.append(template)

            return all_templates

        except Exception as e:
            logger.error(f"Failed to get available templates: {e}")
            return []

    def validate_report_data(
        self,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Validate data for report generation.

        Args:
            project_data: Project data to validate
            circuits_data: Circuits data to validate
            calculations_data: Calculations data to validate

        Returns:
            Dict with validation results
        """
        logger.debug("Validating report data")

        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "data_summary": {
                "project_fields": len(project_data),
                "circuits_count": len(circuits_data),
                "calculations_count": len(calculations_data),
            },
        }

        try:
            # Validate project data
            required_project_fields = ["name", "number"]
            for field in required_project_fields:
                if field not in project_data or not project_data[field]:
                    validation_result["errors"].append(f"Missing required project field: {field}")
                    validation_result["is_valid"] = False

            # Validate circuits data
            if not circuits_data:
                validation_result["warnings"].append("No circuits data provided")
            else:
                required_circuit_fields = ["id", "circuit_length"]
                for i, circuit in enumerate(circuits_data):
                    for field in required_circuit_fields:
                        if field not in circuit or circuit[field] is None:
                            validation_result["errors"].append(
                                f"Missing required field '{field}' in circuit {i+1}"
                            )
                            validation_result["is_valid"] = False

            # Validate calculations data
            if calculations_data:
                # Check if calculation results match circuits
                calc_circuit_ids = {calc.get("circuit_id") for calc in calculations_data}
                circuit_ids = {circuit.get("id") for circuit in circuits_data}
                
                missing_calcs = circuit_ids - calc_circuit_ids
                if missing_calcs:
                    validation_result["warnings"].append(
                        f"Missing calculations for circuits: {list(missing_calcs)}"
                    )

            return validation_result

        except Exception as e:
            logger.error(f"Data validation failed: {e}")
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"Validation error: {str(e)}")
            return validation_result
