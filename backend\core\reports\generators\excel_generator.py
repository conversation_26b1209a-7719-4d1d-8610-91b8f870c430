# backend/core/reports/generators/excel_generator.py
"""
Excel Generator.

This module generates Excel reports with professional formatting,
charts, and multiple worksheets.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ExcelGenerator:
    """
    Generates Excel reports with professional formatting and charts.
    """

    def __init__(self):
        """Initialize the Excel generator."""
        logger.debug("ExcelGenerator initialized")

    def generate_excel_report(
        self,
        sheets_data: Dict[str, Dict[str, Any]],
        output_path: str,
        workbook_name: Optional[str] = None,
        include_charts: bool = True,
    ) -> str:
        """
        Generate Excel report with multiple sheets.

        Args:
            sheets_data: Dict of sheet data with formatting
            output_path: Path to save Excel file
            workbook_name: Name for the workbook
            include_charts: Whether to include charts

        Returns:
            Path to generated Excel file

        Raises:
            CalculationError: If Excel generation fails
        """
        logger.info(f"Generating Excel report: {output_path}")

        try:
            # Try to use openpyxl if available
            try:
                excel_path = self._generate_with_openpyxl(
                    sheets_data, output_path, workbook_name, include_charts
                )
            except ImportError:
                logger.warning("openpyxl not available, using pandas fallback")
                excel_path = self._generate_with_pandas(
                    sheets_data, output_path, workbook_name
                )

            logger.info(f"Excel report generated successfully: {excel_path}")
            return excel_path

        except Exception as e:
            logger.error(f"Excel generation failed: {e}")
            raise CalculationError(f"Excel generation failed: {str(e)}")

    def create_calculation_report_excel(
        self,
        data: Dict[str, Any],
        output_path: str,
    ) -> str:
        """
        Create Excel calculation report with formatted sheets.

        Args:
            data: Report data
            output_path: Output file path

        Returns:
            Path to generated Excel file
        """
        logger.info("Creating Excel calculation report")

        try:
            # Prepare sheets data
            sheets_data = self._prepare_calculation_sheets(data)

            # Generate Excel file
            excel_path = self.generate_excel_report(
                sheets_data, output_path, 
                f"{data.get('project_name', 'Project')}_Calculation_Report"
            )

            return excel_path

        except Exception as e:
            logger.error(f"Calculation report Excel generation failed: {e}")
            raise CalculationError(f"Calculation report Excel generation failed: {str(e)}")

    def create_cable_schedule_excel(
        self,
        data: Dict[str, Any],
        output_path: str,
    ) -> str:
        """
        Create Excel cable schedule with formatting.

        Args:
            data: Cable schedule data
            output_path: Output file path

        Returns:
            Path to generated Excel file
        """
        logger.info("Creating Excel cable schedule")

        try:
            sheets_data = self._prepare_cable_schedule_sheets(data)
            excel_path = self.generate_excel_report(
                sheets_data, output_path,
                f"{data.get('project_name', 'Project')}_Cable_Schedule"
            )

            return excel_path

        except Exception as e:
            logger.error(f"Cable schedule Excel generation failed: {e}")
            raise CalculationError(f"Cable schedule Excel generation failed: {str(e)}")

    def _generate_with_openpyxl(
        self,
        sheets_data: Dict[str, Dict[str, Any]],
        output_path: str,
        workbook_name: Optional[str],
        include_charts: bool,
    ) -> str:
        """Generate Excel using openpyxl."""
        try:
            from openpyxl import Workbook
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            from openpyxl.utils.dataframe import dataframe_to_rows
            from openpyxl.chart import BarChart, Reference
            import pandas as pd

            # Create workbook
            wb = Workbook()
            
            # Remove default sheet
            if "Sheet" in wb.sheetnames:
                wb.remove(wb["Sheet"])

            # Create sheets
            for sheet_name, sheet_data in sheets_data.items():
                ws = wb.create_sheet(title=sheet_name)
                
                # Add data
                self._add_sheet_data_openpyxl(ws, sheet_data)
                
                # Apply formatting
                if "formatting" in sheet_data:
                    self._apply_formatting_openpyxl(ws, sheet_data["formatting"])

                # Add charts if requested
                if include_charts and "chart_data" in sheet_data:
                    self._add_chart_openpyxl(ws, sheet_data["chart_data"])

            # Set workbook properties
            if workbook_name:
                wb.properties.title = workbook_name
            wb.properties.creator = "Ultimate Electrical Designer"
            wb.properties.created = datetime.now()

            # Save file
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            wb.save(output_file)

            return str(output_file)

        except ImportError:
            raise ImportError("openpyxl not available")

    def _generate_with_pandas(
        self,
        sheets_data: Dict[str, Dict[str, Any]],
        output_path: str,
        workbook_name: Optional[str],
    ) -> str:
        """Generate Excel using pandas (fallback)."""
        try:
            import pandas as pd

            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                for sheet_name, sheet_data in sheets_data.items():
                    # Convert data to DataFrame
                    df = self._convert_to_dataframe(sheet_data)
                    
                    # Write to Excel
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

            return str(output_file)

        except ImportError:
            raise ImportError("pandas not available")

    def _add_sheet_data_openpyxl(self, worksheet, sheet_data: Dict[str, Any]):
        """Add data to worksheet using openpyxl."""
        headers = sheet_data.get("headers", [])
        data = sheet_data.get("data", [])

        # Add headers
        if headers:
            for col, header in enumerate(headers, 1):
                worksheet.cell(row=1, column=col, value=header)

        # Add data
        for row_idx, row_data in enumerate(data, 2):
            for col_idx, value in enumerate(row_data, 1):
                worksheet.cell(row=row_idx, column=col_idx, value=value)

    def _apply_formatting_openpyxl(self, worksheet, formatting: Dict[str, Any]):
        """Apply formatting to worksheet using openpyxl."""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment, Border, Side

            # Header formatting
            if "header_style" in formatting:
                header_style = formatting["header_style"]
                
                for cell in worksheet[1]:  # First row
                    if "font" in header_style:
                        font_config = header_style["font"]
                        cell.font = Font(
                            bold=font_config.get("bold", False),
                            color=font_config.get("color", "000000")
                        )
                    
                    if "fill" in header_style:
                        fill_config = header_style["fill"]
                        cell.fill = PatternFill(
                            start_color=fill_config.get("fgColor", "FFFFFF"),
                            end_color=fill_config.get("fgColor", "FFFFFF"),
                            fill_type="solid"
                        )
                    
                    if "alignment" in header_style:
                        align_config = header_style["alignment"]
                        cell.alignment = Alignment(
                            horizontal=align_config.get("horizontal", "left")
                        )

            # Column formatting
            if "column_formats" in formatting:
                for col_letter, format_type in formatting["column_formats"].items():
                    col_range = f"{col_letter}:{col_letter}"
                    
                    for cell in worksheet[col_range]:
                        if isinstance(cell, tuple):
                            for c in cell:
                                self._apply_number_format(c, format_type, formatting)
                        else:
                            self._apply_number_format(cell, format_type, formatting)

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        except Exception as e:
            logger.warning(f"Failed to apply formatting: {e}")

    def _apply_number_format(self, cell, format_type: str, formatting: Dict[str, Any]):
        """Apply number format to cell."""
        number_formats = formatting.get("number_formats", {})
        
        if format_type in number_formats:
            cell.number_format = number_formats[format_type]

    def _add_chart_openpyxl(self, worksheet, chart_data: Dict[str, Any]):
        """Add chart to worksheet using openpyxl."""
        try:
            from openpyxl.chart import BarChart, Reference

            chart_type = chart_data.get("type", "bar")
            
            if chart_type == "bar":
                chart = BarChart()
                chart.title = chart_data.get("title", "Chart")
                
                # Add data
                data_range = chart_data.get("data_range", "B2:B10")
                labels_range = chart_data.get("labels_range", "A2:A10")
                
                data = Reference(worksheet, range_string=data_range)
                labels = Reference(worksheet, range_string=labels_range)
                
                chart.add_data(data, titles_from_data=True)
                chart.set_categories(labels)
                
                # Position chart
                chart_position = chart_data.get("position", "E2")
                worksheet.add_chart(chart, chart_position)

        except Exception as e:
            logger.warning(f"Failed to add chart: {e}")

    def _convert_to_dataframe(self, sheet_data: Dict[str, Any]):
        """Convert sheet data to pandas DataFrame."""
        import pandas as pd
        
        headers = sheet_data.get("headers", [])
        data = sheet_data.get("data", [])
        
        if headers and data:
            return pd.DataFrame(data, columns=headers)
        elif data:
            return pd.DataFrame(data)
        else:
            return pd.DataFrame()

    def _prepare_calculation_sheets(self, data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Prepare sheets data for calculation report."""
        sheets = {}

        # Project Information Sheet
        sheets["Project_Info"] = {
            "headers": ["Parameter", "Value"],
            "data": [
                ["Project Name", data.get("project_name", "")],
                ["Project Number", data.get("project_number", "")],
                ["Calculation Date", data.get("calculation_date", "")],
                ["Engineer", data.get("engineer_name", "")],
                ["Revision", data.get("revision_number", "")],
            ],
            "formatting": self._get_info_sheet_formatting(),
        }

        # Design Parameters Sheet
        design_params = data.get("design_parameters", [])
        if design_params:
            sheets["Design_Parameters"] = {
                "headers": ["Parameter", "Value", "Unit", "Notes"],
                "data": [
                    [p.get("name", ""), p.get("value", ""), p.get("unit", ""), p.get("notes", "")]
                    for p in design_params
                ],
                "formatting": self._get_standard_formatting(),
            }

        # Circuits Summary Sheet
        circuits = data.get("circuits", [])
        if circuits:
            sheets["Circuits_Summary"] = {
                "headers": ["Circuit ID", "Pipe Tag", "Length (m)", "Heat Loss (W/m)", "Cable Power (W/m)", "Total Power (W)", "Status"],
                "data": [
                    [
                        c.get("circuit_id", ""),
                        c.get("pipe_tag", ""),
                        c.get("length", 0),
                        c.get("heat_loss_per_meter", 0),
                        c.get("cable_power_per_meter", 0),
                        c.get("total_power", 0),
                        c.get("status", ""),
                    ]
                    for c in circuits
                ],
                "formatting": self._get_circuits_formatting(),
                "chart_data": {
                    "type": "bar",
                    "title": "Circuit Power Distribution",
                    "data_range": "F2:F" + str(len(circuits) + 1),
                    "labels_range": "A2:A" + str(len(circuits) + 1),
                    "position": "H2",
                },
            }

        # Summary Statistics Sheet
        summary_data = data.get("summary_data", {})
        sheets["Summary"] = {
            "headers": ["Metric", "Value", "Unit"],
            "data": [
                ["Total Length", summary_data.get("total_length", 0), "m"],
                ["Total Heat Loss", summary_data.get("total_heat_loss", 0), "W"],
                ["Total Power", summary_data.get("total_power", 0), "W"],
                ["Average Power Density", summary_data.get("average_power_density", 0), "W/m"],
                ["Safety Factor", summary_data.get("safety_factor", 0), "-"],
                ["Circuit Count", summary_data.get("circuit_count", 0), "ea"],
            ],
            "formatting": self._get_summary_formatting(),
        }

        return sheets

    def _prepare_cable_schedule_sheets(self, data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Prepare sheets data for cable schedule."""
        cables_data = data.get("cables_data", [])
        
        sheet_data = [
            [
                cable.get("circuit_id", ""),
                cable.get("pipe_tag", ""),
                cable.get("cable_type", ""),
                cable.get("power_per_meter", 0),
                cable.get("voltage", 0),
                cable.get("length", 0),
                cable.get("total_power", 0),
                cable.get("panel", ""),
                cable.get("breaker", ""),
                cable.get("notes", ""),
            ]
            for cable in cables_data
        ]

        # Add totals row
        if cables_data:
            total_length = sum(cable.get("length", 0) for cable in cables_data)
            total_power = sum(cable.get("total_power", 0) for cable in cables_data)
            
            sheet_data.append([
                "TOTAL", "", "", "", "", total_length, total_power, "", "", ""
            ])

        return {
            "Cable_Schedule": {
                "headers": ["Circuit ID", "Pipe Tag", "Cable Type", "Power (W/m)", "Voltage (V)", "Length (m)", "Total Power (W)", "Panel", "Breaker", "Notes"],
                "data": sheet_data,
                "formatting": self._get_cable_schedule_formatting(),
            }
        }

    def _get_standard_formatting(self) -> Dict[str, Any]:
        """Get standard formatting configuration."""
        return {
            "header_style": {
                "font": {"bold": True, "color": "FFFFFF"},
                "fill": {"fgColor": "2C5AA0"},
                "alignment": {"horizontal": "center"},
            },
            "number_formats": {
                "currency": "$#,##0.00",
                "decimal": "#,##0.00",
                "integer": "#,##0",
                "percentage": "0.0%",
            },
        }

    def _get_info_sheet_formatting(self) -> Dict[str, Any]:
        """Get formatting for info sheet."""
        formatting = self._get_standard_formatting()
        formatting["column_formats"] = {}
        return formatting

    def _get_circuits_formatting(self) -> Dict[str, Any]:
        """Get formatting for circuits sheet."""
        formatting = self._get_standard_formatting()
        formatting["column_formats"] = {
            "C": "decimal",  # Length
            "D": "decimal",  # Heat Loss
            "E": "decimal",  # Cable Power
            "F": "integer",  # Total Power
        }
        return formatting

    def _get_summary_formatting(self) -> Dict[str, Any]:
        """Get formatting for summary sheet."""
        formatting = self._get_standard_formatting()
        formatting["column_formats"] = {
            "B": "decimal",  # Values
        }
        return formatting

    def _get_cable_schedule_formatting(self) -> Dict[str, Any]:
        """Get formatting for cable schedule."""
        formatting = self._get_standard_formatting()
        formatting["column_formats"] = {
            "D": "decimal",  # Power per meter
            "E": "integer",  # Voltage
            "F": "decimal",  # Length
            "G": "integer",  # Total Power
        }
        return formatting
