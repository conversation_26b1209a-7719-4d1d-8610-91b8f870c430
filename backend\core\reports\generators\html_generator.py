# backend/core/reports/generators/html_generator.py
"""
HTML Generator.

This module generates HTML reports with responsive design and
interactive features for web display.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from datetime import datetime

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class HTMLGenerator:
    """
    Generates HTML reports with responsive design and interactive features.
    """

    def __init__(self):
        """Initialize the HTML generator."""
        self.base_css = self._get_base_css()
        self.base_js = self._get_base_js()
        logger.debug("HTMLGenerator initialized")

    def generate_html_report(
        self,
        template_content: str,
        data: Dict[str, Any],
        output_path: str,
        include_interactive: bool = True,
        custom_css: Optional[str] = None,
        custom_js: Optional[str] = None,
    ) -> str:
        """
        Generate HTML report from template and data.

        Args:
            template_content: HTML template content
            data: Data to render in template
            output_path: Path to save HTML file
            include_interactive: Whether to include interactive features
            custom_css: Custom CSS content
            custom_js: Custom JavaScript content

        Returns:
            Path to generated HTML file

        Raises:
            CalculationError: If HTML generation fails
        """
        logger.info(f"Generating HTML report: {output_path}")

        try:
            # Render template with data
            from ..templates.template_renderer import TemplateRenderer
            renderer = TemplateRenderer()
            
            rendered_content = renderer.render_template(template_content, data, "html")

            # Create complete HTML document
            complete_html = self._create_complete_html(
                rendered_content, data, include_interactive, custom_css, custom_js
            )

            # Save to file
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(complete_html)

            logger.info(f"HTML report generated successfully: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"HTML generation failed: {e}")
            raise CalculationError(f"HTML generation failed: {str(e)}")

    def generate_dashboard_html(
        self,
        data: Dict[str, Any],
        output_path: str,
        include_charts: bool = True,
    ) -> str:
        """
        Generate interactive dashboard HTML.

        Args:
            data: Dashboard data
            output_path: Path to save HTML file
            include_charts: Whether to include charts

        Returns:
            Path to generated HTML file
        """
        logger.info("Generating dashboard HTML")

        try:
            # Create dashboard template
            dashboard_template = self._create_dashboard_template(include_charts)

            # Generate HTML
            html_path = self.generate_html_report(
                dashboard_template, data, output_path, 
                include_interactive=True
            )

            return html_path

        except Exception as e:
            logger.error(f"Dashboard HTML generation failed: {e}")
            raise CalculationError(f"Dashboard HTML generation failed: {str(e)}")

    def generate_responsive_report(
        self,
        template_content: str,
        data: Dict[str, Any],
        output_path: str,
        mobile_optimized: bool = True,
    ) -> str:
        """
        Generate responsive HTML report optimized for different screen sizes.

        Args:
            template_content: HTML template content
            data: Data to render
            output_path: Output file path
            mobile_optimized: Whether to optimize for mobile

        Returns:
            Path to generated HTML file
        """
        logger.info("Generating responsive HTML report")

        try:
            # Add responsive CSS
            responsive_css = self._get_responsive_css(mobile_optimized)

            # Generate HTML with responsive features
            html_path = self.generate_html_report(
                template_content, data, output_path,
                include_interactive=True, custom_css=responsive_css
            )

            return html_path

        except Exception as e:
            logger.error(f"Responsive HTML generation failed: {e}")
            raise CalculationError(f"Responsive HTML generation failed: {str(e)}")

    def create_multi_page_html(
        self,
        pages_data: List[Dict[str, Any]],
        output_directory: str,
        create_index: bool = True,
    ) -> List[str]:
        """
        Create multi-page HTML report with navigation.

        Args:
            pages_data: List of page data
            output_directory: Directory to save HTML files
            create_index: Whether to create index page

        Returns:
            List of generated HTML file paths
        """
        logger.info(f"Creating multi-page HTML with {len(pages_data)} pages")

        try:
            output_dir = Path(output_directory)
            output_dir.mkdir(parents=True, exist_ok=True)

            generated_files = []

            # Generate individual pages
            for i, page_data in enumerate(pages_data):
                page_name = page_data.get("name", f"page_{i+1}")
                page_file = output_dir / f"{page_name}.html"

                # Add navigation data
                nav_data = self._create_navigation_data(pages_data, i)
                page_data["navigation"] = nav_data

                # Generate page
                page_template = page_data.get("template", self._get_default_page_template())
                page_path = self.generate_html_report(
                    page_template, page_data, str(page_file)
                )
                generated_files.append(page_path)

            # Create index page if requested
            if create_index:
                index_path = self._create_index_page(pages_data, output_dir)
                generated_files.insert(0, index_path)

            logger.info(f"Generated {len(generated_files)} HTML files")
            return generated_files

        except Exception as e:
            logger.error(f"Multi-page HTML generation failed: {e}")
            raise CalculationError(f"Multi-page HTML generation failed: {str(e)}")

    def _create_complete_html(
        self,
        content: str,
        data: Dict[str, Any],
        include_interactive: bool,
        custom_css: Optional[str],
        custom_js: Optional[str],
    ) -> str:
        """Create complete HTML document with all assets."""
        title = data.get("project_name", "Report")
        
        # Combine CSS
        css_content = self.base_css
        if custom_css:
            css_content += "\n" + custom_css

        # Combine JavaScript
        js_content = ""
        if include_interactive:
            js_content = self.base_js
        if custom_js:
            js_content += "\n" + custom_js

        # Create complete HTML
        complete_html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        {css_content}
    </style>
</head>
<body>
    {content}
    
    {f'<script>{js_content}</script>' if js_content else ''}
</body>
</html>"""

        return complete_html

    def _create_dashboard_template(self, include_charts: bool) -> str:
        """Create dashboard template."""
        charts_section = """
        <div class="charts-section">
            <h2>Charts & Analytics</h2>
            <div class="charts-grid">
                <div class="chart-container">
                    <canvas id="powerChart"></canvas>
                </div>
                <div class="chart-container">
                    <canvas id="lengthChart"></canvas>
                </div>
            </div>
        </div>
        """ if include_charts else ""

        return f"""
        <div class="dashboard">
            <header class="dashboard-header">
                <h1>{{{{ project_name }}}} - Dashboard</h1>
                <div class="project-info">
                    <span>Project: {{{{ project_number }}}}</span>
                    <span>Date: {{{{ calculation_date }}}}</span>
                </div>
            </header>

            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>Total Circuits</h3>
                    <div class="metric-value">{{{{ total_circuits }}}}</div>
                </div>
                <div class="metric-card">
                    <h3>Total Length</h3>
                    <div class="metric-value">{{{{ summary_data.total_length | round(1) }}}} m</div>
                </div>
                <div class="metric-card">
                    <h3>Total Power</h3>
                    <div class="metric-value">{{{{ (summary_data.total_power / 1000) | round(1) }}}} kW</div>
                </div>
                <div class="metric-card">
                    <h3>Safety Factor</h3>
                    <div class="metric-value">{{{{ summary_data.safety_factor | round(2) }}}}</div>
                </div>
            </div>

            {charts_section}

            <div class="data-tables">
                <h2>Circuit Summary</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Circuit ID</th>
                                <th>Pipe Tag</th>
                                <th>Length (m)</th>
                                <th>Power (W)</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{% for circuit in circuits %}}
                            <tr>
                                <td>{{{{ circuit.circuit_id }}}}</td>
                                <td>{{{{ circuit.pipe_tag }}}}</td>
                                <td>{{{{ circuit.length | round(1) }}}}</td>
                                <td>{{{{ circuit.total_power | round(0) }}}}</td>
                                <td><span class="status-{{{{ circuit.status.lower() }}}}">{{{{ circuit.status }}}}</span></td>
                            </tr>
                            {{% endfor %}}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        """

    def _get_responsive_css(self, mobile_optimized: bool) -> str:
        """Get responsive CSS."""
        base_responsive = """
        @media (max-width: 768px) {
            .project-info {
                grid-template-columns: 1fr !important;
            }
            
            .metrics-grid {
                grid-template-columns: 1fr 1fr !important;
            }
            
            .charts-grid {
                grid-template-columns: 1fr !important;
            }
            
            table {
                font-size: 0.8em;
            }
            
            .table-container {
                overflow-x: auto;
            }
        }
        """

        mobile_specific = """
        @media (max-width: 480px) {
            .metrics-grid {
                grid-template-columns: 1fr !important;
            }
            
            .dashboard-header h1 {
                font-size: 1.5em;
            }
            
            .metric-value {
                font-size: 1.5em;
            }
            
            table {
                font-size: 0.7em;
            }
        }
        """ if mobile_optimized else ""

        return base_responsive + mobile_specific

    def _create_navigation_data(self, pages_data: List[Dict[str, Any]], current_index: int) -> Dict[str, Any]:
        """Create navigation data for multi-page reports."""
        nav_items = []
        
        for i, page_data in enumerate(pages_data):
            nav_items.append({
                "name": page_data.get("title", f"Page {i+1}"),
                "url": f"{page_data.get('name', f'page_{i+1}')}.html",
                "active": i == current_index,
            })

        return {
            "items": nav_items,
            "current_page": current_index + 1,
            "total_pages": len(pages_data),
            "prev_url": f"{pages_data[current_index-1].get('name', f'page_{current_index}')}.html" if current_index > 0 else None,
            "next_url": f"{pages_data[current_index+1].get('name', f'page_{current_index+2}')}.html" if current_index < len(pages_data) - 1 else None,
        }

    def _create_index_page(self, pages_data: List[Dict[str, Any]], output_dir: Path) -> str:
        """Create index page for multi-page report."""
        index_template = """
        <div class="index-page">
            <header>
                <h1>Report Index</h1>
                <p>Generated on: {{ datetime.now().strftime('%Y-%m-%d %H:%M') }}</p>
            </header>
            
            <div class="page-list">
                {% for page in pages %}
                <div class="page-item">
                    <h3><a href="{{ page.url }}">{{ page.title }}</a></h3>
                    <p>{{ page.description or 'No description available' }}</p>
                </div>
                {% endfor %}
            </div>
        </div>
        """

        index_data = {
            "pages": [
                {
                    "title": page.get("title", f"Page {i+1}"),
                    "url": f"{page.get('name', f'page_{i+1}')}.html",
                    "description": page.get("description", ""),
                }
                for i, page in enumerate(pages_data)
            ]
        }

        index_path = output_dir / "index.html"
        return self.generate_html_report(index_template, index_data, str(index_path))

    def _get_default_page_template(self) -> str:
        """Get default page template."""
        return """
        <div class="page">
            {% if navigation %}
            <nav class="page-navigation">
                <div class="nav-links">
                    {% if navigation.prev_url %}
                    <a href="{{ navigation.prev_url }}" class="nav-prev">← Previous</a>
                    {% endif %}
                    
                    <span class="nav-current">Page {{ navigation.current_page }} of {{ navigation.total_pages }}</span>
                    
                    {% if navigation.next_url %}
                    <a href="{{ navigation.next_url }}" class="nav-next">Next →</a>
                    {% endif %}
                </div>
                
                <div class="nav-menu">
                    {% for item in navigation.items %}
                    <a href="{{ item.url }}" class="nav-item {{ 'active' if item.active else '' }}">{{ item.name }}</a>
                    {% endfor %}
                </div>
            </nav>
            {% endif %}
            
            <main class="page-content">
                {{ content | safe }}
            </main>
        </div>
        """

    def _get_base_css(self) -> str:
        """Get base CSS for HTML reports."""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #2c5aa0, #1e3d6f);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .dashboard-header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .project-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-card h3 {
            color: #2c5aa0;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .metric-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #1e3d6f;
        }

        .charts-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 20px;
        }

        .chart-container {
            height: 300px;
        }

        .data-tables {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .table-container {
            overflow-x: auto;
            margin-top: 20px;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .data-table th {
            background-color: #2c5aa0;
            color: white;
            font-weight: bold;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .status-active {
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .status-inactive {
            background-color: #dc3545;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }

        .page-navigation {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .nav-links {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .nav-menu {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .nav-item {
            padding: 8px 15px;
            background-color: #f8f9fa;
            color: #2c5aa0;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        .nav-item:hover,
        .nav-item.active {
            background-color: #2c5aa0;
            color: white;
        }

        .page-content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1, h2, h3 {
            color: #2c5aa0;
            margin-bottom: 15px;
        }

        h2 {
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 10px;
        }
        """

    def _get_base_js(self) -> str:
        """Get base JavaScript for interactive features."""
        return """
        // Interactive features for HTML reports
        document.addEventListener('DOMContentLoaded', function() {
            // Add sorting to tables
            addTableSorting();
            
            // Add search functionality
            addSearchFunctionality();
            
            // Initialize charts if Chart.js is available
            if (typeof Chart !== 'undefined') {
                initializeCharts();
            }
        });

        function addTableSorting() {
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                const headers = table.querySelectorAll('th');
                headers.forEach((header, index) => {
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => sortTable(table, index));
                });
            });
        }

        function sortTable(table, columnIndex) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            const isNumeric = rows.every(row => {
                const cell = row.cells[columnIndex];
                return cell && !isNaN(parseFloat(cell.textContent));
            });

            rows.sort((a, b) => {
                const aVal = a.cells[columnIndex].textContent.trim();
                const bVal = b.cells[columnIndex].textContent.trim();
                
                if (isNumeric) {
                    return parseFloat(aVal) - parseFloat(bVal);
                } else {
                    return aVal.localeCompare(bVal);
                }
            });

            rows.forEach(row => tbody.appendChild(row));
        }

        function addSearchFunctionality() {
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                const searchInput = document.createElement('input');
                searchInput.type = 'text';
                searchInput.placeholder = 'Search table...';
                searchInput.style.marginBottom = '10px';
                searchInput.style.padding = '8px';
                searchInput.style.border = '1px solid #ddd';
                searchInput.style.borderRadius = '4px';
                
                table.parentNode.insertBefore(searchInput, table);
                
                searchInput.addEventListener('input', () => filterTable(table, searchInput.value));
            });
        }

        function filterTable(table, searchTerm) {
            const rows = table.querySelectorAll('tbody tr');
            const term = searchTerm.toLowerCase();
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(term) ? '' : 'none';
            });
        }

        function initializeCharts() {
            // Initialize power chart
            const powerCtx = document.getElementById('powerChart');
            if (powerCtx) {
                new Chart(powerCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Circuit 1', 'Circuit 2', 'Circuit 3'],
                        datasets: [{
                            label: 'Power (W)',
                            data: [500, 750, 300],
                            backgroundColor: '#2c5aa0'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }
        }
        """
