# backend/core/reports/generators/pdf_generator.py
"""
PDF Generator.

This module generates PDF reports from HTML templates using WeasyPrint
or similar PDF generation libraries.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import tempfile
from datetime import datetime

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class PDFGenerator:
    """
    Generates PDF reports from HTML templates with professional styling.
    """

    def __init__(self):
        """Initialize the PDF generator."""
        self.default_css = self._get_default_css()
        logger.debug("PDFGenerator initialized")

    def generate_pdf_from_html(
        self,
        html_content: str,
        output_path: str,
        css_content: Optional[str] = None,
        page_options: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Generate PDF from HTML content.

        Args:
            html_content: HTML content to convert
            output_path: Path to save PDF file
            css_content: Custom CSS content (optional)
            page_options: PDF page options (optional)

        Returns:
            Path to generated PDF file

        Raises:
            CalculationError: If PDF generation fails
        """
        logger.info(f"Generating PDF report: {output_path}")

        try:
            # Validate inputs
            if not html_content.strip():
                raise InvalidInputError("HTML content cannot be empty")

            # Prepare CSS
            css_to_use = css_content or self.default_css

            # Set default page options
            default_options = {
                "page_size": "A4",
                "margin_top": "2cm",
                "margin_bottom": "2cm",
                "margin_left": "2cm",
                "margin_right": "2cm",
                "orientation": "portrait",
            }
            options = {**default_options, **(page_options or {})}

            # Try to use WeasyPrint if available
            try:
                pdf_path = self._generate_with_weasyprint(
                    html_content, css_to_use, output_path, options
                )
            except ImportError:
                logger.warning("WeasyPrint not available, using alternative method")
                pdf_path = self._generate_with_alternative(
                    html_content, css_to_use, output_path, options
                )

            logger.info(f"PDF generated successfully: {pdf_path}")
            return pdf_path

        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            raise CalculationError(f"PDF generation failed: {str(e)}")

    def generate_multi_page_pdf(
        self,
        pages_data: List[Dict[str, Any]],
        output_path: str,
        global_css: Optional[str] = None,
    ) -> str:
        """
        Generate multi-page PDF from multiple HTML pages.

        Args:
            pages_data: List of page data with HTML content
            output_path: Path to save PDF file
            global_css: Global CSS for all pages

        Returns:
            Path to generated PDF file
        """
        logger.info(f"Generating multi-page PDF with {len(pages_data)} pages")

        try:
            # Combine all pages into single HTML
            combined_html = self._combine_pages_html(pages_data, global_css)

            # Generate PDF
            pdf_path = self.generate_pdf_from_html(
                combined_html, output_path, global_css
            )

            return pdf_path

        except Exception as e:
            logger.error(f"Multi-page PDF generation failed: {e}")
            raise CalculationError(f"Multi-page PDF generation failed: {str(e)}")

    def add_header_footer(
        self,
        html_content: str,
        header_content: Optional[str] = None,
        footer_content: Optional[str] = None,
        page_numbers: bool = True,
    ) -> str:
        """
        Add header and footer to HTML content.

        Args:
            html_content: Original HTML content
            header_content: Header HTML content
            footer_content: Footer HTML content
            page_numbers: Whether to include page numbers

        Returns:
            HTML content with header and footer
        """
        logger.debug("Adding header and footer to HTML")

        try:
            # Default header
            if header_content is None:
                header_content = """
                <div class="header">
                    <div class="header-left">Ultimate Electrical Designer</div>
                    <div class="header-right">Heat Tracing Report</div>
                </div>
                """

            # Default footer
            if footer_content is None:
                footer_content = """
                <div class="footer">
                    <div class="footer-left">Generated on: {date}</div>
                    <div class="footer-right">Page <span class="page-number"></span> of <span class="total-pages"></span></div>
                </div>
                """.format(date=datetime.now().strftime('%Y-%m-%d %H:%M'))

            # Add header/footer CSS
            header_footer_css = """
            <style>
            @page {
                @top-center { content: element(header); }
                @bottom-center { content: element(footer); }
            }
            .header {
                position: running(header);
                display: flex;
                justify-content: space-between;
                padding: 10px 0;
                border-bottom: 1px solid #ccc;
                font-size: 12px;
            }
            .footer {
                position: running(footer);
                display: flex;
                justify-content: space-between;
                padding: 10px 0;
                border-top: 1px solid #ccc;
                font-size: 12px;
            }
            .page-number:before { content: counter(page); }
            .total-pages:before { content: counter(pages); }
            </style>
            """

            # Insert header/footer into HTML
            if "<head>" in html_content:
                html_content = html_content.replace(
                    "</head>", f"{header_footer_css}</head>"
                )
            else:
                html_content = f"{header_footer_css}{html_content}"

            if "<body>" in html_content:
                html_content = html_content.replace(
                    "<body>", f"<body>{header_content}"
                )
                html_content = html_content.replace(
                    "</body>", f"{footer_content}</body>"
                )

            return html_content

        except Exception as e:
            logger.error(f"Failed to add header/footer: {e}")
            return html_content  # Return original on error

    def _generate_with_weasyprint(
        self,
        html_content: str,
        css_content: str,
        output_path: str,
        options: Dict[str, Any],
    ) -> str:
        """Generate PDF using WeasyPrint."""
        try:
            from weasyprint import HTML, CSS
            from weasyprint.text.fonts import FontConfiguration

            # Create font configuration
            font_config = FontConfiguration()

            # Create HTML document
            html_doc = HTML(string=html_content)

            # Create CSS
            css_doc = CSS(string=css_content, font_config=font_config)

            # Generate PDF
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            html_doc.write_pdf(
                output_file,
                stylesheets=[css_doc],
                font_config=font_config,
            )

            return str(output_file)

        except ImportError:
            raise ImportError("WeasyPrint not available")

    def _generate_with_alternative(
        self,
        html_content: str,
        css_content: str,
        output_path: str,
        options: Dict[str, Any],
    ) -> str:
        """Generate PDF using alternative method (placeholder)."""
        # This would implement an alternative PDF generation method
        # For now, save as HTML file as fallback
        logger.warning("Using HTML fallback for PDF generation")
        
        output_file = Path(output_path).with_suffix('.html')
        output_file.parent.mkdir(parents=True, exist_ok=True)

        # Combine HTML and CSS
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>{css_content}</style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(full_html)

        return str(output_file)

    def _combine_pages_html(
        self,
        pages_data: List[Dict[str, Any]],
        global_css: Optional[str],
    ) -> str:
        """Combine multiple pages into single HTML document."""
        combined_content = []

        for i, page_data in enumerate(pages_data):
            page_html = page_data.get("html_content", "")
            
            # Add page break before each page (except first)
            if i > 0:
                combined_content.append('<div style="page-break-before: always;"></div>')
            
            combined_content.append(page_html)

        # Create complete HTML document
        full_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Multi-page Report</title>
            <style>
                {global_css or self.default_css}
                .page-break {{ page-break-before: always; }}
            </style>
        </head>
        <body>
            {''.join(combined_content)}
        </body>
        </html>
        """

        return full_html

    def _get_default_css(self) -> str:
        """Get default CSS for PDF generation."""
        return """
        @page {
            size: A4;
            margin: 2cm;
        }

        body {
            font-family: Arial, sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }

        h1, h2, h3, h4, h5, h6 {
            color: #2c5aa0;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }

        h1 { font-size: 18pt; }
        h2 { font-size: 16pt; }
        h3 { font-size: 14pt; }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1em;
            font-size: 10pt;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
            vertical-align: top;
        }

        th {
            background-color: #2c5aa0;
            color: white;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .header {
            text-align: center;
            border-bottom: 2px solid #2c5aa0;
            padding-bottom: 1em;
            margin-bottom: 2em;
        }

        .project-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1em;
            margin-bottom: 2em;
        }

        .info-section {
            border: 1px solid #ddd;
            padding: 1em;
            border-radius: 3px;
        }

        .info-section h3 {
            margin-top: 0;
            color: #2c5aa0;
        }

        .summary-table {
            background-color: #f0f8ff;
        }

        .calculation-section {
            margin-bottom: 2em;
        }

        .calculation-section h2 {
            border-bottom: 1px solid #2c5aa0;
            padding-bottom: 0.3em;
        }

        .signature-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1em;
            margin-top: 3em;
        }

        .signature-box {
            border: 1px solid #ddd;
            padding: 1em;
            text-align: center;
            min-height: 4em;
        }

        .footer {
            margin-top: 3em;
            border-top: 1px solid #ddd;
            padding-top: 1em;
            font-size: 9pt;
            color: #666;
        }

        .page-break {
            page-break-before: always;
        }

        .no-break {
            page-break-inside: avoid;
        }

        /* Print-specific styles */
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
        """

    def get_page_options_template(self) -> Dict[str, Any]:
        """Get template for page options."""
        return {
            "page_size": "A4",  # A4, Letter, Legal, etc.
            "orientation": "portrait",  # portrait, landscape
            "margin_top": "2cm",
            "margin_bottom": "2cm",
            "margin_left": "2cm",
            "margin_right": "2cm",
            "header_height": "1cm",
            "footer_height": "1cm",
            "print_background": True,
            "enable_javascript": False,
        }

    def validate_html_for_pdf(self, html_content: str) -> Dict[str, Any]:
        """
        Validate HTML content for PDF generation.

        Args:
            html_content: HTML content to validate

        Returns:
            Dict with validation results
        """
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": [],
        }

        try:
            # Check for basic HTML structure
            if not html_content.strip():
                validation_result["errors"].append("HTML content is empty")
                validation_result["is_valid"] = False

            # Check for problematic elements
            problematic_elements = ["<script", "<iframe", "<object", "<embed"]
            for element in problematic_elements:
                if element in html_content.lower():
                    validation_result["warnings"].append(
                        f"Found potentially problematic element: {element}"
                    )

            # Check for CSS that might not work in PDF
            if "position: fixed" in html_content.lower():
                validation_result["warnings"].append(
                    "Fixed positioning may not work correctly in PDF"
                )

            # Suggestions for better PDF output
            if "<table" in html_content.lower():
                validation_result["suggestions"].append(
                    "Consider using 'page-break-inside: avoid' for tables"
                )

            return validation_result

        except Exception as e:
            logger.error(f"HTML validation failed: {e}")
            return {
                "is_valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "warnings": [],
                "suggestions": [],
            }
