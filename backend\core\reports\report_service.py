# backend/core/reports/report_service.py
"""
Report Service.

This module provides the main service interface for report generation and export,
orchestrating all reporting functionality with transaction management and caching.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import tempfile
from datetime import datetime, timedelta
import hashlib
import json

from .document_generator import DocumentGenerator
from .data_exporter import DataExporter
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class ReportService:
    """
    Main service for orchestrating all report generation and export operations.
    """

    def __init__(self):
        """Initialize the report service."""
        self.document_generator = DocumentGenerator()
        self.data_exporter = DataExporter()
        self.report_cache = {}
        self.generation_history = []
        logger.debug("ReportService initialized")

    def generate_calculation_report(
        self,
        project_id: str,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
        design_parameters: Dict[str, Any],
        output_format: str = "pdf",
        template_name: Optional[str] = None,
        use_cache: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate heat tracing calculation report with caching and history tracking.

        Args:
            project_id: Project identifier
            project_data: Project information
            circuits_data: Heat tracing circuits data
            calculations_data: Calculation results
            design_parameters: Design parameters and assumptions
            output_format: Output format (pdf, html, excel)
            template_name: Custom template name (optional)
            use_cache: Whether to use cached results

        Returns:
            Dict with generation results and file path

        Raises:
            CalculationError: If report generation fails
        """
        logger.info(f"Generating calculation report for project {project_id} in {output_format} format")

        try:
            # Create cache key
            cache_key = self._create_cache_key(
                "calculation_report", project_id, circuits_data, calculations_data, 
                design_parameters, output_format, template_name
            )

            # Check cache if enabled
            if use_cache and cache_key in self.report_cache:
                cached_result = self.report_cache[cache_key]
                if self._is_cache_valid(cached_result):
                    logger.info("Using cached calculation report")
                    return cached_result

            # Validate input data
            validation_result = self.document_generator.validate_report_data(
                project_data, circuits_data, calculations_data
            )
            
            if not validation_result["is_valid"]:
                raise InvalidInputError(f"Data validation failed: {validation_result['errors']}")

            # Generate report
            start_time = datetime.now()
            
            report_path = self.document_generator.generate_heat_tracing_calculation_report(
                project_data, circuits_data, calculations_data, design_parameters,
                output_format, template_name=template_name
            )

            generation_time = (datetime.now() - start_time).total_seconds()

            # Create result
            result = {
                "success": True,
                "report_type": "calculation_report",
                "project_id": project_id,
                "output_format": output_format,
                "file_path": report_path,
                "file_size": Path(report_path).stat().st_size if Path(report_path).exists() else 0,
                "generation_time_seconds": generation_time,
                "generated_at": datetime.now().isoformat(),
                "data_summary": validation_result["data_summary"],
                "cache_key": cache_key,
            }

            # Cache result
            if use_cache:
                self.report_cache[cache_key] = result

            # Add to history
            self._add_to_history(result)

            logger.info(f"Calculation report generated successfully in {generation_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Calculation report generation failed: {e}")
            error_result = {
                "success": False,
                "report_type": "calculation_report",
                "project_id": project_id,
                "error": str(e),
                "generated_at": datetime.now().isoformat(),
            }
            self._add_to_history(error_result)
            raise CalculationError(f"Calculation report generation failed: {str(e)}")

    def generate_cable_schedule(
        self,
        project_id: str,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        cable_data: List[Dict[str, Any]],
        output_format: str = "excel",
        use_cache: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate cable schedule report.

        Args:
            project_id: Project identifier
            project_data: Project information
            circuits_data: Circuits data
            cable_data: Cable catalog data
            output_format: Output format
            use_cache: Whether to use cached results

        Returns:
            Dict with generation results
        """
        logger.info(f"Generating cable schedule for project {project_id}")

        try:
            # Create cache key
            cache_key = self._create_cache_key(
                "cable_schedule", project_id, circuits_data, cable_data, output_format
            )

            # Check cache
            if use_cache and cache_key in self.report_cache:
                cached_result = self.report_cache[cache_key]
                if self._is_cache_valid(cached_result):
                    logger.info("Using cached cable schedule")
                    return cached_result

            # Generate report
            start_time = datetime.now()
            
            report_path = self.document_generator.generate_cable_schedule_report(
                project_data, circuits_data, cable_data, output_format
            )

            generation_time = (datetime.now() - start_time).total_seconds()

            result = {
                "success": True,
                "report_type": "cable_schedule",
                "project_id": project_id,
                "output_format": output_format,
                "file_path": report_path,
                "file_size": Path(report_path).stat().st_size if Path(report_path).exists() else 0,
                "generation_time_seconds": generation_time,
                "generated_at": datetime.now().isoformat(),
                "data_summary": {
                    "circuits_count": len(circuits_data),
                    "cables_count": len(cable_data),
                },
                "cache_key": cache_key,
            }

            # Cache and track
            if use_cache:
                self.report_cache[cache_key] = result
            self._add_to_history(result)

            logger.info(f"Cable schedule generated successfully in {generation_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Cable schedule generation failed: {e}")
            raise CalculationError(f"Cable schedule generation failed: {str(e)}")

    def generate_dashboard(
        self,
        project_id: str,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
        include_charts: bool = True,
        use_cache: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate interactive dashboard report.

        Args:
            project_id: Project identifier
            project_data: Project information
            circuits_data: Circuits data
            calculations_data: Calculation results
            include_charts: Whether to include charts
            use_cache: Whether to use cached results

        Returns:
            Dict with generation results
        """
        logger.info(f"Generating dashboard for project {project_id}")

        try:
            # Create cache key
            cache_key = self._create_cache_key(
                "dashboard", project_id, circuits_data, calculations_data, include_charts
            )

            # Check cache
            if use_cache and cache_key in self.report_cache:
                cached_result = self.report_cache[cache_key]
                if self._is_cache_valid(cached_result):
                    logger.info("Using cached dashboard")
                    return cached_result

            # Generate dashboard
            start_time = datetime.now()
            
            report_path = self.document_generator.generate_dashboard_report(
                project_data, circuits_data, calculations_data, include_charts=include_charts
            )

            generation_time = (datetime.now() - start_time).total_seconds()

            result = {
                "success": True,
                "report_type": "dashboard",
                "project_id": project_id,
                "output_format": "html",
                "file_path": report_path,
                "file_size": Path(report_path).stat().st_size if Path(report_path).exists() else 0,
                "generation_time_seconds": generation_time,
                "generated_at": datetime.now().isoformat(),
                "include_charts": include_charts,
                "cache_key": cache_key,
            }

            # Cache and track
            if use_cache:
                self.report_cache[cache_key] = result
            self._add_to_history(result)

            logger.info(f"Dashboard generated successfully in {generation_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Dashboard generation failed: {e}")
            raise CalculationError(f"Dashboard generation failed: {str(e)}")

    def export_data(
        self,
        project_id: str,
        data: Dict[str, Any],
        export_type: str,
        format_type: str,
        output_path: Optional[str] = None,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Export project data in specified format.

        Args:
            project_id: Project identifier
            data: Data to export
            export_type: Type of export (calculation_report, cable_schedule, etc.)
            format_type: Export format (csv, json, excel)
            output_path: Custom output path
            filters: Data filters to apply

        Returns:
            Dict with export results
        """
        logger.info(f"Exporting {export_type} data for project {project_id} in {format_type} format")

        try:
            start_time = datetime.now()

            # Apply filters if provided
            if filters:
                export_path = self.data_exporter.export_filtered_data(
                    data, export_type, filters, output_path or self._generate_export_path(project_id, export_type, format_type), format_type
                )
            else:
                # Export based on format
                if format_type.lower() == "csv":
                    export_path = self.data_exporter.export_to_csv(
                        data, export_type, output_path or self._generate_export_path(project_id, export_type, "csv")
                    )
                elif format_type.lower() == "json":
                    export_path = self.data_exporter.export_to_json(
                        data, export_type, output_path or self._generate_export_path(project_id, export_type, "json")
                    )
                elif format_type.lower() == "excel":
                    export_path = self.data_exporter.export_to_excel(
                        data, export_type, output_path or self._generate_export_path(project_id, export_type, "xlsx")
                    )
                else:
                    raise InvalidInputError(f"Unsupported export format: {format_type}")

            export_time = (datetime.now() - start_time).total_seconds()

            result = {
                "success": True,
                "export_type": export_type,
                "project_id": project_id,
                "format": format_type,
                "file_path": export_path,
                "file_size": Path(export_path).stat().st_size if Path(export_path).exists() else 0,
                "export_time_seconds": export_time,
                "exported_at": datetime.now().isoformat(),
                "filters_applied": filters is not None,
            }

            self._add_to_history(result)

            logger.info(f"Data export completed successfully in {export_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Data export failed: {e}")
            raise CalculationError(f"Data export failed: {str(e)}")

    def generate_report_package(
        self,
        project_id: str,
        project_data: Dict[str, Any],
        circuits_data: List[Dict[str, Any]],
        calculations_data: List[Dict[str, Any]],
        design_parameters: Dict[str, Any],
        formats: Optional[List[str]] = None,
        include_dashboard: bool = True,
    ) -> Dict[str, Any]:
        """
        Generate complete report package in multiple formats.

        Args:
            project_id: Project identifier
            project_data: Project information
            circuits_data: Circuits data
            calculations_data: Calculation results
            design_parameters: Design parameters
            formats: List of formats to generate
            include_dashboard: Whether to include dashboard

        Returns:
            Dict with package generation results
        """
        logger.info(f"Generating report package for project {project_id}")

        try:
            start_time = datetime.now()

            # Set default formats
            if formats is None:
                formats = ["pdf", "excel", "html"]

            if include_dashboard:
                formats.append("dashboard")

            # Generate multi-format package
            generated_files = self.document_generator.generate_multi_format_report_package(
                project_data, circuits_data, calculations_data, design_parameters, formats=formats
            )

            generation_time = (datetime.now() - start_time).total_seconds()

            # Calculate total size
            total_size = 0
            successful_files = 0
            
            for format_type, file_path in generated_files.items():
                if not file_path.startswith("Error:"):
                    if Path(file_path).exists():
                        total_size += Path(file_path).stat().st_size
                    successful_files += 1

            result = {
                "success": successful_files > 0,
                "report_type": "report_package",
                "project_id": project_id,
                "generated_files": generated_files,
                "total_files": len(generated_files),
                "successful_files": successful_files,
                "total_size_bytes": total_size,
                "generation_time_seconds": generation_time,
                "generated_at": datetime.now().isoformat(),
            }

            self._add_to_history(result)

            logger.info(f"Report package generated: {successful_files}/{len(generated_files)} files in {generation_time:.2f}s")
            return result

        except Exception as e:
            logger.error(f"Report package generation failed: {e}")
            raise CalculationError(f"Report package generation failed: {str(e)}")

    def get_generation_history(
        self,
        project_id: Optional[str] = None,
        report_type: Optional[str] = None,
        limit: int = 50,
    ) -> List[Dict[str, Any]]:
        """
        Get report generation history with optional filtering.

        Args:
            project_id: Filter by project ID
            report_type: Filter by report type
            limit: Maximum number of records

        Returns:
            List of generation history records
        """
        logger.debug(f"Getting generation history: project_id={project_id}, type={report_type}")

        try:
            filtered_history = self.generation_history

            # Apply filters
            if project_id:
                filtered_history = [h for h in filtered_history if h.get("project_id") == project_id]

            if report_type:
                filtered_history = [h for h in filtered_history if h.get("report_type") == report_type]

            # Sort by generated_at descending and limit
            filtered_history.sort(key=lambda x: x.get("generated_at", ""), reverse=True)
            return filtered_history[:limit]

        except Exception as e:
            logger.error(f"Failed to get generation history: {e}")
            return []

    def clear_cache(self, project_id: Optional[str] = None) -> int:
        """
        Clear report cache.

        Args:
            project_id: Clear cache for specific project only

        Returns:
            Number of cache entries cleared
        """
        logger.info(f"Clearing report cache for project: {project_id or 'all'}")

        try:
            if project_id:
                # Clear cache for specific project
                keys_to_remove = [
                    key for key, value in self.report_cache.items()
                    if value.get("project_id") == project_id
                ]
                for key in keys_to_remove:
                    del self.report_cache[key]
                cleared_count = len(keys_to_remove)
            else:
                # Clear all cache
                cleared_count = len(self.report_cache)
                self.report_cache.clear()

            logger.info(f"Cleared {cleared_count} cache entries")
            return cleared_count

        except Exception as e:
            logger.error(f"Cache clearing failed: {e}")
            return 0

    def get_available_templates(self) -> List[Dict[str, Any]]:
        """
        Get list of available report templates.

        Returns:
            List of available templates
        """
        return self.document_generator.get_available_templates()

    def _create_cache_key(self, *args) -> str:
        """Create cache key from arguments."""
        # Convert arguments to string and hash
        key_string = json.dumps(args, sort_keys=True, default=str)
        return hashlib.md5(key_string.encode()).hexdigest()

    def _is_cache_valid(self, cached_result: Dict[str, Any], max_age_hours: int = 24) -> bool:
        """Check if cached result is still valid."""
        try:
            generated_at = datetime.fromisoformat(cached_result["generated_at"])
            age = datetime.now() - generated_at
            return age < timedelta(hours=max_age_hours)
        except:
            return False

    def _add_to_history(self, result: Dict[str, Any]):
        """Add result to generation history."""
        self.generation_history.append(result)
        
        # Keep only last 1000 entries
        if len(self.generation_history) > 1000:
            self.generation_history = self.generation_history[-1000:]

    def _generate_export_path(self, project_id: str, export_type: str, format_ext: str) -> str:
        """Generate export file path."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{project_id}_{export_type}_{timestamp}.{format_ext}"
        return str(Path(tempfile.gettempdir()) / filename)
