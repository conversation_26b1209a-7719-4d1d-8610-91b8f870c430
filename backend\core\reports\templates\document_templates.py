# backend/core/reports/templates/document_templates.py
"""
Document Templates.

This module provides predefined document templates for common engineering
reports and documentation types.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class DocumentTemplates:
    """
    Provides predefined document templates for engineering reports.
    """

    def __init__(self):
        """Initialize document templates."""
        self.templates = self._load_predefined_templates()
        logger.debug("DocumentTemplates initialized")

    def get_heat_tracing_calculation_report_template(self) -> Dict[str, Any]:
        """
        Get template for heat tracing calculation report.

        Returns:
            Dict containing template data
        """
        return {
            "name": "heat_tracing_calculation_report",
            "type": "html",
            "title": "Heat Tracing Calculation Report",
            "description": "Comprehensive heat tracing design calculations and results",
            "content": self._get_heat_tracing_report_html(),
            "required_variables": [
                "project_name", "project_number", "calculation_date", "engineer_name",
                "circuits", "summary_data", "design_parameters", "calculation_results"
            ],
            "optional_variables": [
                "company_logo", "project_description", "revision_number", "checked_by",
                "approved_by", "notes", "references"
            ],
        }

    def get_cable_schedule_template(self) -> Dict[str, Any]:
        """
        Get template for cable schedule report.

        Returns:
            Dict containing template data
        """
        return {
            "name": "cable_schedule",
            "type": "html", 
            "title": "Heat Tracing Cable Schedule",
            "description": "Detailed schedule of heat tracing cables and circuits",
            "content": self._get_cable_schedule_html(),
            "required_variables": [
                "project_name", "project_number", "schedule_date", "cables_data"
            ],
            "optional_variables": [
                "company_logo", "revision_number", "notes", "total_length", "total_power"
            ],
        }

    def get_load_summary_template(self) -> Dict[str, Any]:
        """
        Get template for electrical load summary.

        Returns:
            Dict containing template data
        """
        return {
            "name": "load_summary",
            "type": "html",
            "title": "Heat Tracing Load Summary",
            "description": "Summary of electrical loads for heat tracing systems",
            "content": self._get_load_summary_html(),
            "required_variables": [
                "project_name", "project_number", "summary_date", "load_data",
                "total_connected_load", "total_operating_load"
            ],
            "optional_variables": [
                "company_logo", "diversity_factor", "safety_factor", "notes"
            ],
        }

    def get_material_takeoff_template(self) -> Dict[str, Any]:
        """
        Get template for material takeoff report.

        Returns:
            Dict containing template data
        """
        return {
            "name": "material_takeoff",
            "type": "html",
            "title": "Heat Tracing Material Takeoff",
            "description": "Bill of materials for heat tracing installation",
            "content": self._get_material_takeoff_html(),
            "required_variables": [
                "project_name", "project_number", "takeoff_date", "materials_data"
            ],
            "optional_variables": [
                "company_logo", "revision_number", "notes", "total_cost", "labor_hours"
            ],
        }

    def get_available_templates(self) -> List[Dict[str, Any]]:
        """
        Get list of all available predefined templates.

        Returns:
            List of template information
        """
        return [
            {
                "name": template["name"],
                "title": template["title"],
                "description": template["description"],
                "type": template["type"],
                "required_variables": len(template["required_variables"]),
                "optional_variables": len(template["optional_variables"]),
            }
            for template in self.templates.values()
        ]

    def get_template_by_name(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        Get template by name.

        Args:
            template_name: Name of the template

        Returns:
            Template data or None if not found
        """
        return self.templates.get(template_name)

    def _load_predefined_templates(self) -> Dict[str, Dict[str, Any]]:
        """Load all predefined templates."""
        templates = {}
        
        # Load each template
        templates["heat_tracing_calculation_report"] = self.get_heat_tracing_calculation_report_template()
        templates["cable_schedule"] = self.get_cable_schedule_template()
        templates["load_summary"] = self.get_load_summary_template()
        templates["material_takeoff"] = self.get_material_takeoff_template()
        
        return templates

    def _get_heat_tracing_report_html(self) -> str:
        """Get HTML template for heat tracing calculation report."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heat Tracing Calculation Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .company-logo {
            max-height: 80px;
            margin-bottom: 10px;
        }
        .project-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-section {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .info-section h3 {
            margin-top: 0;
            color: #2c5aa0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .summary-table {
            background-color: #f9f9f9;
        }
        .calculation-section {
            margin-bottom: 30px;
        }
        .calculation-section h2 {
            color: #2c5aa0;
            border-bottom: 1px solid #2c5aa0;
            padding-bottom: 5px;
        }
        .footer {
            margin-top: 50px;
            border-top: 1px solid #ddd;
            padding-top: 20px;
            font-size: 0.9em;
            color: #666;
        }
        .signature-section {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        .signature-box {
            border: 1px solid #ddd;
            padding: 15px;
            text-align: center;
            min-height: 60px;
        }
    </style>
</head>
<body>
    <div class="header">
        {% if company_logo %}
        <img src="{{ company_logo }}" alt="Company Logo" class="company-logo">
        {% endif %}
        <h1>Heat Tracing Calculation Report</h1>
        <h2>{{ project_name }}</h2>
    </div>

    <div class="project-info">
        <div class="info-section">
            <h3>Project Information</h3>
            <p><strong>Project Name:</strong> {{ project_name }}</p>
            <p><strong>Project Number:</strong> {{ project_number }}</p>
            {% if project_description %}
            <p><strong>Description:</strong> {{ project_description }}</p>
            {% endif %}
            {% if revision_number %}
            <p><strong>Revision:</strong> {{ revision_number }}</p>
            {% endif %}
        </div>
        
        <div class="info-section">
            <h3>Document Information</h3>
            <p><strong>Calculation Date:</strong> {{ calculation_date }}</p>
            <p><strong>Prepared By:</strong> {{ engineer_name }}</p>
            {% if checked_by %}
            <p><strong>Checked By:</strong> {{ checked_by }}</p>
            {% endif %}
            {% if approved_by %}
            <p><strong>Approved By:</strong> {{ approved_by }}</p>
            {% endif %}
        </div>
    </div>

    <div class="calculation-section">
        <h2>Design Parameters</h2>
        <table>
            <thead>
                <tr>
                    <th>Parameter</th>
                    <th>Value</th>
                    <th>Unit</th>
                    <th>Notes</th>
                </tr>
            </thead>
            <tbody>
                {% for param in design_parameters %}
                <tr>
                    <td>{{ param.name }}</td>
                    <td>{{ param.value }}</td>
                    <td>{{ param.unit }}</td>
                    <td>{{ param.notes or '-' }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="calculation-section">
        <h2>Circuit Summary</h2>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>Circuit ID</th>
                    <th>Pipe Tag</th>
                    <th>Length (m)</th>
                    <th>Heat Loss (W/m)</th>
                    <th>Cable Power (W/m)</th>
                    <th>Total Power (W)</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for circuit in circuits %}
                <tr>
                    <td>{{ circuit.circuit_id }}</td>
                    <td>{{ circuit.pipe_tag }}</td>
                    <td>{{ "%.1f"|format(circuit.length) }}</td>
                    <td>{{ "%.1f"|format(circuit.heat_loss_per_meter) }}</td>
                    <td>{{ "%.1f"|format(circuit.cable_power_per_meter) }}</td>
                    <td>{{ "%.0f"|format(circuit.total_power) }}</td>
                    <td>{{ circuit.status }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="calculation-section">
        <h2>Calculation Results Summary</h2>
        <table class="summary-table">
            <thead>
                <tr>
                    <th>Parameter</th>
                    <th>Value</th>
                    <th>Unit</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Total Circuit Length</td>
                    <td>{{ "%.1f"|format(summary_data.total_length) }}</td>
                    <td>m</td>
                </tr>
                <tr>
                    <td>Total Heat Loss</td>
                    <td>{{ "%.0f"|format(summary_data.total_heat_loss) }}</td>
                    <td>W</td>
                </tr>
                <tr>
                    <td>Total Installed Power</td>
                    <td>{{ "%.0f"|format(summary_data.total_power) }}</td>
                    <td>W</td>
                </tr>
                <tr>
                    <td>Average Power Density</td>
                    <td>{{ "%.1f"|format(summary_data.average_power_density) }}</td>
                    <td>W/m</td>
                </tr>
                <tr>
                    <td>Safety Factor</td>
                    <td>{{ "%.2f"|format(summary_data.safety_factor) }}</td>
                    <td>-</td>
                </tr>
            </tbody>
        </table>
    </div>

    {% if notes %}
    <div class="calculation-section">
        <h2>Notes</h2>
        <p>{{ notes }}</p>
    </div>
    {% endif %}

    {% if references %}
    <div class="calculation-section">
        <h2>References</h2>
        <ul>
            {% for reference in references %}
            <li>{{ reference }}</li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}

    <div class="signature-section">
        <div class="signature-box">
            <p><strong>Prepared By:</strong></p>
            <p>{{ engineer_name }}</p>
            <p>Date: {{ calculation_date }}</p>
        </div>
        {% if checked_by %}
        <div class="signature-box">
            <p><strong>Checked By:</strong></p>
            <p>{{ checked_by }}</p>
            <p>Date: _____________</p>
        </div>
        {% endif %}
        {% if approved_by %}
        <div class="signature-box">
            <p><strong>Approved By:</strong></p>
            <p>{{ approved_by }}</p>
            <p>Date: _____________</p>
        </div>
        {% endif %}
    </div>

    <div class="footer">
        <p>This document was generated automatically by the Ultimate Electrical Designer.</p>
        <p>Generated on: {{ datetime.now().strftime('%Y-%m-%d %H:%M:%S') }}</p>
    </div>
</body>
</html>
        """.strip()

    def _get_cable_schedule_html(self) -> str:
        """Get HTML template for cable schedule."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heat Tracing Cable Schedule</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        th {
            background-color: #2c5aa0;
            color: white;
            font-weight: bold;
        }
        .summary-row {
            background-color: #f0f8ff;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            border-top: 1px solid #ddd;
            padding-top: 15px;
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        {% if company_logo %}
        <img src="{{ company_logo }}" alt="Company Logo" style="max-height: 60px; margin-bottom: 10px;">
        {% endif %}
        <h1>Heat Tracing Cable Schedule</h1>
        <h2>{{ project_name }}</h2>
        <p>Project Number: {{ project_number }} | Date: {{ schedule_date }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Circuit ID</th>
                <th>Pipe Tag</th>
                <th>Cable Type</th>
                <th>Power Rating (W/m)</th>
                <th>Voltage (V)</th>
                <th>Length (m)</th>
                <th>Total Power (W)</th>
                <th>Panel</th>
                <th>Breaker</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            {% for cable in cables_data %}
            <tr>
                <td>{{ cable.circuit_id }}</td>
                <td>{{ cable.pipe_tag }}</td>
                <td>{{ cable.cable_type }}</td>
                <td>{{ cable.power_per_meter }}</td>
                <td>{{ cable.voltage }}</td>
                <td>{{ "%.1f"|format(cable.length) }}</td>
                <td>{{ "%.0f"|format(cable.total_power) }}</td>
                <td>{{ cable.panel or '-' }}</td>
                <td>{{ cable.breaker or '-' }}</td>
                <td>{{ cable.notes or '-' }}</td>
            </tr>
            {% endfor %}
            {% if total_length and total_power %}
            <tr class="summary-row">
                <td colspan="5"><strong>TOTAL</strong></td>
                <td><strong>{{ "%.1f"|format(total_length) }}</strong></td>
                <td><strong>{{ "%.0f"|format(total_power) }}</strong></td>
                <td colspan="3"></td>
            </tr>
            {% endif %}
        </tbody>
    </table>

    {% if notes %}
    <div style="margin-top: 20px;">
        <h3>Notes:</h3>
        <p>{{ notes }}</p>
    </div>
    {% endif %}

    <div class="footer">
        <p>Generated by Ultimate Electrical Designer on {{ datetime.now().strftime('%Y-%m-%d %H:%M:%S') }}</p>
        {% if revision_number %}
        <p>Revision: {{ revision_number }}</p>
        {% endif %}
    </div>
</body>
</html>
        """.strip()

    def _get_load_summary_html(self) -> str:
        """Get HTML template for load summary."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heat Tracing Load Summary</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; color: #333; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #2c5aa0; color: white; font-weight: bold; }
        .summary-section { background-color: #f0f8ff; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .total-row { background-color: #e6f3ff; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Heat Tracing Load Summary</h1>
        <h2>{{ project_name }}</h2>
        <p>Project Number: {{ project_number }} | Date: {{ summary_date }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Panel/Distribution</th>
                <th>Circuit Count</th>
                <th>Connected Load (kW)</th>
                <th>Operating Load (kW)</th>
                <th>Load Factor</th>
            </tr>
        </thead>
        <tbody>
            {% for load in load_data %}
            <tr>
                <td>{{ load.panel_name }}</td>
                <td>{{ load.circuit_count }}</td>
                <td>{{ "%.2f"|format(load.connected_load) }}</td>
                <td>{{ "%.2f"|format(load.operating_load) }}</td>
                <td>{{ "%.2f"|format(load.load_factor) }}</td>
            </tr>
            {% endfor %}
            <tr class="total-row">
                <td><strong>TOTAL</strong></td>
                <td><strong>{{ load_data|sum(attribute='circuit_count') }}</strong></td>
                <td><strong>{{ "%.2f"|format(total_connected_load) }}</strong></td>
                <td><strong>{{ "%.2f"|format(total_operating_load) }}</strong></td>
                <td><strong>{{ "%.2f"|format(total_operating_load / total_connected_load) }}</strong></td>
            </tr>
        </tbody>
    </table>

    <div class="summary-section">
        <h3>Load Summary</h3>
        <p><strong>Total Connected Load:</strong> {{ "%.2f"|format(total_connected_load) }} kW</p>
        <p><strong>Total Operating Load:</strong> {{ "%.2f"|format(total_operating_load) }} kW</p>
        {% if diversity_factor %}
        <p><strong>Diversity Factor:</strong> {{ diversity_factor }}</p>
        {% endif %}
        {% if safety_factor %}
        <p><strong>Safety Factor:</strong> {{ safety_factor }}</p>
        {% endif %}
    </div>
</body>
</html>
        """.strip()

    def _get_material_takeoff_html(self) -> str:
        """Get HTML template for material takeoff."""
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Heat Tracing Material Takeoff</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; color: #333; }
        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; font-size: 0.9em; }
        th, td { border: 1px solid #ddd; padding: 6px; text-align: left; }
        th { background-color: #2c5aa0; color: white; font-weight: bold; }
        .category-header { background-color: #f0f8ff; font-weight: bold; }
        .total-row { background-color: #e6f3ff; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Heat Tracing Material Takeoff</h1>
        <h2>{{ project_name }}</h2>
        <p>Project Number: {{ project_number }} | Date: {{ takeoff_date }}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th>Item</th>
                <th>Description</th>
                <th>Unit</th>
                <th>Quantity</th>
                <th>Unit Cost</th>
                <th>Total Cost</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            {% for material in materials_data %}
            {% if material.is_category_header %}
            <tr class="category-header">
                <td colspan="7">{{ material.category }}</td>
            </tr>
            {% else %}
            <tr>
                <td>{{ material.item_code }}</td>
                <td>{{ material.description }}</td>
                <td>{{ material.unit }}</td>
                <td>{{ material.quantity }}</td>
                <td>{{ "${:.2f}"|format(material.unit_cost) if material.unit_cost else '-' }}</td>
                <td>{{ "${:.2f}"|format(material.total_cost) if material.total_cost else '-' }}</td>
                <td>{{ material.notes or '-' }}</td>
            </tr>
            {% endif %}
            {% endfor %}
            {% if total_cost %}
            <tr class="total-row">
                <td colspan="5"><strong>TOTAL MATERIAL COST</strong></td>
                <td><strong>${{ "%.2f"|format(total_cost) }}</strong></td>
                <td></td>
            </tr>
            {% endif %}
        </tbody>
    </table>

    {% if labor_hours %}
    <div style="margin-top: 20px;">
        <h3>Labor Estimate</h3>
        <p><strong>Estimated Labor Hours:</strong> {{ labor_hours }} hours</p>
    </div>
    {% endif %}
</body>
</html>
        """.strip()
