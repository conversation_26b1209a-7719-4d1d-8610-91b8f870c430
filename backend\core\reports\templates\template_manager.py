# backend/core/reports/templates/template_manager.py
"""
Template Manager.

This module manages document templates for report generation,
including template loading, validation, and customization.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import json
from datetime import datetime

from core.errors.exceptions import InvalidIn<PERSON><PERSON>rror, NotFoundError, CalculationError

logger = logging.getLogger(__name__)


class TemplateManager:
    """
    Manages document templates for report generation with validation and customization.
    """

    def __init__(self, templates_directory: Optional[str] = None):
        """
        Initialize the template manager.

        Args:
            templates_directory: Path to templates directory (optional)
        """
        self.templates_directory = Path(templates_directory) if templates_directory else self._get_default_templates_dir()
        self.loaded_templates = {}
        self.template_metadata = {}
        logger.debug(f"TemplateManager initialized with directory: {self.templates_directory}")

    def load_template(self, template_name: str, template_type: str = "html") -> Dict[str, Any]:
        """
        Load a document template.

        Args:
            template_name: Name of the template
            template_type: Type of template (html, markdown, latex)

        Returns:
            Dict containing template content and metadata

        Raises:
            NotFoundError: If template is not found
            CalculationError: If template loading fails
        """
        logger.debug(f"Loading template: {template_name} ({template_type})")

        try:
            template_key = f"{template_name}_{template_type}"
            
            # Check if already loaded
            if template_key in self.loaded_templates:
                return self.loaded_templates[template_key]

            # Find template file
            template_file = self._find_template_file(template_name, template_type)
            
            if not template_file.exists():
                raise NotFoundError(
                    code="404_005",
                    detail=f"Template '{template_name}' not found for type '{template_type}'",
                    category="ClientError",
                    status_code=404,
                )

            # Load template content
            with open(template_file, 'r', encoding='utf-8') as f:
                template_content = f.read()

            # Load metadata if available
            metadata_file = template_file.with_suffix('.json')
            metadata = {}
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)

            template_data = {
                "name": template_name,
                "type": template_type,
                "content": template_content,
                "metadata": metadata,
                "file_path": str(template_file),
                "loaded_at": datetime.utcnow().isoformat(),
            }

            # Cache the loaded template
            self.loaded_templates[template_key] = template_data
            
            logger.debug(f"Template loaded successfully: {template_name}")
            return template_data

        except NotFoundError:
            raise
        except Exception as e:
            logger.error(f"Template loading failed: {e}")
            raise CalculationError(f"Template loading failed: {str(e)}")

    def get_available_templates(self, template_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get list of available templates.

        Args:
            template_type: Filter by template type (optional)

        Returns:
            List of available templates with metadata
        """
        logger.debug(f"Getting available templates for type: {template_type}")

        try:
            templates = []
            
            # Scan templates directory
            if not self.templates_directory.exists():
                logger.warning(f"Templates directory not found: {self.templates_directory}")
                return templates

            # Find template files
            patterns = ["*.html", "*.md", "*.tex", "*.jinja2"]
            if template_type:
                type_extensions = {
                    "html": ["*.html", "*.jinja2"],
                    "markdown": ["*.md"],
                    "latex": ["*.tex"],
                }
                patterns = type_extensions.get(template_type, patterns)

            for pattern in patterns:
                for template_file in self.templates_directory.glob(pattern):
                    template_info = self._get_template_info(template_file)
                    if template_type is None or template_info["type"] == template_type:
                        templates.append(template_info)

            logger.debug(f"Found {len(templates)} available templates")
            return templates

        except Exception as e:
            logger.error(f"Failed to get available templates: {e}")
            raise CalculationError(f"Failed to get available templates: {str(e)}")

    def validate_template(self, template_name: str, template_type: str = "html") -> Dict[str, Any]:
        """
        Validate a template for syntax and required variables.

        Args:
            template_name: Name of the template
            template_type: Type of template

        Returns:
            Dict with validation results
        """
        logger.debug(f"Validating template: {template_name}")

        try:
            # Load template
            template_data = self.load_template(template_name, template_type)
            
            validation_result = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "required_variables": [],
                "optional_variables": [],
            }

            # Validate based on template type
            if template_type == "html":
                validation_result = self._validate_html_template(template_data, validation_result)
            elif template_type == "markdown":
                validation_result = self._validate_markdown_template(template_data, validation_result)
            elif template_type == "latex":
                validation_result = self._validate_latex_template(template_data, validation_result)

            # Extract template variables
            variables = self._extract_template_variables(template_data["content"])
            validation_result["required_variables"] = variables.get("required", [])
            validation_result["optional_variables"] = variables.get("optional", [])

            logger.debug(f"Template validation completed: {validation_result['is_valid']}")
            return validation_result

        except Exception as e:
            logger.error(f"Template validation failed: {e}")
            return {
                "is_valid": False,
                "errors": [str(e)],
                "warnings": [],
                "required_variables": [],
                "optional_variables": [],
            }

    def create_custom_template(
        self,
        template_name: str,
        template_content: str,
        template_type: str = "html",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create a custom template.

        Args:
            template_name: Name for the new template
            template_content: Template content
            template_type: Type of template
            metadata: Template metadata (optional)

        Returns:
            Dict with creation results

        Raises:
            InvalidInputError: If template data is invalid
            CalculationError: If template creation fails
        """
        logger.info(f"Creating custom template: {template_name}")

        try:
            # Validate template name
            if not template_name or not template_name.replace('_', '').replace('-', '').isalnum():
                raise InvalidInputError("Template name must be alphanumeric with underscores/hyphens")

            # Validate template content
            if not template_content.strip():
                raise InvalidInputError("Template content cannot be empty")

            # Create template file path
            extension_map = {
                "html": ".html",
                "markdown": ".md", 
                "latex": ".tex",
            }
            extension = extension_map.get(template_type, ".html")
            template_file = self.templates_directory / f"{template_name}{extension}"

            # Check if template already exists
            if template_file.exists():
                raise InvalidInputError(f"Template '{template_name}' already exists")

            # Ensure templates directory exists
            self.templates_directory.mkdir(parents=True, exist_ok=True)

            # Save template content
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(template_content)

            # Save metadata if provided
            if metadata:
                metadata_file = template_file.with_suffix('.json')
                metadata["created_at"] = datetime.utcnow().isoformat()
                metadata["template_type"] = template_type
                
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2)

            # Validate the created template
            validation_result = self.validate_template(template_name, template_type)

            result = {
                "template_name": template_name,
                "template_type": template_type,
                "file_path": str(template_file),
                "validation": validation_result,
                "created_at": datetime.utcnow().isoformat(),
            }

            logger.info(f"Custom template created successfully: {template_name}")
            return result

        except InvalidInputError:
            raise
        except Exception as e:
            logger.error(f"Custom template creation failed: {e}")
            raise CalculationError(f"Custom template creation failed: {str(e)}")

    def get_template_variables(self, template_name: str, template_type: str = "html") -> Dict[str, List[str]]:
        """
        Get variables used in a template.

        Args:
            template_name: Name of the template
            template_type: Type of template

        Returns:
            Dict with required and optional variables
        """
        logger.debug(f"Getting template variables for: {template_name}")

        try:
            template_data = self.load_template(template_name, template_type)
            variables = self._extract_template_variables(template_data["content"])
            
            logger.debug(f"Found {len(variables.get('required', []))} required and {len(variables.get('optional', []))} optional variables")
            return variables

        except Exception as e:
            logger.error(f"Failed to get template variables: {e}")
            raise CalculationError(f"Failed to get template variables: {str(e)}")

    def _get_default_templates_dir(self) -> Path:
        """Get default templates directory."""
        # In a real implementation, this would be configurable
        return Path(__file__).parent / "default_templates"

    def _find_template_file(self, template_name: str, template_type: str) -> Path:
        """Find template file based on name and type."""
        extension_map = {
            "html": [".html", ".jinja2"],
            "markdown": [".md"],
            "latex": [".tex"],
        }
        
        extensions = extension_map.get(template_type, [".html"])
        
        for ext in extensions:
            template_file = self.templates_directory / f"{template_name}{ext}"
            if template_file.exists():
                return template_file
        
        # Return first extension as default
        return self.templates_directory / f"{template_name}{extensions[0]}"

    def _get_template_info(self, template_file: Path) -> Dict[str, Any]:
        """Get template information from file."""
        name = template_file.stem
        
        # Determine type from extension
        type_map = {
            ".html": "html",
            ".jinja2": "html",
            ".md": "markdown",
            ".tex": "latex",
        }
        template_type = type_map.get(template_file.suffix, "unknown")

        # Load metadata if available
        metadata_file = template_file.with_suffix('.json')
        metadata = {}
        if metadata_file.exists():
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            except Exception:
                pass

        return {
            "name": name,
            "type": template_type,
            "file_path": str(template_file),
            "metadata": metadata,
            "size": template_file.stat().st_size,
            "modified_at": datetime.fromtimestamp(template_file.stat().st_mtime).isoformat(),
        }

    def _validate_html_template(self, template_data: Dict[str, Any], validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate HTML template."""
        content = template_data["content"]
        
        # Basic HTML validation
        if "<html>" not in content.lower() and "<body>" not in content.lower():
            validation_result["warnings"].append("Template may not be a complete HTML document")

        # Check for common template syntax issues
        if "{{" in content and "}}" not in content:
            validation_result["errors"].append("Unclosed template variable syntax")
            validation_result["is_valid"] = False

        return validation_result

    def _validate_markdown_template(self, template_data: Dict[str, Any], validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate Markdown template."""
        content = template_data["content"]
        
        # Check for markdown syntax
        if not any(marker in content for marker in ["#", "*", "-", "`"]):
            validation_result["warnings"].append("Template may not contain Markdown syntax")

        return validation_result

    def _validate_latex_template(self, template_data: Dict[str, Any], validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate LaTeX template."""
        content = template_data["content"]
        
        # Basic LaTeX validation
        if "\\documentclass" not in content:
            validation_result["warnings"].append("Template may not be a complete LaTeX document")

        # Check for balanced braces
        open_braces = content.count("{")
        close_braces = content.count("}")
        if open_braces != close_braces:
            validation_result["errors"].append("Unbalanced braces in LaTeX template")
            validation_result["is_valid"] = False

        return validation_result

    def _extract_template_variables(self, content: str) -> Dict[str, List[str]]:
        """Extract template variables from content."""
        import re
        
        # Find Jinja2-style variables {{ variable }}
        jinja_vars = re.findall(r'\{\{\s*([^}]+)\s*\}\}', content)
        
        # Find template placeholders {variable}
        placeholder_vars = re.findall(r'\{([^{}]+)\}', content)
        
        # Combine and clean variables
        all_vars = set()
        for var in jinja_vars + placeholder_vars:
            # Clean variable name
            clean_var = var.strip().split('|')[0].split('.')[0]  # Remove filters and attributes
            if clean_var and clean_var.isidentifier():
                all_vars.add(clean_var)

        # For now, treat all as required (in a real implementation, this would be more sophisticated)
        return {
            "required": sorted(list(all_vars)),
            "optional": [],
        }
