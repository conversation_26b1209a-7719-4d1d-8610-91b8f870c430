# backend/core/reports/templates/template_renderer.py
"""
Template Renderer.

This module provides template rendering functionality using Jinja2
for generating documents from templates and data.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import tempfile
from pathlib import Path

from jinja2 import Environment, BaseLoader, Template, TemplateError
from markupsafe import Markup

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class TemplateRenderer:
    """
    Renders templates using Jinja2 with engineering-specific filters and functions.
    """

    def __init__(self):
        """Initialize the template renderer."""
        self.jinja_env = self._create_jinja_environment()
        logger.debug("TemplateRenderer initialized")

    def render_template(
        self,
        template_content: str,
        data: Dict[str, Any],
        template_type: str = "html",
    ) -> str:
        """
        Render template with provided data.

        Args:
            template_content: Template content string
            data: Data to render in template
            template_type: Type of template (html, markdown, latex)

        Returns:
            Rendered template content

        Raises:
            InvalidInputError: If template or data is invalid
            CalculationError: If rendering fails
        """
        logger.debug(f"Rendering {template_type} template")

        try:
            # Validate inputs
            if not template_content.strip():
                raise InvalidInputError("Template content cannot be empty")

            if not isinstance(data, dict):
                raise InvalidInputError("Data must be a dictionary")

            # Add standard variables
            render_data = self._prepare_render_data(data)

            # Create template
            template = self.jinja_env.from_string(template_content)

            # Render template
            rendered_content = template.render(**render_data)

            logger.debug("Template rendered successfully")
            return rendered_content

        except TemplateError as e:
            logger.error(f"Template rendering error: {e}")
            raise InvalidInputError(f"Template syntax error: {str(e)}")
        except Exception as e:
            logger.error(f"Template rendering failed: {e}")
            raise CalculationError(f"Template rendering failed: {str(e)}")

    def render_template_to_file(
        self,
        template_content: str,
        data: Dict[str, Any],
        output_path: str,
        template_type: str = "html",
    ) -> str:
        """
        Render template and save to file.

        Args:
            template_content: Template content string
            data: Data to render in template
            output_path: Path to save rendered content
            template_type: Type of template

        Returns:
            Path to rendered file

        Raises:
            CalculationError: If rendering or saving fails
        """
        logger.debug(f"Rendering template to file: {output_path}")

        try:
            # Render template
            rendered_content = self.render_template(template_content, data, template_type)

            # Save to file
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(rendered_content)

            logger.debug(f"Template rendered to file: {output_file}")
            return str(output_file)

        except Exception as e:
            logger.error(f"Failed to render template to file: {e}")
            raise CalculationError(f"Failed to render template to file: {str(e)}")

    def validate_template_syntax(self, template_content: str) -> Dict[str, Any]:
        """
        Validate template syntax.

        Args:
            template_content: Template content to validate

        Returns:
            Dict with validation results
        """
        logger.debug("Validating template syntax")

        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "variables": [],
        }

        try:
            # Try to parse template
            template = self.jinja_env.from_string(template_content)
            
            # Extract variables
            variables = self._extract_template_variables(template_content)
            validation_result["variables"] = variables

            logger.debug("Template syntax validation successful")

        except TemplateError as e:
            validation_result["is_valid"] = False
            validation_result["errors"].append(str(e))
            logger.warning(f"Template syntax validation failed: {e}")

        except Exception as e:
            validation_result["is_valid"] = False
            validation_result["errors"].append(f"Unexpected error: {str(e)}")
            logger.error(f"Template validation error: {e}")

        return validation_result

    def render_with_preview_data(
        self,
        template_content: str,
        template_type: str = "html",
    ) -> str:
        """
        Render template with sample preview data.

        Args:
            template_content: Template content
            template_type: Type of template

        Returns:
            Rendered template with preview data
        """
        logger.debug("Rendering template with preview data")

        try:
            # Generate preview data
            preview_data = self._generate_preview_data()

            # Render template
            rendered_content = self.render_template(template_content, preview_data, template_type)

            return rendered_content

        except Exception as e:
            logger.error(f"Preview rendering failed: {e}")
            raise CalculationError(f"Preview rendering failed: {str(e)}")

    def _create_jinja_environment(self) -> Environment:
        """Create Jinja2 environment with custom filters and functions."""
        env = Environment(
            loader=BaseLoader(),
            autoescape=True,
            trim_blocks=True,
            lstrip_blocks=True,
        )

        # Add custom filters
        env.filters.update({
            'engineering_format': self._engineering_format_filter,
            'currency': self._currency_filter,
            'percentage': self._percentage_filter,
            'round_engineering': self._round_engineering_filter,
            'unit_conversion': self._unit_conversion_filter,
        })

        # Add custom functions
        env.globals.update({
            'datetime': datetime,
            'sum_attribute': self._sum_attribute_function,
            'group_by': self._group_by_function,
            'format_number': self._format_number_function,
        })

        return env

    def _prepare_render_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for rendering by adding standard variables."""
        render_data = data.copy()

        # Add standard variables
        render_data.setdefault('render_date', datetime.now().strftime('%Y-%m-%d'))
        render_data.setdefault('render_time', datetime.now().strftime('%H:%M:%S'))
        render_data.setdefault('render_datetime', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

        # Add utility functions
        render_data['datetime'] = datetime

        return render_data

    def _extract_template_variables(self, template_content: str) -> List[str]:
        """Extract variables from template content."""
        import re
        
        # Find Jinja2 variables
        variables = set()
        
        # Match {{ variable }} patterns
        var_pattern = r'\{\{\s*([^}|]+?)(?:\|[^}]*)?\s*\}\}'
        matches = re.findall(var_pattern, template_content)
        
        for match in matches:
            # Clean variable name
            var_name = match.strip().split('.')[0]  # Remove attribute access
            if var_name and var_name.isidentifier():
                variables.add(var_name)

        # Match {% for variable in ... %} patterns
        for_pattern = r'\{\%\s*for\s+(\w+)\s+in\s+([^%]+)\s*\%\}'
        for_matches = re.findall(for_pattern, template_content)
        
        for loop_var, collection in for_matches:
            collection_name = collection.strip().split('.')[0]
            if collection_name and collection_name.isidentifier():
                variables.add(collection_name)

        return sorted(list(variables))

    def _generate_preview_data(self) -> Dict[str, Any]:
        """Generate sample data for template preview."""
        return {
            "project_name": "Sample Heat Tracing Project",
            "project_number": "HT-2024-001",
            "calculation_date": datetime.now().strftime('%Y-%m-%d'),
            "engineer_name": "John Engineer",
            "company_logo": None,
            "revision_number": "Rev 1",
            "design_parameters": [
                {
                    "name": "Ambient Temperature",
                    "value": "-20",
                    "unit": "°C",
                    "notes": "Winter design condition"
                },
                {
                    "name": "Maintain Temperature",
                    "value": "60",
                    "unit": "°C",
                    "notes": "Process requirement"
                },
                {
                    "name": "Pipe Material",
                    "value": "Carbon Steel",
                    "unit": "-",
                    "notes": "Schedule 40"
                }
            ],
            "circuits": [
                {
                    "circuit_id": "HT-001",
                    "pipe_tag": "P-101-A",
                    "length": 25.5,
                    "heat_loss_per_meter": 15.2,
                    "cable_power_per_meter": 20.0,
                    "total_power": 510,
                    "status": "Active"
                },
                {
                    "circuit_id": "HT-002",
                    "pipe_tag": "P-102-A",
                    "length": 18.3,
                    "heat_loss_per_meter": 12.8,
                    "cable_power_per_meter": 16.0,
                    "total_power": 293,
                    "status": "Active"
                }
            ],
            "summary_data": {
                "total_length": 43.8,
                "total_heat_loss": 1218,
                "total_power": 803,
                "average_power_density": 18.3,
                "safety_factor": 1.32
            },
            "cables_data": [
                {
                    "circuit_id": "HT-001",
                    "pipe_tag": "P-101-A",
                    "cable_type": "Self-Regulating 20W/m",
                    "power_per_meter": 20,
                    "voltage": 240,
                    "length": 25.5,
                    "total_power": 510,
                    "panel": "HTP-01",
                    "breaker": "CB-01",
                    "notes": ""
                }
            ],
            "load_data": [
                {
                    "panel_name": "HTP-01",
                    "circuit_count": 12,
                    "connected_load": 8.5,
                    "operating_load": 6.8,
                    "load_factor": 0.8
                }
            ],
            "total_connected_load": 8.5,
            "total_operating_load": 6.8,
            "materials_data": [
                {
                    "is_category_header": True,
                    "category": "Heat Tracing Cables"
                },
                {
                    "item_code": "HTC-20-SR",
                    "description": "Self-Regulating Cable 20W/m",
                    "unit": "m",
                    "quantity": 100,
                    "unit_cost": 25.50,
                    "total_cost": 2550.00,
                    "notes": "Including end seals"
                }
            ],
            "total_cost": 15750.00,
            "labor_hours": 120,
        }

    # Custom Jinja2 filters
    def _engineering_format_filter(self, value: float, precision: int = 2) -> str:
        """Format number in engineering notation."""
        try:
            if value == 0:
                return "0"
            
            # Engineering notation (powers of 1000)
            exponent = int(value // 1000) if value >= 1000 else 0
            mantissa = value / (1000 ** exponent)
            
            suffixes = ["", "k", "M", "G", "T"]
            suffix = suffixes[min(exponent, len(suffixes) - 1)]
            
            return f"{mantissa:.{precision}f}{suffix}"
        except:
            return str(value)

    def _currency_filter(self, value: float, currency: str = "$") -> str:
        """Format number as currency."""
        try:
            return f"{currency}{value:,.2f}"
        except:
            return str(value)

    def _percentage_filter(self, value: float, precision: int = 1) -> str:
        """Format number as percentage."""
        try:
            return f"{value * 100:.{precision}f}%"
        except:
            return str(value)

    def _round_engineering_filter(self, value: float, precision: int = 2) -> float:
        """Round to engineering precision."""
        try:
            return round(value, precision)
        except:
            return value

    def _unit_conversion_filter(self, value: float, from_unit: str, to_unit: str) -> float:
        """Convert between units (basic implementation)."""
        # This would be expanded with a proper unit conversion library
        conversions = {
            ("m", "ft"): 3.28084,
            ("ft", "m"): 0.3048,
            ("W", "kW"): 0.001,
            ("kW", "W"): 1000,
        }
        
        factor = conversions.get((from_unit, to_unit), 1.0)
        return value * factor

    # Custom Jinja2 functions
    def _sum_attribute_function(self, items: List[Dict], attribute: str) -> float:
        """Sum an attribute across a list of items."""
        try:
            return sum(item.get(attribute, 0) for item in items if isinstance(item, dict))
        except:
            return 0

    def _group_by_function(self, items: List[Dict], key: str) -> Dict[str, List[Dict]]:
        """Group items by a key."""
        groups = {}
        for item in items:
            if isinstance(item, dict) and key in item:
                group_key = item[key]
                if group_key not in groups:
                    groups[group_key] = []
                groups[group_key].append(item)
        return groups

    def _format_number_function(self, value: float, format_type: str = "decimal") -> str:
        """Format number according to type."""
        try:
            if format_type == "decimal":
                return f"{value:.2f}"
            elif format_type == "integer":
                return f"{int(value)}"
            elif format_type == "scientific":
                return f"{value:.2e}"
            else:
                return str(value)
        except:
            return str(value)
