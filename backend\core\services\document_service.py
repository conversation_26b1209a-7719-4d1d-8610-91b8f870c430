# backend/core/services/document_service.py
"""
Document service for business logic operations.

This module provides service classes for document management including
data import/export, report generation, and calculation standards management.
"""

import json
import logging
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from core.models.documents import (
    ImportedDataRevision,
    ExportedDocument,
    CalculationStandard,
)
from core.repositories.document_repository import (
    ImportedDataRevisionRepository,
    ExportedDocumentRepository,
    CalculationStandardRepository,
)
from core.repositories.project_repository import ProjectRepository
from core.repositories.user_repository import UserRepository
from core.schemas.document_schemas import (
    ImportedDataRevisionCreateSchema,
    ImportedDataRevisionUpdateSchema,
    ImportedDataRevisionReadSchema,
    ImportedDataRevisionSummarySchema,
    ExportedDocumentCreateSchema,
    ExportedDocumentUpdateSchema,
    ExportedDocumentReadSchema,
    ExportedDocumentSummarySchema,
    CalculationStandardCreateSchema,
    CalculationStandardUpdateSchema,
    CalculationStandardReadSchema,
    CalculationStandardSummarySchema,
    DocumentGenerationRequestSchema,
    FileUploadSchema,
    FileDownloadSchema,
)
from core.errors.exceptions import (
    NotFoundError,
    DataValidationError,
    DuplicateEntryError,
    DatabaseError,
    InvalidInputError,
)

logger = logging.getLogger(__name__)


class DocumentService:
    """
    Service class for document management operations.

    Handles business logic for data import/export, report generation,
    and calculation standards management.
    """

    def __init__(
        self,
        imported_data_revision_repository: ImportedDataRevisionRepository,
        exported_document_repository: ExportedDocumentRepository,
        calculation_standard_repository: CalculationStandardRepository,
        project_repository: ProjectRepository,
        user_repository: UserRepository,
    ):
        """
        Initialize the Document service.

        Args:
            imported_data_revision_repository: Repository for imported data revisions
            exported_document_repository: Repository for exported documents
            calculation_standard_repository: Repository for calculation standards
            project_repository: Repository for projects
            user_repository: Repository for users
        """
        self.imported_data_revision_repository = imported_data_revision_repository
        self.exported_document_repository = exported_document_repository
        self.calculation_standard_repository = calculation_standard_repository
        self.project_repository = project_repository
        self.user_repository = user_repository
        logger.debug("DocumentService initialized")

    # ============================================================================
    # IMPORTED DATA REVISION OPERATIONS
    # ============================================================================

    def create_imported_data_revision(
        self, revision_data: ImportedDataRevisionCreateSchema
    ) -> ImportedDataRevisionReadSchema:
        """
        Create a new imported data revision with business validation.

        Args:
            revision_data: Validated revision creation data

        Returns:
            ImportedDataRevisionReadSchema: Created revision data

        Raises:
            NotFoundError: If project or user doesn't exist
            DuplicateEntryError: If active revision already exists for filename
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(
            f"Creating imported data revision for project {revision_data.project_id}: {revision_data.source_filename}"
        )

        try:
            # Validate project exists
            project = self.project_repository.get_by_id(revision_data.project_id)
            if not project:
                raise NotFoundError(
                    code="PROJECT_NOT_FOUND",
                    detail=f"Project with ID {revision_data.project_id} not found",
                )

            # Validate user exists if provided
            if revision_data.imported_by_user_id:
                user = self.user_repository.get_by_id(revision_data.imported_by_user_id)
                if not user:
                    raise NotFoundError(
                        code="USER_NOT_FOUND",
                        detail=f"User with ID {revision_data.imported_by_user_id} not found",
                    )

            # Business validation
            self._validate_imported_data_revision_creation(revision_data)

            # Handle active revision logic
            if revision_data.is_active_revision:
                # Deactivate other revisions for this filename
                deactivated_count = (
                    self.imported_data_revision_repository.deactivate_other_revisions(
                        revision_data.project_id, revision_data.source_filename
                    )
                )
                # Flush the deactivation changes to avoid unique constraint violations
                if deactivated_count > 0:
                    self.imported_data_revision_repository.db_session.flush()

            # Convert schema to dict for repository
            revision_dict = revision_data.model_dump()

            # Create revision via repository
            new_revision = self.imported_data_revision_repository.create(revision_dict)

            # Commit the transaction
            self.imported_data_revision_repository.db_session.commit()
            self.imported_data_revision_repository.db_session.refresh(new_revision)

            logger.info(
                f"Imported data revision '{new_revision.source_filename}' (ID: {new_revision.id}) created successfully"
            )

            # Convert to read schema
            return ImportedDataRevisionReadSchema.model_validate(new_revision)

        except IntegrityError as e:
            self.imported_data_revision_repository.db_session.rollback()
            logger.warning(
                f"Duplicate imported data revision detected: {revision_data.source_filename}"
            )
            raise DuplicateEntryError(
                message=f"Active revision for filename '{revision_data.source_filename}' already exists",
                original_exception=e,
            )

        except (NotFoundError, DataValidationError, DuplicateEntryError):
            self.imported_data_revision_repository.db_session.rollback()
            raise

        except SQLAlchemyError as e:
            self.imported_data_revision_repository.db_session.rollback()
            logger.error(f"Database error creating imported data revision: {e}")
            raise DatabaseError(
                reason="Failed to create imported data revision",
                original_exception=e,
            ) from e

    def get_imported_data_revision_by_id(
        self, revision_id: int
    ) -> ImportedDataRevisionReadSchema:
        """
        Get imported data revision by ID.

        Args:
            revision_id: Revision ID

        Returns:
            ImportedDataRevisionReadSchema: Revision data

        Raises:
            NotFoundError: If revision doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving imported data revision {revision_id}")

        try:
            revision = self.imported_data_revision_repository.get_by_id(revision_id)
            if not revision:
                raise NotFoundError(
                    code="IMPORTED_DATA_REVISION_NOT_FOUND",
                    detail=f"Imported data revision with ID {revision_id} not found",
                )

            return ImportedDataRevisionReadSchema.model_validate(revision)

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving imported data revision {revision_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to retrieve imported data revision",
                original_exception=e,
            ) from e

    def get_imported_data_revisions_by_project(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> Tuple[List[ImportedDataRevisionSummarySchema], int]:
        """
        Get imported data revisions for a project with pagination.

        Args:
            project_id: Project ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple[List[ImportedDataRevisionSummarySchema], int]: List of revisions and total count

        Raises:
            NotFoundError: If project doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving imported data revisions for project {project_id}")

        try:
            # Validate project exists
            project = self.project_repository.get_by_id(project_id)
            if not project:
                raise NotFoundError(
                    code="PROJECT_NOT_FOUND",
                    detail=f"Project with ID {project_id} not found",
                )

            # Get revisions
            revisions = self.imported_data_revision_repository.get_by_project_id(
                project_id, skip, limit
            )

            # Get total count
            total_count = self.imported_data_revision_repository.count_by_project_id(
                project_id
            )

            # Convert to summary schemas
            revision_summaries = [
                ImportedDataRevisionSummarySchema.model_validate(revision)
                for revision in revisions
            ]

            logger.debug(
                f"Retrieved {len(revision_summaries)} imported data revisions for project {project_id}"
            )

            return revision_summaries, total_count

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving imported data revisions for project {project_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to retrieve imported data revisions",
                original_exception=e,
            ) from e

    def update_imported_data_revision(
        self, revision_id: int, revision_data: ImportedDataRevisionUpdateSchema
    ) -> ImportedDataRevisionReadSchema:
        """
        Update an existing imported data revision.

        Args:
            revision_id: Revision ID
            revision_data: Update data

        Returns:
            ImportedDataRevisionReadSchema: Updated revision data

        Raises:
            NotFoundError: If revision doesn't exist
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(f"Updating imported data revision {revision_id}")

        try:
            # Get existing revision
            existing_revision = self.imported_data_revision_repository.get_by_id(
                revision_id
            )
            if not existing_revision:
                raise NotFoundError(
                    code="IMPORTED_DATA_REVISION_NOT_FOUND",
                    detail=f"Imported data revision with ID {revision_id} not found",
                )

            # Business validation for updates
            self._validate_imported_data_revision_update(
                existing_revision, revision_data
            )

            # Update only provided fields
            update_dict = revision_data.model_dump(exclude_unset=True)

            if not update_dict:
                logger.debug(
                    f"No fields to update for imported data revision {revision_id}"
                )
                return ImportedDataRevisionReadSchema.model_validate(existing_revision)

            # Handle active revision logic
            if (
                "is_active_revision" in update_dict
                and update_dict["is_active_revision"]
            ):
                # Deactivate other revisions for this filename
                self.imported_data_revision_repository.deactivate_other_revisions(
                    existing_revision.project_id,
                    existing_revision.source_filename,
                    exclude_id=revision_id,
                )

            # Update the revision
            for field, value in update_dict.items():
                setattr(existing_revision, field, value)

            # Commit the transaction
            self.imported_data_revision_repository.db_session.commit()
            self.imported_data_revision_repository.db_session.refresh(existing_revision)

            logger.info(
                f"Imported data revision '{existing_revision.source_filename}' (ID: {existing_revision.id}) updated successfully"
            )

            return ImportedDataRevisionReadSchema.model_validate(existing_revision)

        except (NotFoundError, DataValidationError):
            self.imported_data_revision_repository.db_session.rollback()
            raise

        except SQLAlchemyError as e:
            self.imported_data_revision_repository.db_session.rollback()
            logger.error(
                f"Database error updating imported data revision {revision_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to update imported data revision",
                original_exception=e,
            ) from e

    def delete_imported_data_revision(self, revision_id: int) -> bool:
        """
        Soft delete an imported data revision.

        Args:
            revision_id: Revision ID

        Returns:
            bool: True if deletion was successful

        Raises:
            NotFoundError: If revision doesn't exist
            BusinessLogicError: If revision cannot be deleted
            DatabaseError: If database operation fails
        """
        logger.info(f"Deleting imported data revision {revision_id}")

        try:
            # Get existing revision
            existing_revision = self.imported_data_revision_repository.get_by_id(
                revision_id
            )
            if not existing_revision:
                raise NotFoundError(
                    code="IMPORTED_DATA_REVISION_NOT_FOUND",
                    detail=f"Imported data revision with ID {revision_id} not found",
                )

            # Business validation for deletion
            self._validate_imported_data_revision_deletion(existing_revision)

            # Perform soft delete
            existing_revision.is_deleted = True
            existing_revision.deleted_at = datetime.now(timezone.utc)

            # Commit the transaction
            self.imported_data_revision_repository.db_session.commit()

            logger.info(f"Imported data revision {revision_id} deleted successfully")
            return True

        except (NotFoundError, InvalidInputError):
            self.imported_data_revision_repository.db_session.rollback()
            raise

        except SQLAlchemyError as e:
            self.imported_data_revision_repository.db_session.rollback()
            logger.error(
                f"Database error deleting imported data revision {revision_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to delete imported data revision",
                original_exception=e,
            ) from e

    # ============================================================================
    # EXPORTED DOCUMENT OPERATIONS
    # ============================================================================

    def create_exported_document(
        self, document_data: ExportedDocumentCreateSchema
    ) -> ExportedDocumentReadSchema:
        """
        Create a new exported document with business validation.

        Args:
            document_data: Validated document creation data

        Returns:
            ExportedDocumentReadSchema: Created document data

        Raises:
            NotFoundError: If project or user doesn't exist
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(
            f"Creating exported document for project {document_data.project_id}: {document_data.filename}"
        )

        try:
            # Validate project exists
            project = self.project_repository.get_by_id(document_data.project_id)
            if not project:
                raise NotFoundError(
                    code="PROJECT_NOT_FOUND",
                    detail=f"Project with ID {document_data.project_id} not found",
                )

            # Validate user exists if provided
            if document_data.generated_by_user_id:
                user = self.user_repository.get_by_id(
                    document_data.generated_by_user_id
                )
                if not user:
                    raise NotFoundError(
                        code="USER_NOT_FOUND",
                        detail=f"User with ID {document_data.generated_by_user_id} not found",
                    )

            # Business validation
            self._validate_exported_document_creation(document_data)

            # Handle latest revision logic
            if document_data.is_latest_revision:
                # Mark other documents as not latest for this document type
                self.exported_document_repository.mark_others_as_not_latest(
                    document_data.project_id, document_data.document_type
                )

            # Convert schema to dict for repository
            document_dict = document_data.model_dump()

            # Create document via repository
            new_document = self.exported_document_repository.create(document_dict)

            # Commit the transaction
            self.exported_document_repository.db_session.commit()
            self.exported_document_repository.db_session.refresh(new_document)

            logger.info(
                f"Exported document '{new_document.filename}' (ID: {new_document.id}) created successfully"
            )

            # Convert to read schema
            return ExportedDocumentReadSchema.model_validate(new_document)

        except (NotFoundError, DataValidationError):
            self.exported_document_repository.db_session.rollback()
            raise

        except SQLAlchemyError as e:
            self.exported_document_repository.db_session.rollback()
            logger.error(f"Database error creating exported document: {e}")
            raise DatabaseError(
                reason="Failed to create exported document",
                original_exception=e,
            ) from e

    def get_exported_document_by_id(
        self, document_id: int
    ) -> ExportedDocumentReadSchema:
        """
        Get exported document by ID.

        Args:
            document_id: Document ID

        Returns:
            ExportedDocumentReadSchema: Document data

        Raises:
            NotFoundError: If document doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving exported document {document_id}")

        try:
            document = self.exported_document_repository.get_by_id(document_id)
            if not document:
                raise NotFoundError(
                    code="EXPORTED_DOCUMENT_NOT_FOUND",
                    detail=f"Exported document with ID {document_id} not found",
                )

            return ExportedDocumentReadSchema.model_validate(document)

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving exported document {document_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to retrieve exported document",
                original_exception=e,
            ) from e

    def get_exported_documents_by_project(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> Tuple[List[ExportedDocumentSummarySchema], int]:
        """
        Get exported documents for a project with pagination.

        Args:
            project_id: Project ID
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple[List[ExportedDocumentSummarySchema], int]: List of documents and total count

        Raises:
            NotFoundError: If project doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving exported documents for project {project_id}")

        try:
            # Validate project exists
            project = self.project_repository.get_by_id(project_id)
            if not project:
                raise NotFoundError(
                    code="PROJECT_NOT_FOUND",
                    detail=f"Project with ID {project_id} not found",
                )

            # Get documents
            documents = self.exported_document_repository.get_by_project_id(
                project_id, skip, limit
            )

            # Get total count
            total_count = self.exported_document_repository.count_by_project_id(
                project_id
            )

            # Convert to summary schemas
            document_summaries = [
                ExportedDocumentSummarySchema.model_validate(document)
                for document in documents
            ]

            logger.debug(
                f"Retrieved {len(document_summaries)} exported documents for project {project_id}"
            )

            return document_summaries, total_count

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving exported documents for project {project_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to retrieve exported documents",
                original_exception=e,
            ) from e

    # ============================================================================
    # VALIDATION METHODS
    # ============================================================================

    def _validate_imported_data_revision_creation(
        self, revision_data: ImportedDataRevisionCreateSchema
    ) -> None:
        """
        Validate imported data revision creation business rules.

        Args:
            revision_data: Revision creation data

        Raises:
            DataValidationError: If validation fails
        """
        # Note: We don't check for existing active revisions here because
        # the service handles deactivation of other revisions automatically
        # when is_active_revision=True. This allows for proper revision management.
        pass

    def _validate_imported_data_revision_update(
        self,
        existing_revision: ImportedDataRevision,
        revision_data: ImportedDataRevisionUpdateSchema,
    ) -> None:
        """
        Validate imported data revision update business rules.

        Args:
            existing_revision: Existing revision
            revision_data: Update data

        Raises:
            DataValidationError: If validation fails
        """
        # Check if trying to activate when another active revision exists
        if (
            hasattr(revision_data, "is_active_revision")
            and revision_data.is_active_revision
            and not existing_revision.is_active_revision
        ):
            existing_active = (
                self.imported_data_revision_repository.get_active_revision_by_filename(
                    existing_revision.project_id, existing_revision.source_filename
                )
            )
            if existing_active and existing_active.id != existing_revision.id:
                raise DataValidationError(
                    details={
                        "is_active_revision": f"Another active revision already exists for filename '{existing_revision.source_filename}'"
                    }
                )

    def _validate_imported_data_revision_deletion(
        self, existing_revision: ImportedDataRevision
    ) -> None:
        """
        Validate imported data revision deletion business rules.

        Args:
            existing_revision: Existing revision

        Raises:
            BusinessLogicError: If deletion is not allowed
        """
        # Check if this is the only active revision for the filename
        if existing_revision.is_active_revision:
            all_revisions = (
                self.imported_data_revision_repository.get_revisions_by_filename(
                    existing_revision.project_id, existing_revision.source_filename
                )
            )
            active_revisions = [
                r for r in all_revisions if r.is_active_revision and not r.is_deleted
            ]

            if len(active_revisions) <= 1:
                raise InvalidInputError(
                    message=f"Cannot delete the only active revision for filename '{existing_revision.source_filename}'"
                )

    def _validate_exported_document_creation(
        self,
        document_data: ExportedDocumentCreateSchema,  # ruff: noqa: ARG002
    ) -> None:
        """
        Validate exported document creation business rules.

        Args:
            document_data: Document creation data

        Raises:
            DataValidationError: If validation fails
        """
        # Additional business validation can be added here
        # For now, basic validation is handled by the schema
        pass

    # ============================================================================
    # CALCULATION STANDARD OPERATIONS
    # ============================================================================

    def create_calculation_standard(
        self, standard_data: CalculationStandardCreateSchema
    ) -> CalculationStandardReadSchema:
        """
        Create a new calculation standard with business validation.

        Args:
            standard_data: Validated standard creation data

        Returns:
            CalculationStandardReadSchema: Created standard data

        Raises:
            DuplicateEntryError: If standard code already exists
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating calculation standard: {standard_data.standard_code}")

        try:
            # Business validation
            self._validate_calculation_standard_creation(standard_data)

            # Convert schema to dict for repository
            standard_dict = standard_data.model_dump()

            # Create standard via repository
            new_standard = self.calculation_standard_repository.create(standard_dict)

            # Commit the transaction
            self.calculation_standard_repository.db_session.commit()
            self.calculation_standard_repository.db_session.refresh(new_standard)

            logger.info(
                f"Calculation standard '{new_standard.standard_code}' (ID: {new_standard.id}) created successfully"
            )

            # Convert to read schema
            return CalculationStandardReadSchema.model_validate(new_standard)

        except IntegrityError as e:
            self.calculation_standard_repository.db_session.rollback()
            logger.warning(
                f"Duplicate calculation standard detected: {standard_data.standard_code}"
            )
            raise DuplicateEntryError(
                message=f"Calculation standard with code '{standard_data.standard_code}' already exists",
                original_exception=e,
            )

        except DataValidationError:
            self.calculation_standard_repository.db_session.rollback()
            raise

        except SQLAlchemyError as e:
            self.calculation_standard_repository.db_session.rollback()
            logger.error(f"Database error creating calculation standard: {e}")
            raise DatabaseError(
                reason="Failed to create calculation standard",
                original_exception=e,
            ) from e

    def get_calculation_standard_by_id(
        self, standard_id: int
    ) -> CalculationStandardReadSchema:
        """
        Get calculation standard by ID.

        Args:
            standard_id: Standard ID

        Returns:
            CalculationStandardReadSchema: Standard data

        Raises:
            NotFoundError: If standard doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving calculation standard {standard_id}")

        try:
            standard = self.calculation_standard_repository.get_by_id(standard_id)
            if not standard or standard.is_deleted:
                raise NotFoundError(
                    code="CALCULATION_STANDARD_NOT_FOUND",
                    detail=f"Calculation standard with ID {standard_id} not found",
                )

            return CalculationStandardReadSchema.model_validate(standard)

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving calculation standard {standard_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to retrieve calculation standard",
                original_exception=e,
            ) from e

    def get_calculation_standard_by_code(
        self, standard_code: str
    ) -> CalculationStandardReadSchema:
        """
        Get calculation standard by code.

        Args:
            standard_code: Standard code

        Returns:
            CalculationStandardReadSchema: Standard data

        Raises:
            NotFoundError: If standard doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving calculation standard by code: {standard_code}")

        try:
            standard = self.calculation_standard_repository.get_by_standard_code(
                standard_code
            )
            if not standard:
                raise NotFoundError(
                    code="CALCULATION_STANDARD_NOT_FOUND",
                    detail=f"Calculation standard with code '{standard_code}' not found",
                )

            return CalculationStandardReadSchema.model_validate(standard)

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving calculation standard by code {standard_code}: {e}"
            )
            raise DatabaseError(
                reason="Failed to retrieve calculation standard",
                original_exception=e,
            ) from e

    def get_all_calculation_standards(
        self, skip: int = 0, limit: int = 100
    ) -> Tuple[List[CalculationStandardSummarySchema], int]:
        """
        Get all calculation standards with pagination.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return

        Returns:
            Tuple[List[CalculationStandardSummarySchema], int]: List of standards and total count

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Retrieving all calculation standards")

        try:
            # Get standards
            standards = self.calculation_standard_repository.get_all_active(skip, limit)

            # Get total count
            total_count = self.calculation_standard_repository.count_active()

            # Convert to summary schemas
            standard_summaries = [
                CalculationStandardSummarySchema.model_validate(standard)
                for standard in standards
            ]

            logger.debug(f"Retrieved {len(standard_summaries)} calculation standards")

            return standard_summaries, total_count

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving calculation standards: {e}")
            raise DatabaseError(
                reason="Failed to retrieve calculation standards",
                original_exception=e,
            ) from e

    def update_calculation_standard(
        self, standard_id: int, standard_data: CalculationStandardUpdateSchema
    ) -> CalculationStandardReadSchema:
        """
        Update an existing calculation standard.

        Args:
            standard_id: Standard ID
            standard_data: Update data

        Returns:
            CalculationStandardReadSchema: Updated standard data

        Raises:
            NotFoundError: If standard doesn't exist
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(f"Updating calculation standard {standard_id}")

        try:
            # Get existing standard
            existing_standard = self.calculation_standard_repository.get_by_id(
                standard_id
            )
            if not existing_standard:
                raise NotFoundError(
                    code="CALCULATION_STANDARD_NOT_FOUND",
                    detail=f"Calculation standard with ID {standard_id} not found",
                )

            # Business validation for updates
            self._validate_calculation_standard_update(existing_standard, standard_data)

            # Update only provided fields
            update_dict = standard_data.model_dump(exclude_unset=True)

            if not update_dict:
                logger.debug(
                    f"No fields to update for calculation standard {standard_id}"
                )
                return CalculationStandardReadSchema.model_validate(existing_standard)

            # Update the standard
            for field, value in update_dict.items():
                setattr(existing_standard, field, value)

            # Commit the transaction
            self.calculation_standard_repository.db_session.commit()
            self.calculation_standard_repository.db_session.refresh(existing_standard)

            logger.info(
                f"Calculation standard '{existing_standard.standard_code}' (ID: {existing_standard.id}) updated successfully"
            )

            return CalculationStandardReadSchema.model_validate(existing_standard)

        except (NotFoundError, DataValidationError):
            self.calculation_standard_repository.db_session.rollback()
            raise

        except SQLAlchemyError as e:
            self.calculation_standard_repository.db_session.rollback()
            logger.error(
                f"Database error updating calculation standard {standard_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to update calculation standard",
                original_exception=e,
            ) from e

    def delete_calculation_standard(self, standard_id: int) -> bool:
        """
        Soft delete a calculation standard.

        Args:
            standard_id: Standard ID

        Returns:
            bool: True if deletion was successful

        Raises:
            NotFoundError: If standard doesn't exist
            DatabaseError: If database operation fails
        """
        logger.info(f"Deleting calculation standard {standard_id}")

        try:
            # Get existing standard
            existing_standard = self.calculation_standard_repository.get_by_id(
                standard_id
            )
            if not existing_standard:
                raise NotFoundError(
                    code="CALCULATION_STANDARD_NOT_FOUND",
                    detail=f"Calculation standard with ID {standard_id} not found",
                )

            # Perform soft delete
            existing_standard.is_deleted = True
            existing_standard.deleted_at = datetime.now(timezone.utc)

            # Commit the transaction
            self.calculation_standard_repository.db_session.commit()

            logger.info(f"Calculation standard {standard_id} deleted successfully")
            return True

        except NotFoundError:
            self.calculation_standard_repository.db_session.rollback()
            raise

        except SQLAlchemyError as e:
            self.calculation_standard_repository.db_session.rollback()
            logger.error(
                f"Database error deleting calculation standard {standard_id}: {e}"
            )
            raise DatabaseError(
                reason="Failed to delete calculation standard",
                original_exception=e,
            ) from e

    def _validate_calculation_standard_creation(
        self, standard_data: CalculationStandardCreateSchema
    ) -> None:
        """
        Validate calculation standard creation business rules.

        Args:
            standard_data: Standard creation data

        Raises:
            DataValidationError: If validation fails
        """
        # Check if standard code already exists
        existing_standard = self.calculation_standard_repository.get_by_standard_code(
            standard_data.standard_code
        )
        if existing_standard:
            raise DuplicateEntryError(
                message=f"Standard with code '{standard_data.standard_code}' already exists"
            )

        # Check if standard name already exists
        existing_by_name = self.calculation_standard_repository.get_by_name(
            standard_data.name
        )
        if existing_by_name:
            raise DataValidationError(
                details={
                    "name": f"Standard with name '{standard_data.name}' already exists"
                }
            )

    def _validate_calculation_standard_update(
        self,
        existing_standard: CalculationStandard,
        standard_data: CalculationStandardUpdateSchema,
    ) -> None:
        """
        Validate calculation standard update business rules.

        Args:
            existing_standard: Existing standard
            standard_data: Update data

        Raises:
            DataValidationError: If validation fails
        """
        # Check if name is being changed and already exists
        if hasattr(standard_data, "name") and standard_data.name:
            if standard_data.name != existing_standard.name:
                existing_by_name = self.calculation_standard_repository.get_by_name(
                    standard_data.name
                )
                if existing_by_name and existing_by_name.id != existing_standard.id:
                    raise DataValidationError(
                        details={
                            "name": f"Standard with name '{standard_data.name}' already exists"
                        }
                    )
