# backend/core/services/electrical_service.py
"""
Electrical Service

This module provides business logic for electrical design operations, integrating
with the calculations layer for cable sizing and voltage drop calculations, and
the standards layer for compliance validation.
"""

from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy.orm import Session

# Import utilities for enhanced functionality
from core.utils.string_utils import sanitize_text
from core.utils.datetime_utils import utcnow_aware
from core.utils.pagination_utils import (
    PaginationParams,
    SortParams,
    paginate_query,
    create_pagination_response,
)
from core.utils.query_utils import QueryBuilder

try:
    from config.logging_config import get_logger
    from core.calculations.calculation_service import (
        CalculationService,
        CableSizingInput,
        CableSizingResult,
    )
    from core.calculations.electrical_sizing.voltage_drop import (
        calculate_voltage_drop,
        calculate_voltage_drop_percentage,
        validate_voltage_drop_compliance,
    )
    from core.errors.exceptions import (
        BaseApplicationException,
        CalculationError,
        DatabaseError,
        InvalidInputError,
        NotFoundError,
        StandardComplianceError,
    )
    from core.models.electrical import (
        CableRoute,
        CableSegment,
        ElectricalNode,
        LoadCalculation,
        VoltageDropCalculation,
    )
    from core.repositories.electrical_repository import (
        CableRouteRepository,
        CableSegmentRepository,
        ElectricalNodeRepository,
        LoadCalculationRepository,
        VoltageDropCalculationRepository,
    )
    from core.schemas.electrical_schemas import (
        CableSizingCalculationInputSchema,
        CableSizingCalculationResultSchema,
        ElectricalDesignInputSchema,
        ElectricalDesignResultSchema,
        ElectricalStandardsValidationInputSchema,
        ElectricalStandardsValidationResultSchema,
        VoltageDropCalculationInputSchema,
        VoltageDropCalculationResultSchema,
    )
    from core.standards.standards_manager import (
        StandardsContext,
        StandardsManager,
        ValidationResult,
    )
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.calculations.calculation_service import (
        CalculationService,
        CableSizingInput,
        CableSizingResult,
    )
    from core.calculations.electrical_sizing.voltage_drop import (
        calculate_voltage_drop,
        calculate_voltage_drop_percentage,
        validate_voltage_drop_compliance,
    )
    from core.errors.exceptions import (
        BaseApplicationException,
        CalculationError,
        DatabaseError,
        InvalidInputError,
        NotFoundError,
        StandardComplianceError,
    )
    from core.models.electrical import (
        CableRoute,
        CableSegment,
        ElectricalNode,
        LoadCalculation,
        VoltageDropCalculation,
    )
    from core.repositories.electrical_repository import (
        CableRouteRepository,
        CableSegmentRepository,
        ElectricalNodeRepository,
        LoadCalculationRepository,
        VoltageDropCalculationRepository,
    )
    from core.schemas.electrical_schemas import (
        CableSizingCalculationInputSchema,
        CableSizingCalculationResultSchema,
        ElectricalDesignInputSchema,
        ElectricalDesignResultSchema,
        ElectricalStandardsValidationInputSchema,
        ElectricalStandardsValidationResultSchema,
        VoltageDropCalculationInputSchema,
        VoltageDropCalculationResultSchema,
    )
    from core.standards.standards_manager import (
        StandardsContext,
        StandardsManager,
        ValidationResult,
    )

# Initialize logger for this module
logger = get_logger(__name__)


class ElectricalService:
    """
    Service for electrical design operations and calculations.

    This service provides high-level business logic for electrical design workflows,
    integrating with the calculations layer for cable sizing and voltage drop
    calculations, and the standards layer for compliance validation.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the electrical service.

        Args:
            db_session: SQLAlchemy database session
        """
        self.db_session = db_session
        self.electrical_node_repo = ElectricalNodeRepository(db_session)
        self.cable_route_repo = CableRouteRepository(db_session)
        self.cable_segment_repo = CableSegmentRepository(db_session)
        self.load_calculation_repo = LoadCalculationRepository(db_session)
        self.voltage_drop_repo = VoltageDropCalculationRepository(db_session)
        self.calculation_service = CalculationService()
        self.standards_manager = StandardsManager()
        logger.debug("ElectricalService initialized")

    def perform_cable_sizing_calculation(
        self, inputs: CableSizingCalculationInputSchema
    ) -> CableSizingCalculationResultSchema:
        """
        Perform cable sizing calculation for electrical loads.

        Args:
            inputs: Cable sizing calculation inputs

        Returns:
            CableSizingCalculationResultSchema: Cable sizing results

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
            BusinessLogicError: If business logic validation fails
        """
        logger.info(
            f"Starting cable sizing calculation for {inputs.required_power_kw}kW load"
        )

        try:
            # Convert schema to calculation service input
            calc_input = CableSizingInput(
                required_power=inputs.required_power_kw * 1000,  # Convert kW to W
                cable_length=inputs.cable_length_m,
                supply_voltage=inputs.supply_voltage_v,
                ambient_temperature=inputs.ambient_temperature_c,
                installation_method=inputs.installation_method.value,
                cable_type=inputs.cable_type,
            )

            # Perform calculation
            calc_result = self.calculation_service.calculate_cable_sizing(calc_input)

            # Convert result to schema
            result = CableSizingCalculationResultSchema(
                recommended_cable_type=calc_result.recommended_cable_type,
                cable_power_per_meter=calc_result.cable_power_per_meter,
                total_cable_length=calc_result.total_cable_length,
                current_draw=calc_result.current_draw,
                voltage_drop=calc_result.voltage_drop,
                voltage_drop_percent=(
                    calc_result.voltage_drop / inputs.supply_voltage_v
                )
                * 100,
                power_density=calc_result.power_density,
                is_compliant=(calc_result.voltage_drop / inputs.supply_voltage_v) * 100
                <= inputs.max_voltage_drop_percent,
                safety_margin_percent=inputs.max_voltage_drop_percent
                - ((calc_result.voltage_drop / inputs.supply_voltage_v) * 100),
                calculation_metadata=calc_result.metadata,
            )

            logger.info(
                f"Cable sizing calculation completed: {result.recommended_cable_type}, "
                f"voltage drop: {result.voltage_drop_percent:.1f}%"
            )
            return result

        except (InvalidInputError, CalculationError):
            raise
        except Exception as e:
            logger.error(f"Cable sizing calculation failed: {e}", exc_info=True)
            raise BaseApplicationException(
                code="ELECTRICAL_CALCULATION_ERROR",
                detail=f"Cable sizing calculation failed: {str(e)}",
            )

    def perform_voltage_drop_calculation(
        self, inputs: VoltageDropCalculationInputSchema
    ) -> VoltageDropCalculationResultSchema:
        """
        Perform voltage drop calculation for cable routes.

        Args:
            inputs: Voltage drop calculation inputs

        Returns:
            VoltageDropCalculationResultSchema: Voltage drop calculation results

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
            BusinessLogicError: If business logic validation fails
        """
        logger.info(
            f"Starting voltage drop calculation for {inputs.load_current_a}A load over {inputs.cable_length_m}m"
        )

        try:
            # Calculate voltage drop
            voltage_drop_v = calculate_voltage_drop(
                current=inputs.load_current_a,
                length=inputs.cable_length_m,
                cable_resistance=inputs.cable_resistance_ohm_per_m,
                power_factor=inputs.power_factor,
                cable_reactance=inputs.cable_reactance_ohm_per_m,
            )

            # Calculate voltage drop percentage
            voltage_drop_percent = calculate_voltage_drop_percentage(
                current=inputs.load_current_a,
                length=inputs.cable_length_m,
                cable_resistance=inputs.cable_resistance_ohm_per_m,
                supply_voltage=inputs.supply_voltage_v,
                power_factor=inputs.power_factor,
            )

            # Calculate power loss
            power_loss_w = (inputs.load_current_a**2) * (
                inputs.cable_resistance_ohm_per_m * inputs.cable_length_m
            )

            # Calculate efficiency
            efficiency_percent = 100 - (
                (power_loss_w / (inputs.supply_voltage_v * inputs.load_current_a)) * 100
            )

            # Check compliance
            is_compliant = voltage_drop_percent <= inputs.max_voltage_drop_percent
            compliance_margin_percent = (
                inputs.max_voltage_drop_percent - voltage_drop_percent
            )

            # Validate compliance
            compliance_result = validate_voltage_drop_compliance(
                current=inputs.load_current_a,
                length=inputs.cable_length_m,
                cable_resistance=inputs.cable_resistance_ohm_per_m,
                supply_voltage=inputs.supply_voltage_v,
                max_voltage_drop_percent=inputs.max_voltage_drop_percent,
            )

            result = VoltageDropCalculationResultSchema(
                calculated_voltage_drop_v=voltage_drop_v,
                calculated_voltage_drop_percent=voltage_drop_percent,
                calculated_power_loss_w=power_loss_w,
                calculated_efficiency_percent=efficiency_percent,
                is_compliant=is_compliant,
                compliance_margin_percent=compliance_margin_percent,
                calculation_method="IEC",  # Default calculation method
                calculation_metadata={
                    "compliance_details": compliance_result,
                    "derating_factor": inputs.derating_factor,
                    "ambient_temperature": inputs.ambient_temperature_c,
                },
            )

            logger.info(
                f"Voltage drop calculation completed: {voltage_drop_percent:.1f}%, "
                f"compliant: {is_compliant}"
            )
            return result

        except (InvalidInputError, CalculationError):
            raise
        except Exception as e:
            logger.error(f"Voltage drop calculation failed: {e}", exc_info=True)
            raise BaseApplicationException(
                code="ELECTRICAL_CALCULATION_ERROR",
                detail=f"Voltage drop calculation failed: {str(e)}",
            )

    def validate_electrical_standards(
        self, inputs: ElectricalStandardsValidationInputSchema
    ) -> ElectricalStandardsValidationResultSchema:
        """
        Validate electrical design against applicable standards.

        Args:
            inputs: Standards validation inputs

        Returns:
            ElectricalStandardsValidationResultSchema: Standards validation results

        Raises:
            StandardComplianceError: If critical violations are found
            BusinessLogicError: If validation fails
        """
        logger.info(
            f"Starting electrical standards validation for {len(inputs.project_standards)} standards"
        )

        try:
            # Set up standards context
            context = StandardsContext(
                project_standards=inputs.project_standards,
                hazardous_area_zone=inputs.hazardous_area_zone,
                gas_group=inputs.gas_group,
                temperature_class=inputs.temperature_class,
                application_type="electrical",
                safety_level="standard",
            )
            self.standards_manager.set_context(context)

            # Validate cable selection
            cable_result_dict = inputs.cable_sizing_result.model_dump()
            design_params_dict = inputs.design_parameters.copy()
            design_params_dict.update(inputs.voltage_drop_result.model_dump())

            validation_result = self.standards_manager.validate_cable_selection(
                cable_result=cable_result_dict,
                design_parameters=design_params_dict,
            )

            # Apply safety factors
            adjusted_result = self.standards_manager.apply_safety_factors(
                calculation_result=cable_result_dict,
                calculation_type="cable_sizing",
            )

            result = ElectricalStandardsValidationResultSchema(
                is_compliant=validation_result.is_compliant,
                standard=validation_result.standard,
                violations=validation_result.violations,
                warnings=validation_result.warnings,
                applied_factors=validation_result.applied_factors,
                recommendations=self._generate_recommendations(validation_result),
                metadata={
                    "validation_context": context.__dict__,
                    "adjusted_results": adjusted_result,
                    "original_validation": validation_result.metadata,
                },
            )

            logger.info(
                f"Standards validation completed: compliant={result.is_compliant}, "
                f"violations={len(result.violations)}, warnings={len(result.warnings)}"
            )
            return result

        except StandardComplianceError:
            raise
        except Exception as e:
            logger.error(f"Standards validation failed: {e}", exc_info=True)
            raise BaseApplicationException(
                code="ELECTRICAL_STANDARDS_ERROR",
                detail=f"Standards validation failed: {str(e)}",
            )

    def _generate_recommendations(
        self, validation_result: ValidationResult
    ) -> List[str]:
        """
        Generate design recommendations based on validation results.

        Args:
            validation_result: Standards validation result

        Returns:
            List[str]: List of design recommendations
        """
        recommendations = []

        # Voltage drop recommendations
        if any("voltage drop" in v.lower() for v in validation_result.violations):
            recommendations.append(
                "Consider using larger cable size to reduce voltage drop"
            )
            recommendations.append("Evaluate shorter cable routing options")
            recommendations.append("Consider higher supply voltage if feasible")

        # Power recommendations
        if any("power" in v.lower() for v in validation_result.violations):
            recommendations.append("Review load calculations for accuracy")
            recommendations.append("Consider load diversity factors")

        # Temperature recommendations
        if any("temperature" in v.lower() for v in validation_result.violations):
            recommendations.append("Verify ambient temperature conditions")
            recommendations.append("Consider temperature derating factors")
            recommendations.append("Evaluate cable installation environment")

        # Hazardous area recommendations
        if any("hazardous" in v.lower() for v in validation_result.violations):
            recommendations.append(
                "Ensure cable meets hazardous area certification requirements"
            )
            recommendations.append(
                "Verify gas group and temperature class compatibility"
            )

        # General recommendations for warnings
        if validation_result.warnings:
            recommendations.append("Review design margins and safety factors")
            recommendations.append("Consider future load growth in design")

        return recommendations

    def calculate_load_for_electrical_node(
        self, electrical_node_id: int
    ) -> Dict[str, Any]:
        """
        Calculate total electrical load for a specific electrical node.

        Args:
            electrical_node_id: Electrical node ID

        Returns:
            Dict[str, Any]: Load calculation summary

        Raises:
            NotFoundError: If electrical node not found
            DatabaseError: If database operation fails
            BusinessLogicError: If calculation fails
        """
        logger.info(f"Calculating total load for electrical node {electrical_node_id}")

        try:
            # Verify electrical node exists
            electrical_node = self.electrical_node_repo.get_by_id(electrical_node_id)
            if not electrical_node:
                raise NotFoundError(
                    code="ELECTRICAL_NODE_NOT_FOUND",
                    detail=f"Electrical node {electrical_node_id} not found",
                )

            # Get all load calculations for this node
            load_calculations = self.load_calculation_repo.get_by_electrical_node_id(
                electrical_node_id
            )

            # Calculate totals
            total_rated_power_kw = sum(
                load.rated_power_kw for load in load_calculations
            )
            total_operating_power_kw = sum(
                load.calculated_operating_power_kw
                or load.rated_power_kw * (load.load_factor_percent / 100)
                for load in load_calculations
            )
            total_current_a = sum(
                load.calculated_operating_current_a
                or (
                    (load.rated_power_kw * 1000 * (load.load_factor_percent / 100))
                    / (load.rated_voltage_v * load.power_factor)
                )
                for load in load_calculations
            )

            # Calculate diversity factor
            diversity_factor = (
                sum(load.diversity_factor for load in load_calculations)
                / len(load_calculations)
                if load_calculations
                else 1.0
            )

            # Apply diversity factor
            diversified_power_kw = total_operating_power_kw * diversity_factor
            diversified_current_a = total_current_a * diversity_factor

            # Calculate load breakdown by type
            load_breakdown = {}
            for load in load_calculations:
                load_type = load.load_type
                if load_type not in load_breakdown:
                    load_breakdown[load_type] = {
                        "count": 0,
                        "total_power_kw": 0.0,
                        "total_current_a": 0.0,
                    }
                load_breakdown[load_type]["count"] += 1
                load_breakdown[load_type]["total_power_kw"] += load.rated_power_kw
                load_breakdown[load_type]["total_current_a"] += (
                    load.calculated_operating_current_a
                    or (load.rated_power_kw * 1000)
                    / (load.rated_voltage_v * load.power_factor)
                )

            result = {
                "electrical_node_id": electrical_node_id,
                "electrical_node_name": electrical_node.name,
                "load_count": len(load_calculations),
                "total_rated_power_kw": total_rated_power_kw,
                "total_operating_power_kw": total_operating_power_kw,
                "diversified_power_kw": diversified_power_kw,
                "total_current_a": total_current_a,
                "diversified_current_a": diversified_current_a,
                "diversity_factor": diversity_factor,
                "load_breakdown": load_breakdown,
                "capacity_utilization_percent": (
                    (diversified_power_kw / electrical_node.power_capacity_kva) * 100
                    if electrical_node.power_capacity_kva
                    else 0.0
                ),
                "available_capacity_kva": (
                    electrical_node.power_capacity_kva - diversified_power_kw
                    if electrical_node.power_capacity_kva
                    else 0.0
                ),
            }

            logger.info(
                f"Load calculation completed for node {electrical_node_id}: "
                f"{diversified_power_kw:.1f}kW diversified load"
            )
            return result

        except (NotFoundError, DatabaseError):
            raise
        except Exception as e:
            logger.error(
                f"Load calculation failed for node {electrical_node_id}: {e}",
                exc_info=True,
            )
            raise BaseApplicationException(
                code="ELECTRICAL_LOAD_CALCULATION_ERROR",
                detail=f"Load calculation failed: {str(e)}",
            )

    def optimize_cable_route(
        self, cable_route_id: int, optimization_criteria: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Optimize cable route for cost, voltage drop, or other criteria.

        Args:
            cable_route_id: Cable route ID to optimize
            optimization_criteria: Optimization parameters

        Returns:
            Dict[str, Any]: Optimization results and recommendations

        Raises:
            NotFoundError: If cable route not found
            BusinessLogicError: If optimization fails
        """
        logger.info(f"Optimizing cable route {cable_route_id}")

        try:
            # Get cable route
            cable_route = self.cable_route_repo.get_by_id(cable_route_id)
            if not cable_route:
                raise NotFoundError(
                    code="CABLE_ROUTE_NOT_FOUND",
                    detail=f"Cable route {cable_route_id} not found",
                )

            # Get cable segments
            cable_segments = self.cable_segment_repo.get_by_cable_route_id(
                cable_route_id
            )

            # Get load calculations for the route
            to_node_loads = self.load_calculation_repo.get_by_electrical_node_id(
                cable_route.to_node_id
            )

            # Calculate current requirements
            total_load_kw = sum(load.rated_power_kw for load in to_node_loads)
            total_current_a = sum(
                (load.rated_power_kw * 1000)
                / (load.rated_voltage_v * load.power_factor)
                for load in to_node_loads
            )

            # Optimization criteria
            max_voltage_drop_percent = optimization_criteria.get(
                "max_voltage_drop_percent", 5.0
            )
            # Future use for multi-criteria optimization
            # cost_weight = optimization_criteria.get("cost_weight", 0.3)
            # performance_weight = optimization_criteria.get("performance_weight", 0.7)

            # Current performance
            current_voltage_drop = cable_route.calculated_voltage_drop_v or 0.0
            current_voltage_drop_percent = (
                (current_voltage_drop / 240.0) * 100  # Assuming 240V supply
            )

            optimization_results = {
                "cable_route_id": cable_route_id,
                "current_performance": {
                    "voltage_drop_v": current_voltage_drop,
                    "voltage_drop_percent": current_voltage_drop_percent,
                    "is_compliant": current_voltage_drop_percent
                    <= max_voltage_drop_percent,
                    "total_length_m": cable_route.length_m,
                    "segment_count": len(cable_segments),
                },
                "load_requirements": {
                    "total_load_kw": total_load_kw,
                    "total_current_a": total_current_a,
                    "load_count": len(to_node_loads),
                },
                "optimization_recommendations": [],
                "alternative_solutions": [],
            }

            # Generate optimization recommendations
            recommendations = []

            if current_voltage_drop_percent > max_voltage_drop_percent:
                recommendations.append(
                    {
                        "type": "voltage_drop_violation",
                        "description": f"Voltage drop {current_voltage_drop_percent:.1f}% exceeds limit {max_voltage_drop_percent}%",
                        "solutions": [
                            "Increase cable size",
                            "Reduce cable length",
                            "Use multiple parallel cables",
                            "Increase supply voltage",
                        ],
                    }
                )

            if current_voltage_drop_percent > 3.0:
                recommendations.append(
                    {
                        "type": "high_voltage_drop",
                        "description": f"Voltage drop {current_voltage_drop_percent:.1f}% is high (>3%)",
                        "solutions": [
                            "Consider larger cable size for better efficiency",
                            "Evaluate intermediate distribution points",
                        ],
                    }
                )

            if len(cable_segments) > 5:
                recommendations.append(
                    {
                        "type": "complex_routing",
                        "description": f"Route has {len(cable_segments)} segments - consider simplification",
                        "solutions": [
                            "Consolidate cable segments",
                            "Use direct routing where possible",
                            "Standardize installation methods",
                        ],
                    }
                )

            optimization_results["optimization_recommendations"] = recommendations

            logger.info(
                f"Cable route optimization completed for route {cable_route_id}"
            )
            return optimization_results

        except (NotFoundError, DatabaseError):
            raise
        except Exception as e:
            logger.error(
                f"Cable route optimization failed for route {cable_route_id}: {e}",
                exc_info=True,
            )
            raise BaseApplicationException(
                code="ELECTRICAL_OPTIMIZATION_ERROR",
                detail=f"Cable route optimization failed: {str(e)}",
            )

    def perform_electrical_design_workflow(
        self, inputs: ElectricalDesignInputSchema
    ) -> ElectricalDesignResultSchema:
        """
        Perform complete electrical design workflow.

        Args:
            inputs: Electrical design workflow inputs

        Returns:
            ElectricalDesignResultSchema: Complete design results

        Raises:
            BusinessLogicError: If design workflow fails
        """
        logger.info(
            f"Starting electrical design workflow for project {inputs.project_id}"
        )

        try:
            design_results = ElectricalDesignResultSchema(
                project_id=inputs.project_id,
                designed_nodes=[],
                designed_routes=[],
                created_segments=[],
                load_calculations=[],
                voltage_drop_calculations=[],
                cable_sizing_results=[],
                standards_validation_results=[],
                design_summary={},
                warnings=[],
                errors=[],
            )

            # Process electrical nodes if specified
            if inputs.electrical_node_ids:
                for node_id in inputs.electrical_node_ids:
                    try:
                        node_load_summary = self.calculate_load_for_electrical_node(
                            node_id
                        )
                        design_results.design_summary[f"node_{node_id}_load"] = (
                            node_load_summary
                        )
                    except Exception as e:
                        design_results.warnings.append(
                            f"Failed to calculate load for node {node_id}: {str(e)}"
                        )

            # Process cable routes if specified
            if inputs.cable_route_ids:
                for route_id in inputs.cable_route_ids:
                    try:
                        if inputs.optimization_enabled:
                            optimization_result = self.optimize_cable_route(
                                route_id, inputs.design_parameters
                            )
                            design_results.design_summary[
                                f"route_{route_id}_optimization"
                            ] = optimization_result
                    except Exception as e:
                        design_results.warnings.append(
                            f"Failed to optimize route {route_id}: {str(e)}"
                        )

            # Perform standards validation if requested
            if hasattr(inputs, "standards_context") and inputs.standards_context:
                try:
                    validation_result = self.validate_electrical_standards(
                        inputs.standards_context
                    )
                    design_results.standards_validation_results.append(
                        validation_result
                    )
                except Exception as e:
                    design_results.errors.append(
                        f"Standards validation failed: {str(e)}"
                    )

            # Generate design summary
            design_results.design_summary.update(
                {
                    "total_nodes_processed": len(inputs.electrical_node_ids or []),
                    "total_routes_processed": len(inputs.cable_route_ids or []),
                    "warnings_count": len(design_results.warnings),
                    "errors_count": len(design_results.errors),
                    "design_parameters": inputs.design_parameters,
                }
            )

            logger.info(
                f"Electrical design workflow completed for project {inputs.project_id}: "
                f"{len(design_results.warnings)} warnings, {len(design_results.errors)} errors"
            )
            return design_results

        except Exception as e:
            logger.error(
                f"Electrical design workflow failed for project {inputs.project_id}: {e}",
                exc_info=True,
            )
            raise BaseApplicationException(
                code="ELECTRICAL_DESIGN_WORKFLOW_ERROR",
                detail=f"Electrical design workflow failed: {str(e)}",
            )

    # ============================================================================
    # ENHANCED METHODS WITH UTILITIES
    # ============================================================================

    def create_electrical_node_enhanced(
        self, node_data: Dict[str, Any], created_by_user_id: int
    ) -> Dict[str, Any]:
        """
        Create electrical node with enhanced text sanitization.

        Args:
            node_data: Node creation data
            created_by_user_id: ID of user creating the node

        Returns:
            Dict[str, Any]: Created node data

        Raises:
            DatabaseError: If database operation fails
        """
        logger.info(f"Creating electrical node with enhanced utilities")

        try:
            # Sanitize text fields for security
            if node_data.get("name"):
                node_data["name"] = sanitize_text(node_data["name"])
            if node_data.get("description"):
                node_data["description"] = sanitize_text(node_data["description"])
            if node_data.get("location"):
                node_data["location"] = sanitize_text(node_data["location"])
            if node_data.get("tag_number"):
                node_data["tag_number"] = sanitize_text(node_data["tag_number"])

            # Add metadata
            node_data["created_by_user_id"] = created_by_user_id
            node_data["created_at"] = utcnow_aware()

            # Create node via repository
            new_node = self.electrical_node_repo.create(node_data)
            self.db_session.commit()
            self.db_session.refresh(new_node)

            logger.info(f"Electrical node created successfully: {new_node.id}")
            return {
                "id": new_node.id,
                "name": new_node.name,
                "description": new_node.description,
                "created_at": new_node.created_at,
            }

        except Exception as e:
            logger.error(f"Failed to create electrical node: {e}", exc_info=True)
            raise BaseApplicationException(
                code="ELECTRICAL_NODE_CREATION_ERROR",
                detail=f"Failed to create electrical node: {str(e)}",
            )

    def get_electrical_nodes_paginated(
        self,
        project_id: int,
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        filters: Optional[dict] = None,
    ) -> dict:
        """
        Get paginated list of electrical nodes with enhanced search and sorting.

        Args:
            project_id: Project ID
            pagination_params: Pagination parameters (page, per_page)
            sort_params: Optional sorting parameters
            filters: Optional filters (search, node_type, etc.)

        Returns:
            dict: Paginated response with electrical nodes and metadata

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving paginated electrical nodes for project {project_id}: "
            f"page={pagination_params.page}, per_page={pagination_params.per_page}, "
            f"sort={sort_params}, filters={filters}"
        )

        try:
            from core.models.electrical import ElectricalNode

            # Build base query using QueryBuilder
            builder = QueryBuilder(self.db_session, ElectricalNode)
            builder.filter_by_field("project_id", project_id)

            # Apply filters
            if filters:
                # Handle search filter
                search_term = filters.get("search")
                if search_term:
                    searchable_fields = [
                        "name",
                        "description",
                        "location",
                        "tag_number",
                    ]
                    builder.filter_by_text_search(search_term, searchable_fields)

                # Handle node type filter
                node_type = filters.get("node_type")
                if node_type:
                    builder.filter_by_field("node_type", node_type)

                # Handle voltage level filter
                voltage_level = filters.get("voltage_level")
                if voltage_level:
                    builder.filter_by_field("voltage_level", voltage_level)

            # Build the query
            query = builder.build()

            # Apply pagination and sorting using utilities
            allowed_sort_fields = [
                "name",
                "tag_number",
                "node_type",
                "voltage_level",
                "created_at",
            ]
            result = paginate_query(
                self.db_session,
                query,
                ElectricalNode,
                pagination_params,
                sort_params,
                allowed_sort_fields,
            )

            # Convert to response format
            node_summaries = [
                {
                    "id": node.id,
                    "name": node.name,
                    "description": node.description,
                    "node_type": node.node_type,
                    "voltage_level": node.voltage_level,
                    "location": node.location,
                    "tag_number": node.tag_number,
                    "created_at": node.created_at,
                }
                for node in result.items
            ]

            # Create standardized response
            response = create_pagination_response(node_summaries, result)

            logger.debug(
                f"Retrieved {len(node_summaries)} electrical nodes (total: {result.total})"
            )

            return response

        except Exception as e:
            logger.error(
                f"Database error retrieving paginated electrical nodes: {e}",
                exc_info=True,
            )
            raise BaseApplicationException(
                code="ELECTRICAL_NODES_RETRIEVAL_ERROR",
                detail=f"Failed to retrieve paginated electrical nodes: {str(e)}",
            )
