# backend/core/standards/__init__.py
"""
Standards Layer - Engineering standards validation and compliance.

This module provides comprehensive implementation of international engineering
standards for heat tracing design and electrical calculations.

The standards layer is organized into specialized sub-packages:
- general: Common standards functionality and base classes
- tr_50410: TR 50410 standard specific rules
- iec_60079_30_1: IEC 60079-30-1 hazardous area standards
- ieee: IEEE standards implementation (IEEE 515, IEEE 844, etc.)
- iec: IEC standards implementation (IEC 60079, IEC 62395, etc.)
- api: API standards implementation (API RP 14F, API RP 14FZ, etc.)
- nfpa: NFPA standards implementation (NFPA 70, NFPA 497, etc.)
- iso: ISO standards implementation (ISO 13623, ISO 14692, etc.)
- standards_manager: Central interface for standard selection and application

All standards implementations follow a consistent interface and provide
comprehensive validation, calculation methods, and compliance checking.
"""

from .standards_manager import StandardsManager
from .general import StandardsValidator, StandardsCalculator, BaseStandard
from .ieee import IEEEStandards, IEEE515, IEEE844
from .iec import IECStandards, IEC60079, IEC62395
from .api import APIStandards, APIRP14<PERSON>, <PERSON><PERSON>14<PERSON><PERSON>
from .nfpa import NFPAStandards, NFPA70, NFPA497
from .iso import ISOStandards, ISO13623, ISO14692
from .standards_service import StandardsService

__all__ = [
    # General standards functionality
    "StandardsManager",
    "StandardsValidator",
    "StandardsCalculator",
    "BaseStandard",
    # IEEE standards
    "IEEEStandards",
    "IEEE515",
    "IEEE844",
    # IEC standards
    "IECStandards",
    "IEC60079",
    "IEC62395",
    # API standards
    "APIStandards",
    "APIRP14F",
    "APIRP14FZ",
    # NFPA standards
    "NFPAStandards",
    "NFPA70",
    "NFPA497",
    # ISO standards
    "ISOStandards",
    "ISO13623",
    "ISO14692",
    # Main service
    "StandardsService",
]
