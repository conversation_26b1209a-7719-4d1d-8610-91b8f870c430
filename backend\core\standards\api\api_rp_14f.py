# backend/core/standards/api/api_rp_14f.py
"""
API RP 14F Standard.

Recommended Practice for Design and Installation of Electrical Systems 
for Fixed and Floating Offshore Petroleum Facilities.
"""

import logging
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class APIRP14F(BaseStandard):
    """
    API RP 14F Standard implementation.
    
    This standard covers electrical systems for offshore petroleum facilities.
    """

    def __init__(self):
        """Initialize API RP 14F standard."""
        super().__init__(
            standard_id="API-RP-14F",
            title="Recommended Practice for Design and Installation of Electrical Systems for Fixed and Floating Offshore Petroleum Facilities",
            version="2019",
            standard_type=StandardType.API
        )
        
        self._load_standard_rules()
        self._load_standard_parameters()
        logger.debug("API RP 14F standard initialized")

    def validate_design(self, design_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate design against API RP 14F requirements.

        Args:
            design_data: Design data to validate

        Returns:
            ValidationResult with compliance information
        """
        logger.info("Validating design against API RP 14F")

        try:
            result = ValidationResult()
            
            # Required fields for API RP 14F validation
            required_fields = [
                "facility_type", "location_classification", "voltage", "environmental_conditions"
            ]
            
            self.validate_input_data(design_data, required_fields)

            # Apply validation rules
            applicable_rules = self.get_applicable_rules(design_data)
            
            for rule_id in applicable_rules:
                rule = self.get_rule(rule_id)
                if rule:
                    self._apply_validation_rule(rule_id, rule, design_data, result)

            logger.info(f"API RP 14F validation completed: {result.compliance_level.value}")
            return result

        except Exception as e:
            logger.error(f"API RP 14F validation failed: {e}")
            raise CalculationError(f"API RP 14F validation failed: {str(e)}")

    def get_applicable_rules(self, design_data: Dict[str, Any]) -> List[str]:
        """Get applicable API RP 14F rules."""
        return [
            "APIRP14F_FACILITY_TYPE",
            "APIRP14F_LOCATION_CLASSIFICATION",
            "APIRP14F_VOLTAGE_LIMITS",
            "APIRP14F_ENVIRONMENTAL_PROTECTION",
        ]

    def calculate_parameters(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate API RP 14F specific parameters."""
        logger.info("Calculating API RP 14F parameters")

        try:
            calculated_params = {}

            # Calculate offshore safety factors
            facility_type = input_data.get("facility_type", "fixed")
            base_safety_factor = 1.3
            
            if facility_type == "floating":
                offshore_factor = 1.5
            else:
                offshore_factor = 1.4
            
            total_safety_factor = base_safety_factor * offshore_factor
            
            calculated_params["apirp14f_safety_factor"] = {
                "value": total_safety_factor,
                "unit": "",
                "description": f"API RP 14F safety factor for {facility_type} facility",
            }

            return calculated_params

        except Exception as e:
            logger.error(f"API RP 14F parameter calculation failed: {e}")
            raise CalculationError(f"API RP 14F parameter calculation failed: {str(e)}")

    def _load_standard_rules(self):
        """Load API RP 14F specific rules."""
        self.add_rule("APIRP14F_FACILITY_TYPE", {
            "description": "Facility type requirements",
            "valid_types": ["fixed", "floating", "mobile"],
            "severity": "major",
        })

    def _load_standard_parameters(self):
        """Load API RP 14F specific parameters."""
        self.add_parameter("offshore_safety_factor", {
            "value": 1.5,
            "unit": "",
            "description": "Offshore environment safety factor",
        })

    def _apply_validation_rule(self, rule_id: str, rule: Dict[str, Any], design_data: Dict[str, Any], result: ValidationResult):
        """Apply a specific validation rule."""
        try:
            if rule_id == "APIRP14F_FACILITY_TYPE":
                facility_type = design_data.get("facility_type", "")
                valid_types = rule.get("valid_types", [])
                
                if facility_type not in valid_types:
                    result.add_violation(
                        rule_id,
                        f"Invalid facility type: {facility_type}",
                        rule.get("severity", "major")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

        except Exception as e:
            logger.error(f"Failed to apply rule {rule_id}: {e}")
            result.add_violation(rule_id, f"Rule application failed: {str(e)}", "critical")
