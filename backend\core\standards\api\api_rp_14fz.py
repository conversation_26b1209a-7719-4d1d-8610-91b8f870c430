# backend/core/standards/api/api_rp_14fz.py
"""
API RP 14FZ Standard.

Recommended Practice for Design, Installation, and Maintenance of 
Electrical Systems for Fixed Offshore Platforms Operating in Explosive Atmospheres.
"""

import logging
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class APIRP14FZ(BaseStandard):
    """
    API RP 14FZ Standard implementation.
    
    This standard covers electrical systems for offshore platforms 
    in explosive atmospheres.
    """

    def __init__(self):
        """Initialize API RP 14FZ standard."""
        super().__init__(
            standard_id="API-RP-14FZ",
            title="Recommended Practice for Design, Installation, and Maintenance of Electrical Systems for Fixed Offshore Platforms Operating in Explosive Atmospheres",
            version="2013",
            standard_type=StandardType.API
        )
        
        self._load_standard_rules()
        self._load_standard_parameters()
        logger.debug("API RP 14FZ standard initialized")

    def validate_design(self, design_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate design against API RP 14FZ requirements.

        Args:
            design_data: Design data to validate

        Returns:
            ValidationResult with compliance information
        """
        logger.info("Validating design against API RP 14FZ")

        try:
            result = ValidationResult()
            
            # Required fields for API RP 14FZ validation
            required_fields = [
                "platform_type", "explosive_atmosphere_classification", "protection_method"
            ]
            
            self.validate_input_data(design_data, required_fields)

            # Apply validation rules
            applicable_rules = self.get_applicable_rules(design_data)
            
            for rule_id in applicable_rules:
                rule = self.get_rule(rule_id)
                if rule:
                    self._apply_validation_rule(rule_id, rule, design_data, result)

            logger.info(f"API RP 14FZ validation completed: {result.compliance_level.value}")
            return result

        except Exception as e:
            logger.error(f"API RP 14FZ validation failed: {e}")
            raise CalculationError(f"API RP 14FZ validation failed: {str(e)}")

    def get_applicable_rules(self, design_data: Dict[str, Any]) -> List[str]:
        """Get applicable API RP 14FZ rules."""
        return [
            "APIRP14FZ_PLATFORM_TYPE",
            "APIRP14FZ_EXPLOSIVE_ATMOSPHERE",
            "APIRP14FZ_PROTECTION_METHOD",
        ]

    def calculate_parameters(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate API RP 14FZ specific parameters."""
        logger.info("Calculating API RP 14FZ parameters")

        try:
            calculated_params = {}

            # Calculate explosive atmosphere safety factors
            atmosphere_class = input_data.get("explosive_atmosphere_classification", "Zone 2")
            
            safety_factors = {
                "Zone 0": 2.0,
                "Zone 1": 1.8,
                "Zone 2": 1.5,
            }
            
            safety_factor = safety_factors.get(atmosphere_class, 1.5)
            
            calculated_params["apirp14fz_safety_factor"] = {
                "value": safety_factor,
                "unit": "",
                "description": f"API RP 14FZ safety factor for {atmosphere_class}",
            }

            return calculated_params

        except Exception as e:
            logger.error(f"API RP 14FZ parameter calculation failed: {e}")
            raise CalculationError(f"API RP 14FZ parameter calculation failed: {str(e)}")

    def _load_standard_rules(self):
        """Load API RP 14FZ specific rules."""
        self.add_rule("APIRP14FZ_EXPLOSIVE_ATMOSPHERE", {
            "description": "Explosive atmosphere classification requirements",
            "valid_classifications": ["Zone 0", "Zone 1", "Zone 2"],
            "severity": "critical",
        })

    def _load_standard_parameters(self):
        """Load API RP 14FZ specific parameters."""
        self.add_parameter("explosive_atmosphere_safety_factor", {
            "value": 1.8,
            "unit": "",
            "description": "Safety factor for explosive atmospheres",
        })

    def _apply_validation_rule(self, rule_id: str, rule: Dict[str, Any], design_data: Dict[str, Any], result: ValidationResult):
        """Apply a specific validation rule."""
        try:
            if rule_id == "APIRP14FZ_EXPLOSIVE_ATMOSPHERE":
                atmosphere_class = design_data.get("explosive_atmosphere_classification", "")
                valid_classes = rule.get("valid_classifications", [])
                
                if atmosphere_class not in valid_classes:
                    result.add_violation(
                        rule_id,
                        f"Invalid explosive atmosphere classification: {atmosphere_class}",
                        rule.get("severity", "critical")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

        except Exception as e:
            logger.error(f"Failed to apply rule {rule_id}: {e}")
            result.add_violation(rule_id, f"Rule application failed: {str(e)}", "critical")
