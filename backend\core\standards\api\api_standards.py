# backend/core/standards/api/api_standards.py
"""
API Standards.

This module provides the main API standards interface for oil and gas
industry applications.
"""

import logging
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class APIStandards:
    """
    Main interface for API standards implementations.
    
    This class provides access to various API standards for oil and gas
    industry heat tracing applications.
    """

    def __init__(self):
        """Initialize API standards interface."""
        self.available_standards = {}
        self.common_parameters = self._load_common_parameters()
        logger.debug("APIStandards initialized")

    def get_available_standards(self) -> List[Dict[str, Any]]:
        """
        Get list of available API standards.

        Returns:
            List of available API standards with metadata
        """
        return [
            {
                "standard_id": "API-RP-14F",
                "title": "Recommended Practice for Design and Installation of Electrical Systems for Fixed and Floating Offshore Petroleum Facilities for Unclassified and Class I, Zone 0, Zone 1, and Zone 2 Locations",
                "version": "2019",
                "description": "Electrical systems for offshore petroleum facilities",
                "application_areas": ["offshore", "petroleum", "hazardous_areas"],
                "status": "active",
            },
            {
                "standard_id": "API-RP-14FZ",
                "title": "Recommended Practice for Design, Installation, and Maintenance of Electrical Systems for Fixed Offshore Platforms Operating in Explosive Atmospheres",
                "version": "2013",
                "description": "Electrical systems for offshore platforms in explosive atmospheres",
                "application_areas": ["offshore", "platforms", "explosive_atmospheres"],
                "status": "active",
            },
        ]

    def validate_offshore_design(
        self,
        design_data: Dict[str, Any],
        standard_ids: Optional[List[str]] = None,
    ) -> Dict[str, ValidationResult]:
        """
        Validate offshore design against API standards.

        Args:
            design_data: Offshore design data
            standard_ids: Specific API standards to validate against

        Returns:
            Dict mapping standard IDs to validation results
        """
        logger.info("Validating offshore design against API standards")

        try:
            # Use all available standards if none specified
            if standard_ids is None:
                standard_ids = [std["standard_id"] for std in self.get_available_standards()]

            results = {}

            for standard_id in standard_ids:
                try:
                    if standard_id == "API-RP-14F":
                        from .api_rp_14f import APIRP14F
                        standard = APIRP14F()
                        result = standard.validate_design(design_data)
                        results[standard_id] = result
                    
                    elif standard_id == "API-RP-14FZ":
                        from .api_rp_14fz import APIRP14FZ
                        standard = APIRP14FZ()
                        result = standard.validate_design(design_data)
                        results[standard_id] = result
                    
                    else:
                        logger.warning(f"Unknown API standard: {standard_id}")

                except Exception as e:
                    logger.error(f"Validation failed for {standard_id}: {e}")
                    # Create error result
                    error_result = ValidationResult()
                    error_result.add_violation(
                        "VALIDATION_ERROR",
                        f"Validation failed: {str(e)}",
                        "critical"
                    )
                    results[standard_id] = error_result

            return results

        except Exception as e:
            logger.error(f"API standards validation failed: {e}")
            raise CalculationError(f"API standards validation failed: {str(e)}")

    def _load_common_parameters(self) -> Dict[str, Any]:
        """Load common API parameters."""
        return {
            "offshore_voltages": [24, 48, 110, 220, 440, 480],
            "environmental_factors": {
                "salt_spray": 1.2,
                "high_humidity": 1.1,
                "temperature_cycling": 1.15,
            },
            "safety_factors": {
                "offshore": 1.5,
                "hazardous_area": 1.4,
                "critical_service": 1.6,
            },
        }
