# backend/core/standards/general/base_standard.py
"""
Base Standard.

This module provides the base class for all engineering standards implementations,
defining common interfaces and functionality.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum

from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class StandardType(Enum):
    """Enumeration of standard types."""
    IEEE = "ieee"
    IEC = "iec"
    API = "api"
    NFPA = "nfpa"
    ISO = "iso"
    ANSI = "ansi"
    ASTM = "astm"
    EN = "en"
    TR = "tr"


class ComplianceLevel(Enum):
    """Enumeration of compliance levels."""
    COMPLIANT = "compliant"
    NON_COMPLIANT = "non_compliant"
    WARNING = "warning"
    NOT_APPLICABLE = "not_applicable"
    UNKNOWN = "unknown"


class ValidationResult:
    """Container for validation results."""
    
    def __init__(self):
        self.is_compliant = True
        self.compliance_level = ComplianceLevel.COMPLIANT
        self.violations = []
        self.warnings = []
        self.recommendations = []
        self.applied_rules = []
        self.metadata = {}

    def add_violation(self, rule_id: str, description: str, severity: str = "error"):
        """Add a compliance violation."""
        violation = {
            "rule_id": rule_id,
            "description": description,
            "severity": severity,
            "timestamp": datetime.now().isoformat(),
        }
        self.violations.append(violation)
        self.is_compliant = False
        if self.compliance_level == ComplianceLevel.COMPLIANT:
            self.compliance_level = ComplianceLevel.NON_COMPLIANT

    def add_warning(self, rule_id: str, description: str):
        """Add a compliance warning."""
        warning = {
            "rule_id": rule_id,
            "description": description,
            "timestamp": datetime.now().isoformat(),
        }
        self.warnings.append(warning)
        if self.compliance_level == ComplianceLevel.COMPLIANT:
            self.compliance_level = ComplianceLevel.WARNING

    def add_recommendation(self, description: str, priority: str = "medium"):
        """Add a recommendation."""
        recommendation = {
            "description": description,
            "priority": priority,
            "timestamp": datetime.now().isoformat(),
        }
        self.recommendations.append(recommendation)

    def add_applied_rule(self, rule_id: str, description: str, result: str):
        """Add information about an applied rule."""
        applied_rule = {
            "rule_id": rule_id,
            "description": description,
            "result": result,
            "timestamp": datetime.now().isoformat(),
        }
        self.applied_rules.append(applied_rule)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "is_compliant": self.is_compliant,
            "compliance_level": self.compliance_level.value,
            "violations_count": len(self.violations),
            "warnings_count": len(self.warnings),
            "recommendations_count": len(self.recommendations),
            "violations": self.violations,
            "warnings": self.warnings,
            "recommendations": self.recommendations,
            "applied_rules": self.applied_rules,
            "metadata": self.metadata,
        }


class BaseStandard(ABC):
    """
    Base class for all engineering standards implementations.
    
    This class defines the common interface and functionality that all
    standards implementations must provide.
    """

    def __init__(self, standard_id: str, title: str, version: str, standard_type: StandardType):
        """
        Initialize the base standard.

        Args:
            standard_id: Unique identifier for the standard (e.g., "IEEE-515-2017")
            title: Full title of the standard
            version: Version or year of the standard
            standard_type: Type of standard (IEEE, IEC, etc.)
        """
        self.standard_id = standard_id
        self.title = title
        self.version = version
        self.standard_type = standard_type
        self.rules = {}
        self.parameters = {}
        self.metadata = {
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
        }
        
        logger.debug(f"Initialized standard: {self.standard_id}")

    @abstractmethod
    def validate_design(self, design_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate a design against this standard.

        Args:
            design_data: Design data to validate

        Returns:
            ValidationResult with compliance information

        Raises:
            InvalidInputError: If design data is invalid
            CalculationError: If validation fails
        """
        pass

    @abstractmethod
    def get_applicable_rules(self, design_data: Dict[str, Any]) -> List[str]:
        """
        Get list of rules applicable to the given design.

        Args:
            design_data: Design data to analyze

        Returns:
            List of applicable rule IDs
        """
        pass

    @abstractmethod
    def calculate_parameters(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate standard-specific parameters.

        Args:
            input_data: Input data for calculations

        Returns:
            Dict with calculated parameters

        Raises:
            CalculationError: If calculation fails
        """
        pass

    def get_standard_info(self) -> Dict[str, Any]:
        """
        Get information about this standard.

        Returns:
            Dict with standard information
        """
        return {
            "standard_id": self.standard_id,
            "title": self.title,
            "version": self.version,
            "standard_type": self.standard_type.value,
            "rules_count": len(self.rules),
            "parameters_count": len(self.parameters),
            "metadata": self.metadata,
        }

    def add_rule(self, rule_id: str, rule_data: Dict[str, Any]):
        """
        Add a rule to this standard.

        Args:
            rule_id: Unique identifier for the rule
            rule_data: Rule data including description, conditions, etc.
        """
        self.rules[rule_id] = {
            **rule_data,
            "added_at": datetime.now().isoformat(),
        }
        self.metadata["last_updated"] = datetime.now().isoformat()

    def get_rule(self, rule_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific rule by ID.

        Args:
            rule_id: Rule identifier

        Returns:
            Rule data or None if not found
        """
        return self.rules.get(rule_id)

    def get_all_rules(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all rules for this standard.

        Returns:
            Dict mapping rule IDs to rule data
        """
        return self.rules.copy()

    def add_parameter(self, param_id: str, param_data: Dict[str, Any]):
        """
        Add a parameter to this standard.

        Args:
            param_id: Unique identifier for the parameter
            param_data: Parameter data including value, units, description, etc.
        """
        self.parameters[param_id] = {
            **param_data,
            "added_at": datetime.now().isoformat(),
        }
        self.metadata["last_updated"] = datetime.now().isoformat()

    def get_parameter(self, param_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a specific parameter by ID.

        Args:
            param_id: Parameter identifier

        Returns:
            Parameter data or None if not found
        """
        return self.parameters.get(param_id)

    def get_all_parameters(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all parameters for this standard.

        Returns:
            Dict mapping parameter IDs to parameter data
        """
        return self.parameters.copy()

    def validate_input_data(self, data: Dict[str, Any], required_fields: List[str]) -> None:
        """
        Validate input data for required fields.

        Args:
            data: Data to validate
            required_fields: List of required field names

        Raises:
            InvalidInputError: If required fields are missing
        """
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)

        if missing_fields:
            raise InvalidInputError(
                f"Missing required fields for {self.standard_id}: {missing_fields}"
            )

    def check_numeric_range(
        self,
        value: Union[int, float],
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None,
        field_name: str = "value",
    ) -> bool:
        """
        Check if a numeric value is within specified range.

        Args:
            value: Value to check
            min_value: Minimum allowed value (optional)
            max_value: Maximum allowed value (optional)
            field_name: Name of the field for error messages

        Returns:
            True if value is within range

        Raises:
            InvalidInputError: If value is out of range
        """
        if min_value is not None and value < min_value:
            raise InvalidInputError(
                f"{field_name} ({value}) is below minimum allowed value ({min_value})"
            )

        if max_value is not None and value > max_value:
            raise InvalidInputError(
                f"{field_name} ({value}) is above maximum allowed value ({max_value})"
            )

        return True

    def apply_safety_factor(
        self,
        base_value: Union[int, float],
        safety_factor: Union[int, float],
        rule_id: str,
    ) -> Union[int, float]:
        """
        Apply safety factor to a base value.

        Args:
            base_value: Base value to apply factor to
            safety_factor: Safety factor to apply
            rule_id: Rule ID for tracking

        Returns:
            Value with safety factor applied
        """
        result = base_value * safety_factor
        
        logger.debug(
            f"Applied safety factor {safety_factor} to {base_value} = {result} "
            f"(rule: {rule_id})"
        )
        
        return result

    def interpolate_table_value(
        self,
        table_data: List[Dict[str, Union[int, float]]],
        input_value: Union[int, float],
        input_key: str,
        output_key: str,
    ) -> Union[int, float]:
        """
        Interpolate value from a table.

        Args:
            table_data: List of table rows with numeric data
            input_value: Input value to interpolate for
            input_key: Key name for input values in table
            output_key: Key name for output values in table

        Returns:
            Interpolated output value

        Raises:
            InvalidInputError: If interpolation fails
        """
        if not table_data:
            raise InvalidInputError("Table data is empty")

        # Sort table by input key
        sorted_table = sorted(table_data, key=lambda x: x[input_key])

        # Check if input value is within table range
        min_input = sorted_table[0][input_key]
        max_input = sorted_table[-1][input_key]

        if input_value < min_input or input_value > max_input:
            raise InvalidInputError(
                f"Input value {input_value} is outside table range [{min_input}, {max_input}]"
            )

        # Find interpolation points
        for i in range(len(sorted_table) - 1):
            x1, y1 = sorted_table[i][input_key], sorted_table[i][output_key]
            x2, y2 = sorted_table[i + 1][input_key], sorted_table[i + 1][output_key]

            if x1 <= input_value <= x2:
                # Linear interpolation
                if x2 == x1:
                    return y1
                else:
                    return y1 + (y2 - y1) * (input_value - x1) / (x2 - x1)

        # Should not reach here if input validation passed
        raise CalculationError("Interpolation failed")

    def __str__(self) -> str:
        """String representation of the standard."""
        return f"{self.standard_id} - {self.title} ({self.version})"

    def __repr__(self) -> str:
        """Detailed string representation."""
        return (
            f"BaseStandard(standard_id='{self.standard_id}', "
            f"title='{self.title}', version='{self.version}', "
            f"type='{self.standard_type.value}')"
        )
