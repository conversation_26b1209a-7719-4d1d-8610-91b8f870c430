# backend/core/standards/general/standards_calculator.py
"""
Standards Calculator.

This module provides calculation functionality for engineering standards,
including parameter calculations, safety factors, and design values.
"""

import logging
import math
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime

from .base_standard import BaseStandard
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class CalculationResult:
    """Container for calculation results."""
    
    def __init__(self):
        self.success = True
        self.calculated_values = {}
        self.applied_standards = []
        self.calculation_steps = []
        self.warnings = []
        self.metadata = {}

    def add_calculated_value(self, name: str, value: Any, unit: str = "", description: str = ""):
        """Add a calculated value."""
        self.calculated_values[name] = {
            "value": value,
            "unit": unit,
            "description": description,
            "calculated_at": datetime.now().isoformat(),
        }

    def add_calculation_step(self, step_name: str, formula: str, inputs: Dict[str, Any], result: Any):
        """Add a calculation step for traceability."""
        step = {
            "step_name": step_name,
            "formula": formula,
            "inputs": inputs,
            "result": result,
            "calculated_at": datetime.now().isoformat(),
        }
        self.calculation_steps.append(step)

    def add_warning(self, message: str, category: str = "general"):
        """Add a calculation warning."""
        warning = {
            "message": message,
            "category": category,
            "timestamp": datetime.now().isoformat(),
        }
        self.warnings.append(warning)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "success": self.success,
            "calculated_values": self.calculated_values,
            "applied_standards": self.applied_standards,
            "calculation_steps": self.calculation_steps,
            "warnings": self.warnings,
            "metadata": self.metadata,
        }


class StandardsCalculator:
    """
    Calculator for engineering standards-based calculations.
    
    This class provides functionality to perform calculations according to
    various engineering standards and generate traceable results.
    """

    def __init__(self):
        """Initialize the standards calculator."""
        self.registered_standards = {}
        self.calculation_history = []
        self.default_parameters = {}
        logger.debug("StandardsCalculator initialized")

    def register_standard(self, standard: BaseStandard):
        """
        Register a standard for calculations.

        Args:
            standard: Standard implementation to register

        Raises:
            InvalidInputError: If standard is invalid
        """
        if not isinstance(standard, BaseStandard):
            raise InvalidInputError("Standard must be an instance of BaseStandard")

        self.registered_standards[standard.standard_id] = standard
        logger.info(f"Registered standard for calculations: {standard.standard_id}")

    def calculate_with_standard(
        self,
        standard_id: str,
        input_data: Dict[str, Any],
        calculation_options: Optional[Dict[str, Any]] = None,
    ) -> CalculationResult:
        """
        Perform calculations using a specific standard.

        Args:
            standard_id: ID of standard to use for calculations
            input_data: Input data for calculations
            calculation_options: Optional calculation configuration

        Returns:
            CalculationResult with calculated values

        Raises:
            InvalidInputError: If standard not found or data invalid
            CalculationError: If calculation fails
        """
        logger.info(f"Performing calculations with standard: {standard_id}")

        try:
            # Check if standard is registered
            if standard_id not in self.registered_standards:
                raise InvalidInputError(f"Standard not registered: {standard_id}")

            standard = self.registered_standards[standard_id]

            # Validate input data
            if not input_data:
                raise InvalidInputError("Input data cannot be empty")

            # Apply calculation options
            options = calculation_options or {}
            
            # Perform calculations
            start_time = datetime.now()
            calculated_params = standard.calculate_parameters(input_data)
            calculation_time = (datetime.now() - start_time).total_seconds()

            # Create result
            result = CalculationResult()
            
            # Add calculated values
            for param_name, param_value in calculated_params.items():
                if isinstance(param_value, dict) and "value" in param_value:
                    result.add_calculated_value(
                        param_name,
                        param_value["value"],
                        param_value.get("unit", ""),
                        param_value.get("description", "")
                    )
                else:
                    result.add_calculated_value(param_name, param_value)

            # Add metadata
            result.applied_standards.append(standard_id)
            result.metadata.update({
                "standard_id": standard_id,
                "calculation_time_seconds": calculation_time,
                "calculated_at": datetime.now().isoformat(),
                "calculation_options": options,
                "input_data_hash": self._hash_input_data(input_data),
            })

            # Record in history
            self._add_to_history({
                "standard_id": standard_id,
                "calculation_result": result.to_dict(),
                "input_data_hash": self._hash_input_data(input_data),
                "calculated_at": datetime.now().isoformat(),
            })

            logger.info(f"Calculations completed: {len(result.calculated_values)} values")
            return result

        except Exception as e:
            logger.error(f"Calculation failed for standard {standard_id}: {e}")
            raise CalculationError(f"Calculation failed: {str(e)}")

    def calculate_heat_loss_parameters(
        self,
        pipe_data: Dict[str, Any],
        environmental_data: Dict[str, Any],
        standard_id: Optional[str] = None,
    ) -> CalculationResult:
        """
        Calculate heat loss parameters according to standards.

        Args:
            pipe_data: Pipe and insulation data
            environmental_data: Environmental conditions
            standard_id: Specific standard to use (optional)

        Returns:
            CalculationResult with heat loss calculations
        """
        logger.info("Calculating heat loss parameters")

        try:
            result = CalculationResult()

            # Extract parameters
            pipe_diameter = pipe_data.get("diameter", 0.1)  # meters
            insulation_thickness = pipe_data.get("insulation_thickness", 0.05)  # meters
            insulation_conductivity = pipe_data.get("insulation_conductivity", 0.04)  # W/m·K
            
            maintain_temp = environmental_data.get("maintain_temperature", 60)  # °C
            ambient_temp = environmental_data.get("ambient_temperature", 0)  # °C
            wind_speed = environmental_data.get("wind_speed", 0)  # m/s

            # Calculate thermal resistances
            r_insulation = self._calculate_insulation_resistance(
                pipe_diameter, insulation_thickness, insulation_conductivity
            )
            result.add_calculation_step(
                "insulation_resistance",
                "ln(r_outer/r_inner) / (2π * k * L)",
                {
                    "pipe_diameter": pipe_diameter,
                    "insulation_thickness": insulation_thickness,
                    "conductivity": insulation_conductivity,
                },
                r_insulation
            )

            r_convection = self._calculate_convection_resistance(
                pipe_diameter + 2 * insulation_thickness, wind_speed
            )
            result.add_calculation_step(
                "convection_resistance",
                "1 / (h * A)",
                {
                    "outer_diameter": pipe_diameter + 2 * insulation_thickness,
                    "wind_speed": wind_speed,
                },
                r_convection
            )

            # Total thermal resistance
            r_total = r_insulation + r_convection
            result.add_calculated_value(
                "total_thermal_resistance", r_total, "K·m/W", 
                "Total thermal resistance per unit length"
            )

            # Heat loss per unit length
            temp_diff = maintain_temp - ambient_temp
            heat_loss_per_meter = temp_diff / r_total
            result.add_calculated_value(
                "heat_loss_per_meter", heat_loss_per_meter, "W/m",
                "Heat loss per unit length"
            )

            # Apply safety factors if standard specified
            if standard_id and standard_id in self.registered_standards:
                standard = self.registered_standards[standard_id]
                safety_factor_param = standard.get_parameter("heat_loss_safety_factor")
                if safety_factor_param:
                    safety_factor = safety_factor_param.get("value", 1.2)
                    design_heat_loss = heat_loss_per_meter * safety_factor
                    result.add_calculated_value(
                        "design_heat_loss_per_meter", design_heat_loss, "W/m",
                        f"Design heat loss with safety factor {safety_factor}"
                    )
                    result.applied_standards.append(standard_id)

            logger.info(f"Heat loss calculation completed: {heat_loss_per_meter:.2f} W/m")
            return result

        except Exception as e:
            logger.error(f"Heat loss calculation failed: {e}")
            raise CalculationError(f"Heat loss calculation failed: {str(e)}")

    def calculate_power_requirements(
        self,
        heat_loss_data: Dict[str, Any],
        circuit_data: Dict[str, Any],
        standard_id: Optional[str] = None,
    ) -> CalculationResult:
        """
        Calculate power requirements according to standards.

        Args:
            heat_loss_data: Heat loss calculation results
            circuit_data: Circuit configuration data
            standard_id: Specific standard to use (optional)

        Returns:
            CalculationResult with power calculations
        """
        logger.info("Calculating power requirements")

        try:
            result = CalculationResult()

            # Extract parameters
            heat_loss_per_meter = heat_loss_data.get("heat_loss_per_meter", 0)
            circuit_length = circuit_data.get("length", 0)
            application_type = circuit_data.get("application_type", "standard")

            # Calculate base power requirement
            base_power = heat_loss_per_meter * circuit_length
            result.add_calculated_value(
                "base_power_requirement", base_power, "W",
                "Base power requirement without safety factors"
            )

            # Apply safety factors based on application type
            safety_factors = {
                "critical": 1.5,
                "standard": 1.3,
                "non_critical": 1.2,
            }
            
            safety_factor = safety_factors.get(application_type, 1.3)
            
            # Apply standard-specific safety factors if available
            if standard_id and standard_id in self.registered_standards:
                standard = self.registered_standards[standard_id]
                std_safety_param = standard.get_parameter(f"safety_factor_{application_type}")
                if std_safety_param:
                    safety_factor = std_safety_param.get("value", safety_factor)
                    result.applied_standards.append(standard_id)

            design_power = base_power * safety_factor
            result.add_calculated_value(
                "design_power_requirement", design_power, "W",
                f"Design power with safety factor {safety_factor}"
            )

            result.add_calculation_step(
                "power_calculation",
                "P_design = P_base * safety_factor",
                {
                    "base_power": base_power,
                    "safety_factor": safety_factor,
                    "application_type": application_type,
                },
                design_power
            )

            # Calculate power density
            power_per_meter = design_power / circuit_length if circuit_length > 0 else 0
            result.add_calculated_value(
                "power_per_meter", power_per_meter, "W/m",
                "Required power density"
            )

            logger.info(f"Power calculation completed: {design_power:.2f} W")
            return result

        except Exception as e:
            logger.error(f"Power calculation failed: {e}")
            raise CalculationError(f"Power calculation failed: {str(e)}")

    def calculate_electrical_parameters(
        self,
        power_data: Dict[str, Any],
        electrical_data: Dict[str, Any],
        standard_id: Optional[str] = None,
    ) -> CalculationResult:
        """
        Calculate electrical parameters according to standards.

        Args:
            power_data: Power requirement data
            electrical_data: Electrical system data
            standard_id: Specific standard to use (optional)

        Returns:
            CalculationResult with electrical calculations
        """
        logger.info("Calculating electrical parameters")

        try:
            result = CalculationResult()

            # Extract parameters
            total_power = power_data.get("design_power_requirement", 0)  # W
            voltage = electrical_data.get("voltage", 240)  # V
            power_factor = electrical_data.get("power_factor", 1.0)
            circuit_type = electrical_data.get("circuit_type", "single_phase")

            # Calculate current
            if circuit_type == "single_phase":
                current = total_power / (voltage * power_factor)
            elif circuit_type == "three_phase":
                current = total_power / (math.sqrt(3) * voltage * power_factor)
            else:
                raise InvalidInputError(f"Unknown circuit type: {circuit_type}")

            result.add_calculated_value(
                "operating_current", current, "A",
                f"Operating current for {circuit_type} circuit"
            )

            result.add_calculation_step(
                "current_calculation",
                f"I = P / ({'√3 * ' if circuit_type == 'three_phase' else ''}V * pf)",
                {
                    "power": total_power,
                    "voltage": voltage,
                    "power_factor": power_factor,
                    "circuit_type": circuit_type,
                },
                current
            )

            # Apply derating factors if standard specified
            if standard_id and standard_id in self.registered_standards:
                standard = self.registered_standards[standard_id]
                derating_param = standard.get_parameter("current_derating_factor")
                if derating_param:
                    derating_factor = derating_param.get("value", 0.8)
                    derated_current = current / derating_factor
                    result.add_calculated_value(
                        "derated_current", derated_current, "A",
                        f"Current with derating factor {derating_factor}"
                    )
                    result.applied_standards.append(standard_id)

            # Calculate cable sizing requirements
            cable_current = current * 1.25  # 125% rule
            result.add_calculated_value(
                "minimum_cable_current", cable_current, "A",
                "Minimum cable current rating (125% of operating current)"
            )

            logger.info(f"Electrical calculation completed: {current:.2f} A")
            return result

        except Exception as e:
            logger.error(f"Electrical calculation failed: {e}")
            raise CalculationError(f"Electrical calculation failed: {str(e)}")

    def _calculate_insulation_resistance(
        self,
        pipe_diameter: float,
        insulation_thickness: float,
        conductivity: float,
    ) -> float:
        """Calculate thermal resistance of insulation."""
        r_inner = pipe_diameter / 2
        r_outer = r_inner + insulation_thickness
        
        if r_outer <= r_inner:
            raise InvalidInputError("Outer radius must be greater than inner radius")
        
        resistance = math.log(r_outer / r_inner) / (2 * math.pi * conductivity)
        return resistance

    def _calculate_convection_resistance(
        self,
        outer_diameter: float,
        wind_speed: float,
    ) -> float:
        """Calculate convection thermal resistance."""
        # Simplified convection coefficient calculation
        # In practice, this would use more sophisticated correlations
        
        base_h = 10.0  # W/m²·K (natural convection)
        wind_factor = 1 + 0.1 * wind_speed  # Simplified wind effect
        h_conv = base_h * wind_factor
        
        surface_area_per_length = math.pi * outer_diameter
        resistance = 1 / (h_conv * surface_area_per_length)
        
        return resistance

    def get_calculation_history(
        self,
        standard_id: Optional[str] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Get calculation history with optional filtering.

        Args:
            standard_id: Filter by standard ID (optional)
            limit: Maximum number of records

        Returns:
            List of calculation history records
        """
        logger.debug(f"Getting calculation history: standard_id={standard_id}")

        try:
            filtered_history = self.calculation_history

            # Apply filters
            if standard_id:
                filtered_history = [
                    h for h in filtered_history 
                    if h.get("standard_id") == standard_id
                ]

            # Sort by calculated_at descending and limit
            filtered_history.sort(key=lambda x: x.get("calculated_at", ""), reverse=True)
            return filtered_history[:limit]

        except Exception as e:
            logger.error(f"Failed to get calculation history: {e}")
            return []

    def _add_to_history(self, record: Dict[str, Any]):
        """Add record to calculation history."""
        self.calculation_history.append(record)
        
        # Keep only last 1000 entries
        if len(self.calculation_history) > 1000:
            self.calculation_history = self.calculation_history[-1000:]

    def _hash_input_data(self, input_data: Dict[str, Any]) -> str:
        """Generate hash of input data for tracking."""
        import hashlib
        import json
        
        # Create a stable string representation
        data_str = json.dumps(input_data, sort_keys=True, default=str)
        return hashlib.md5(data_str.encode()).hexdigest()
