# backend/core/standards/general/standards_validator.py
"""
Standards Validator.

This module provides comprehensive validation functionality for engineering
standards compliance checking and design validation.
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from datetime import datetime

from .base_standard import BaseStandard, ValidationResult, ComplianceLevel
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class StandardsValidator:
    """
    Comprehensive validator for engineering standards compliance.
    
    This class provides functionality to validate designs against multiple
    standards simultaneously and generate comprehensive compliance reports.
    """

    def __init__(self):
        """Initialize the standards validator."""
        self.registered_standards = {}
        self.validation_history = []
        self.default_validation_rules = {}
        logger.debug("StandardsValidator initialized")

    def register_standard(self, standard: BaseStandard):
        """
        Register a standard for validation.

        Args:
            standard: Standard implementation to register

        Raises:
            InvalidInputError: If standard is invalid
        """
        if not isinstance(standard, BaseStandard):
            raise InvalidInputError("Standard must be an instance of BaseStandard")

        self.registered_standards[standard.standard_id] = standard
        logger.info(f"Registered standard: {standard.standard_id}")

    def unregister_standard(self, standard_id: str) -> bool:
        """
        Unregister a standard.

        Args:
            standard_id: ID of standard to unregister

        Returns:
            True if standard was unregistered, False if not found
        """
        if standard_id in self.registered_standards:
            del self.registered_standards[standard_id]
            logger.info(f"Unregistered standard: {standard_id}")
            return True
        return False

    def get_registered_standards(self) -> List[str]:
        """
        Get list of registered standard IDs.

        Returns:
            List of registered standard IDs
        """
        return list(self.registered_standards.keys())

    def validate_against_standard(
        self,
        design_data: Dict[str, Any],
        standard_id: str,
        validation_options: Optional[Dict[str, Any]] = None,
    ) -> ValidationResult:
        """
        Validate design against a specific standard.

        Args:
            design_data: Design data to validate
            standard_id: ID of standard to validate against
            validation_options: Optional validation configuration

        Returns:
            ValidationResult with compliance information

        Raises:
            InvalidInputError: If standard not found or data invalid
            CalculationError: If validation fails
        """
        logger.info(f"Validating design against standard: {standard_id}")

        try:
            # Check if standard is registered
            if standard_id not in self.registered_standards:
                raise InvalidInputError(f"Standard not registered: {standard_id}")

            standard = self.registered_standards[standard_id]

            # Validate design data
            if not design_data:
                raise InvalidInputError("Design data cannot be empty")

            # Apply validation options
            options = validation_options or {}
            
            # Perform validation
            start_time = datetime.now()
            result = standard.validate_design(design_data)
            validation_time = (datetime.now() - start_time).total_seconds()

            # Add metadata
            result.metadata.update({
                "standard_id": standard_id,
                "validation_time_seconds": validation_time,
                "validated_at": datetime.now().isoformat(),
                "validation_options": options,
            })

            # Record in history
            self._add_to_history({
                "standard_id": standard_id,
                "validation_result": result.to_dict(),
                "design_data_hash": self._hash_design_data(design_data),
                "validated_at": datetime.now().isoformat(),
            })

            logger.info(f"Validation completed: {result.compliance_level.value}")
            return result

        except Exception as e:
            logger.error(f"Validation failed for standard {standard_id}: {e}")
            raise CalculationError(f"Validation failed: {str(e)}")

    def validate_against_multiple_standards(
        self,
        design_data: Dict[str, Any],
        standard_ids: List[str],
        validation_options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, ValidationResult]:
        """
        Validate design against multiple standards.

        Args:
            design_data: Design data to validate
            standard_ids: List of standard IDs to validate against
            validation_options: Optional validation configuration

        Returns:
            Dict mapping standard IDs to validation results

        Raises:
            InvalidInputError: If any standard not found
            CalculationError: If validation fails
        """
        logger.info(f"Validating design against {len(standard_ids)} standards")

        try:
            results = {}
            
            for standard_id in standard_ids:
                try:
                    result = self.validate_against_standard(
                        design_data, standard_id, validation_options
                    )
                    results[standard_id] = result
                except Exception as e:
                    logger.error(f"Validation failed for standard {standard_id}: {e}")
                    # Create error result
                    error_result = ValidationResult()
                    error_result.add_violation(
                        "VALIDATION_ERROR",
                        f"Validation failed: {str(e)}",
                        "critical"
                    )
                    results[standard_id] = error_result

            logger.info(f"Multi-standard validation completed: {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Multi-standard validation failed: {e}")
            raise CalculationError(f"Multi-standard validation failed: {str(e)}")

    def generate_compliance_report(
        self,
        validation_results: Dict[str, ValidationResult],
        design_data: Dict[str, Any],
        report_options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Generate comprehensive compliance report.

        Args:
            validation_results: Results from validation
            design_data: Original design data
            report_options: Optional report configuration

        Returns:
            Dict with comprehensive compliance report
        """
        logger.info("Generating compliance report")

        try:
            options = report_options or {}
            
            # Overall compliance summary
            total_standards = len(validation_results)
            compliant_standards = sum(
                1 for result in validation_results.values() 
                if result.compliance_level == ComplianceLevel.COMPLIANT
            )
            
            overall_compliance = compliant_standards / total_standards if total_standards > 0 else 0

            # Collect all violations and warnings
            all_violations = []
            all_warnings = []
            all_recommendations = []

            for standard_id, result in validation_results.items():
                for violation in result.violations:
                    violation["standard_id"] = standard_id
                    all_violations.append(violation)
                
                for warning in result.warnings:
                    warning["standard_id"] = standard_id
                    all_warnings.append(warning)
                
                for recommendation in result.recommendations:
                    recommendation["standard_id"] = standard_id
                    all_recommendations.append(recommendation)

            # Categorize violations by severity
            critical_violations = [v for v in all_violations if v.get("severity") == "critical"]
            major_violations = [v for v in all_violations if v.get("severity") == "major"]
            minor_violations = [v for v in all_violations if v.get("severity") == "minor"]

            # Generate report
            report = {
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "report_type": "compliance_report",
                    "standards_evaluated": list(validation_results.keys()),
                    "report_options": options,
                },
                "compliance_summary": {
                    "overall_compliance_percentage": overall_compliance * 100,
                    "total_standards": total_standards,
                    "compliant_standards": compliant_standards,
                    "non_compliant_standards": total_standards - compliant_standards,
                    "overall_status": self._determine_overall_status(validation_results),
                },
                "violations_summary": {
                    "total_violations": len(all_violations),
                    "critical_violations": len(critical_violations),
                    "major_violations": len(major_violations),
                    "minor_violations": len(minor_violations),
                    "total_warnings": len(all_warnings),
                    "total_recommendations": len(all_recommendations),
                },
                "standard_results": {
                    standard_id: result.to_dict()
                    for standard_id, result in validation_results.items()
                },
                "detailed_violations": all_violations,
                "detailed_warnings": all_warnings,
                "recommendations": all_recommendations,
                "compliance_matrix": self._generate_compliance_matrix(validation_results),
            }

            # Add design data summary if requested
            if options.get("include_design_summary", True):
                report["design_summary"] = self._generate_design_summary(design_data)

            logger.info(f"Compliance report generated: {overall_compliance:.1%} compliance")
            return report

        except Exception as e:
            logger.error(f"Compliance report generation failed: {e}")
            raise CalculationError(f"Compliance report generation failed: {str(e)}")

    def check_cross_standard_conflicts(
        self,
        validation_results: Dict[str, ValidationResult],
    ) -> List[Dict[str, Any]]:
        """
        Check for conflicts between different standards.

        Args:
            validation_results: Results from multiple standards

        Returns:
            List of detected conflicts
        """
        logger.debug("Checking for cross-standard conflicts")

        conflicts = []

        try:
            # Check for conflicting requirements
            # This is a simplified implementation - real conflicts would be more complex
            
            standards_list = list(validation_results.keys())
            
            for i, std1 in enumerate(standards_list):
                for std2 in standards_list[i+1:]:
                    result1 = validation_results[std1]
                    result2 = validation_results[std2]
                    
                    # Check if one standard is compliant and another is not for similar rules
                    conflict = self._detect_rule_conflicts(std1, result1, std2, result2)
                    if conflict:
                        conflicts.append(conflict)

            logger.debug(f"Found {len(conflicts)} cross-standard conflicts")
            return conflicts

        except Exception as e:
            logger.error(f"Cross-standard conflict checking failed: {e}")
            return []

    def get_validation_history(
        self,
        standard_id: Optional[str] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        Get validation history with optional filtering.

        Args:
            standard_id: Filter by standard ID (optional)
            limit: Maximum number of records

        Returns:
            List of validation history records
        """
        logger.debug(f"Getting validation history: standard_id={standard_id}")

        try:
            filtered_history = self.validation_history

            # Apply filters
            if standard_id:
                filtered_history = [
                    h for h in filtered_history 
                    if h.get("standard_id") == standard_id
                ]

            # Sort by validated_at descending and limit
            filtered_history.sort(key=lambda x: x.get("validated_at", ""), reverse=True)
            return filtered_history[:limit]

        except Exception as e:
            logger.error(f"Failed to get validation history: {e}")
            return []

    def _determine_overall_status(self, validation_results: Dict[str, ValidationResult]) -> str:
        """Determine overall compliance status."""
        if not validation_results:
            return "unknown"

        compliance_levels = [result.compliance_level for result in validation_results.values()]
        
        if all(level == ComplianceLevel.COMPLIANT for level in compliance_levels):
            return "compliant"
        elif any(level == ComplianceLevel.NON_COMPLIANT for level in compliance_levels):
            return "non_compliant"
        elif any(level == ComplianceLevel.WARNING for level in compliance_levels):
            return "warning"
        else:
            return "unknown"

    def _generate_compliance_matrix(
        self, 
        validation_results: Dict[str, ValidationResult]
    ) -> Dict[str, Dict[str, str]]:
        """Generate compliance matrix showing status for each standard."""
        matrix = {}
        
        for standard_id, result in validation_results.items():
            matrix[standard_id] = {
                "compliance_level": result.compliance_level.value,
                "violations_count": len(result.violations),
                "warnings_count": len(result.warnings),
                "is_compliant": result.is_compliant,
            }
        
        return matrix

    def _generate_design_summary(self, design_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of design data."""
        summary = {
            "total_fields": len(design_data),
            "field_types": {},
            "key_parameters": {},
        }

        # Analyze field types
        for key, value in design_data.items():
            value_type = type(value).__name__
            summary["field_types"][value_type] = summary["field_types"].get(value_type, 0) + 1

        # Extract key parameters (simplified)
        key_fields = [
            "project_name", "project_type", "design_temperature", "ambient_temperature",
            "pipe_diameter", "insulation_type", "cable_type", "voltage", "power_density"
        ]
        
        for field in key_fields:
            if field in design_data:
                summary["key_parameters"][field] = design_data[field]

        return summary

    def _detect_rule_conflicts(
        self,
        std1: str,
        result1: ValidationResult,
        std2: str,
        result2: ValidationResult,
    ) -> Optional[Dict[str, Any]]:
        """Detect conflicts between two standards."""
        # Simplified conflict detection
        # In a real implementation, this would be much more sophisticated
        
        if (result1.compliance_level == ComplianceLevel.COMPLIANT and 
            result2.compliance_level == ComplianceLevel.NON_COMPLIANT):
            
            return {
                "conflict_type": "compliance_mismatch",
                "standard_1": std1,
                "standard_2": std2,
                "description": f"{std1} is compliant but {std2} is not compliant",
                "severity": "medium",
                "detected_at": datetime.now().isoformat(),
            }
        
        return None

    def _add_to_history(self, record: Dict[str, Any]):
        """Add record to validation history."""
        self.validation_history.append(record)
        
        # Keep only last 1000 entries
        if len(self.validation_history) > 1000:
            self.validation_history = self.validation_history[-1000:]

    def _hash_design_data(self, design_data: Dict[str, Any]) -> str:
        """Generate hash of design data for tracking."""
        import hashlib
        import json
        
        # Create a stable string representation
        data_str = json.dumps(design_data, sort_keys=True, default=str)
        return hashlib.md5(data_str.encode()).hexdigest()
