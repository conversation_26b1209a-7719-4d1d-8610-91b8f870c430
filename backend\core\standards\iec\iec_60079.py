# backend/core/standards/iec/iec_60079.py
"""
IEC 60079-30-1 Standard.

Explosive atmospheres - Part 30-1: Electrical resistance trace heating - 
General and testing requirements.
"""

import logging
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class IEC60079(BaseStandard):
    """
    IEC 60079-30-1 Standard implementation.
    
    This standard covers electrical resistance trace heating requirements
    for explosive atmospheres.
    """

    def __init__(self):
        """Initialize IEC 60079 standard."""
        super().__init__(
            standard_id="IEC-60079-30-1",
            title="Explosive atmospheres - Part 30-1: Electrical resistance trace heating - General and testing requirements",
            version="2015",
            standard_type=StandardType.IEC
        )
        
        self._load_standard_rules()
        self._load_standard_parameters()
        logger.debug("IEC 60079-30-1 standard initialized")

    def validate_design(self, design_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate design against IEC 60079 requirements.

        Args:
            design_data: Design data to validate

        Returns:
            ValidationResult with compliance information
        """
        logger.info("Validating design against IEC 60079-30-1")

        try:
            result = ValidationResult()
            
            # Required fields for IEC 60079 validation
            required_fields = [
                "zone_classification", "temperature_class", "protection_method",
                "surface_temperature", "cable_type"
            ]
            
            self.validate_input_data(design_data, required_fields)

            # Apply validation rules
            applicable_rules = self.get_applicable_rules(design_data)
            
            for rule_id in applicable_rules:
                rule = self.get_rule(rule_id)
                if rule:
                    self._apply_validation_rule(rule_id, rule, design_data, result)

            logger.info(f"IEC 60079 validation completed: {result.compliance_level.value}")
            return result

        except Exception as e:
            logger.error(f"IEC 60079 validation failed: {e}")
            raise CalculationError(f"IEC 60079 validation failed: {str(e)}")

    def get_applicable_rules(self, design_data: Dict[str, Any]) -> List[str]:
        """Get applicable IEC 60079 rules."""
        return [
            "IEC60079_ZONE_CLASSIFICATION",
            "IEC60079_TEMPERATURE_CLASS",
            "IEC60079_PROTECTION_METHOD",
            "IEC60079_SURFACE_TEMPERATURE",
            "IEC60079_CABLE_REQUIREMENTS",
        ]

    def calculate_parameters(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate IEC 60079 specific parameters."""
        logger.info("Calculating IEC 60079 parameters")

        try:
            calculated_params = {}

            # Calculate maximum surface temperature
            temp_class = input_data.get("temperature_class", "T3")
            temp_limits = {"T1": 450, "T2": 300, "T3": 200, "T4": 135, "T5": 100, "T6": 85}
            
            if temp_class in temp_limits:
                max_surface_temp = temp_limits[temp_class] - 20  # 20°C safety margin
                calculated_params["iec60079_max_surface_temperature"] = {
                    "value": max_surface_temp,
                    "unit": "°C",
                    "description": f"Maximum surface temperature for {temp_class} with safety margin",
                }

            return calculated_params

        except Exception as e:
            logger.error(f"IEC 60079 parameter calculation failed: {e}")
            raise CalculationError(f"IEC 60079 parameter calculation failed: {str(e)}")

    def _load_standard_rules(self):
        """Load IEC 60079 specific rules."""
        self.add_rule("IEC60079_ZONE_CLASSIFICATION", {
            "description": "Zone classification must be appropriate",
            "valid_zones": ["Zone 0", "Zone 1", "Zone 2"],
            "severity": "critical",
        })

        self.add_rule("IEC60079_TEMPERATURE_CLASS", {
            "description": "Temperature class requirements",
            "valid_classes": ["T1", "T2", "T3", "T4", "T5", "T6"],
            "severity": "critical",
        })

    def _load_standard_parameters(self):
        """Load IEC 60079 specific parameters."""
        self.add_parameter("temperature_safety_margin", {
            "value": 20,
            "unit": "°C",
            "description": "Temperature safety margin for hazardous areas",
        })

    def _apply_validation_rule(self, rule_id: str, rule: Dict[str, Any], design_data: Dict[str, Any], result: ValidationResult):
        """Apply a specific validation rule."""
        try:
            if rule_id == "IEC60079_ZONE_CLASSIFICATION":
                zone = design_data.get("zone_classification", "")
                valid_zones = rule.get("valid_zones", [])
                
                if zone not in valid_zones:
                    result.add_violation(
                        rule_id,
                        f"Invalid zone classification: {zone}",
                        rule.get("severity", "major")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

        except Exception as e:
            logger.error(f"Failed to apply rule {rule_id}: {e}")
            result.add_violation(rule_id, f"Rule application failed: {str(e)}", "critical")
