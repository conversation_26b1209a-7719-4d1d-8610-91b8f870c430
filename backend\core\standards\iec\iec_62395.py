# backend/core/standards/iec/iec_62395.py
"""
IEC 62395 Standard.

Electrical resistance trace heating systems for industrial 
and commercial applications.
"""

import logging
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class IEC62395(BaseStandard):
    """
    IEC 62395 Standard implementation.
    
    This standard covers electrical resistance trace heating systems
    for industrial and commercial applications.
    """

    def __init__(self):
        """Initialize IEC 62395 standard."""
        super().__init__(
            standard_id="IEC-62395",
            title="Electrical resistance trace heating systems for industrial and commercial applications",
            version="2013",
            standard_type=StandardType.IEC
        )
        
        self._load_standard_rules()
        self._load_standard_parameters()
        logger.debug("IEC 62395 standard initialized")

    def validate_design(self, design_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate design against IEC 62395 requirements.

        Args:
            design_data: Design data to validate

        Returns:
            ValidationResult with compliance information
        """
        logger.info("Validating design against IEC 62395")

        try:
            result = ValidationResult()
            
            # Required fields for IEC 62395 validation
            required_fields = [
                "voltage", "power_density", "cable_type", "installation_method"
            ]
            
            self.validate_input_data(design_data, required_fields)

            # Apply validation rules
            applicable_rules = self.get_applicable_rules(design_data)
            
            for rule_id in applicable_rules:
                rule = self.get_rule(rule_id)
                if rule:
                    self._apply_validation_rule(rule_id, rule, design_data, result)

            logger.info(f"IEC 62395 validation completed: {result.compliance_level.value}")
            return result

        except Exception as e:
            logger.error(f"IEC 62395 validation failed: {e}")
            raise CalculationError(f"IEC 62395 validation failed: {str(e)}")

    def get_applicable_rules(self, design_data: Dict[str, Any]) -> List[str]:
        """Get applicable IEC 62395 rules."""
        return [
            "IEC62395_VOLTAGE_LIMITS",
            "IEC62395_POWER_DENSITY",
            "IEC62395_CABLE_SELECTION",
            "IEC62395_INSTALLATION_METHOD",
        ]

    def calculate_parameters(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate IEC 62395 specific parameters."""
        logger.info("Calculating IEC 62395 parameters")

        try:
            calculated_params = {}

            # Calculate power requirements with IEC safety factors
            if "heat_loss_per_meter" in input_data:
                heat_loss = input_data["heat_loss_per_meter"]
                safety_factor = 1.25  # IEC 62395 safety factor
                design_power = heat_loss * safety_factor
                
                calculated_params["iec62395_design_power"] = {
                    "value": design_power,
                    "unit": "W/m",
                    "description": f"Design power with IEC 62395 safety factor {safety_factor}",
                }

            return calculated_params

        except Exception as e:
            logger.error(f"IEC 62395 parameter calculation failed: {e}")
            raise CalculationError(f"IEC 62395 parameter calculation failed: {str(e)}")

    def _load_standard_rules(self):
        """Load IEC 62395 specific rules."""
        self.add_rule("IEC62395_VOLTAGE_LIMITS", {
            "description": "Voltage limits per IEC 62395",
            "max_voltage": 1000,
            "standard_voltages": [24, 48, 110, 230, 400, 690],
            "severity": "major",
        })

    def _load_standard_parameters(self):
        """Load IEC 62395 specific parameters."""
        self.add_parameter("power_safety_factor", {
            "value": 1.25,
            "unit": "",
            "description": "Power safety factor per IEC 62395",
        })

    def _apply_validation_rule(self, rule_id: str, rule: Dict[str, Any], design_data: Dict[str, Any], result: ValidationResult):
        """Apply a specific validation rule."""
        try:
            if rule_id == "IEC62395_VOLTAGE_LIMITS":
                voltage = design_data.get("voltage", 0)
                max_voltage = rule.get("max_voltage", 1000)
                
                if voltage > max_voltage:
                    result.add_violation(
                        rule_id,
                        f"Voltage {voltage}V exceeds IEC 62395 limit ({max_voltage}V)",
                        rule.get("severity", "major")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

        except Exception as e:
            logger.error(f"Failed to apply rule {rule_id}: {e}")
            result.add_violation(rule_id, f"Rule application failed: {str(e)}", "critical")
