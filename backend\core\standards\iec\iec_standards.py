# backend/core/standards/iec/iec_standards.py
"""
IEC Standards.

This module provides the main IEC standards interface and common
functionality for IEC electrical and hazardous area standards.
"""

import logging
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class IECStandards:
    """
    Main interface for IEC standards implementations.
    
    This class provides access to various IEC standards and common
    functionality for IEC electrical and hazardous area standards.
    """

    def __init__(self):
        """Initialize IEC standards interface."""
        self.available_standards = {}
        self.common_parameters = self._load_common_parameters()
        logger.debug("IECStandards initialized")

    def get_available_standards(self) -> List[Dict[str, Any]]:
        """
        Get list of available IEC standards.

        Returns:
            List of available IEC standards with metadata
        """
        return [
            {
                "standard_id": "IEC-60079-30-1",
                "title": "Explosive atmospheres - Part 30-1: Electrical resistance trace heating - General and testing requirements",
                "version": "2015",
                "description": "Requirements for electrical resistance trace heating in explosive atmospheres",
                "application_areas": ["hazardous_areas", "explosive_atmospheres", "trace_heating"],
                "status": "active",
            },
            {
                "standard_id": "IEC-62395",
                "title": "Electrical resistance trace heating systems for industrial and commercial applications",
                "version": "2013",
                "description": "General requirements for electrical resistance trace heating systems",
                "application_areas": ["industrial", "commercial", "trace_heating", "freeze_protection"],
                "status": "active",
            },
        ]

    def validate_hazardous_area_design(
        self,
        design_data: Dict[str, Any],
        standard_ids: Optional[List[str]] = None,
    ) -> Dict[str, ValidationResult]:
        """
        Validate hazardous area design against IEC standards.

        Args:
            design_data: Hazardous area design data
            standard_ids: Specific IEC standards to validate against

        Returns:
            Dict mapping standard IDs to validation results
        """
        logger.info("Validating hazardous area design against IEC standards")

        try:
            # Use all available standards if none specified
            if standard_ids is None:
                standard_ids = [std["standard_id"] for std in self.get_available_standards()]

            results = {}

            for standard_id in standard_ids:
                try:
                    if standard_id == "IEC-60079-30-1":
                        from .iec_60079 import IEC60079
                        standard = IEC60079()
                        result = standard.validate_design(design_data)
                        results[standard_id] = result
                    
                    elif standard_id == "IEC-62395":
                        from .iec_62395 import IEC62395
                        standard = IEC62395()
                        result = standard.validate_design(design_data)
                        results[standard_id] = result
                    
                    else:
                        logger.warning(f"Unknown IEC standard: {standard_id}")

                except Exception as e:
                    logger.error(f"Validation failed for {standard_id}: {e}")
                    # Create error result
                    error_result = ValidationResult()
                    error_result.add_violation(
                        "VALIDATION_ERROR",
                        f"Validation failed: {str(e)}",
                        "critical"
                    )
                    results[standard_id] = error_result

            return results

        except Exception as e:
            logger.error(f"IEC standards validation failed: {e}")
            raise CalculationError(f"IEC standards validation failed: {str(e)}")

    def get_hazardous_area_classifications(self) -> Dict[str, Dict[str, Any]]:
        """
        Get IEC hazardous area classifications.

        Returns:
            Dict with hazardous area classification data
        """
        return {
            "zones": {
                "Zone 0": {
                    "description": "Area where explosive atmosphere is present continuously or for long periods",
                    "probability": "continuous",
                    "equipment_category": "1G",
                    "protection_level": "very_high",
                },
                "Zone 1": {
                    "description": "Area where explosive atmosphere is likely to occur in normal operation",
                    "probability": "likely",
                    "equipment_category": "2G",
                    "protection_level": "high",
                },
                "Zone 2": {
                    "description": "Area where explosive atmosphere is not likely to occur in normal operation",
                    "probability": "unlikely",
                    "equipment_category": "3G",
                    "protection_level": "enhanced",
                },
            },
            "temperature_classes": {
                "T1": {"max_surface_temp": 450, "unit": "°C"},
                "T2": {"max_surface_temp": 300, "unit": "°C"},
                "T3": {"max_surface_temp": 200, "unit": "°C"},
                "T4": {"max_surface_temp": 135, "unit": "°C"},
                "T5": {"max_surface_temp": 100, "unit": "°C"},
                "T6": {"max_surface_temp": 85, "unit": "°C"},
            },
            "protection_methods": {
                "Ex d": "Flameproof enclosure",
                "Ex e": "Increased safety",
                "Ex i": "Intrinsic safety",
                "Ex m": "Encapsulation",
                "Ex n": "Non-sparking",
                "Ex o": "Oil immersion",
                "Ex p": "Pressurization",
                "Ex q": "Powder filling",
            },
        }

    def calculate_surface_temperature_limits(
        self,
        hazardous_classification: Dict[str, Any],
        ambient_conditions: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Calculate surface temperature limits for hazardous areas.

        Args:
            hazardous_classification: Hazardous area classification data
            ambient_conditions: Ambient condition data

        Returns:
            Dict with calculated temperature limits
        """
        logger.info("Calculating surface temperature limits for hazardous areas")

        try:
            results = {}

            # Get temperature class
            temp_class = hazardous_classification.get("temperature_class", "T3")
            classifications = self.get_hazardous_area_classifications()
            
            if temp_class in classifications["temperature_classes"]:
                max_surface_temp = classifications["temperature_classes"][temp_class]["max_surface_temp"]
                
                # Apply safety margin
                safety_margin = 20  # °C
                design_max_temp = max_surface_temp - safety_margin
                
                results = {
                    "temperature_class": temp_class,
                    "max_surface_temperature": max_surface_temp,
                    "design_max_temperature": design_max_temp,
                    "safety_margin": safety_margin,
                    "ambient_temperature": ambient_conditions.get("ambient_temperature", 20),
                    "temperature_rise_limit": design_max_temp - ambient_conditions.get("ambient_temperature", 20),
                }

            return results

        except Exception as e:
            logger.error(f"Surface temperature calculation failed: {e}")
            raise CalculationError(f"Surface temperature calculation failed: {str(e)}")

    def get_protection_requirements(
        self,
        zone_classification: str,
        equipment_category: str,
    ) -> Dict[str, Any]:
        """
        Get protection requirements for specific zone and equipment category.

        Args:
            zone_classification: Zone classification (Zone 0, 1, 2)
            equipment_category: Equipment category (1G, 2G, 3G)

        Returns:
            Dict with protection requirements
        """
        logger.debug(f"Getting protection requirements for {zone_classification}, {equipment_category}")

        try:
            requirements = {
                "zone": zone_classification,
                "equipment_category": equipment_category,
                "protection_methods": [],
                "certification_required": True,
                "testing_requirements": [],
                "installation_requirements": [],
            }

            # Zone-specific requirements
            if zone_classification == "Zone 0":
                requirements["protection_methods"] = ["Ex ia", "Ex ma"]
                requirements["testing_requirements"] = [
                    "Type testing", "Routine testing", "Special conditions testing"
                ]
                requirements["installation_requirements"] = [
                    "Intrinsically safe circuits only",
                    "Certified barriers required",
                    "Cable segregation mandatory"
                ]

            elif zone_classification == "Zone 1":
                requirements["protection_methods"] = ["Ex d", "Ex e", "Ex i", "Ex m", "Ex p"]
                requirements["testing_requirements"] = [
                    "Type testing", "Routine testing"
                ]
                requirements["installation_requirements"] = [
                    "Certified equipment required",
                    "Proper cable glands",
                    "Earthing requirements"
                ]

            elif zone_classification == "Zone 2":
                requirements["protection_methods"] = ["Ex n", "Ex e", "Ex i"]
                requirements["testing_requirements"] = [
                    "Type testing"
                ]
                requirements["installation_requirements"] = [
                    "Non-sparking equipment",
                    "Temperature monitoring",
                    "Regular inspection"
                ]

            return requirements

        except Exception as e:
            logger.error(f"Failed to get protection requirements: {e}")
            return {}

    def validate_equipment_certification(
        self,
        equipment_data: Dict[str, Any],
        zone_requirements: Dict[str, Any],
    ) -> ValidationResult:
        """
        Validate equipment certification against zone requirements.

        Args:
            equipment_data: Equipment certification data
            zone_requirements: Zone protection requirements

        Returns:
            ValidationResult with certification compliance
        """
        logger.info("Validating equipment certification")

        try:
            result = ValidationResult()

            # Check certification presence
            certification = equipment_data.get("certification", "")
            if not certification:
                result.add_violation(
                    "IEC_CERTIFICATION_MISSING",
                    "Equipment certification is required for hazardous areas",
                    "critical"
                )
                return result

            # Check protection method compatibility
            equipment_protection = equipment_data.get("protection_method", "")
            required_methods = zone_requirements.get("protection_methods", [])
            
            if equipment_protection not in required_methods:
                result.add_violation(
                    "IEC_PROTECTION_METHOD_INCOMPATIBLE",
                    f"Protection method {equipment_protection} not suitable for this zone",
                    "critical"
                )

            # Check temperature class
            equipment_temp_class = equipment_data.get("temperature_class", "")
            required_temp_class = zone_requirements.get("temperature_class", "T3")
            
            temp_classes = ["T1", "T2", "T3", "T4", "T5", "T6"]
            if (equipment_temp_class in temp_classes and 
                required_temp_class in temp_classes and
                temp_classes.index(equipment_temp_class) < temp_classes.index(required_temp_class)):
                result.add_violation(
                    "IEC_TEMPERATURE_CLASS_INSUFFICIENT",
                    f"Equipment temperature class {equipment_temp_class} insufficient for {required_temp_class}",
                    "major"
                )

            if result.is_compliant:
                result.add_applied_rule(
                    "IEC_EQUIPMENT_CERTIFICATION",
                    "Equipment certification validated",
                    "passed"
                )

            return result

        except Exception as e:
            logger.error(f"Equipment certification validation failed: {e}")
            raise CalculationError(f"Equipment certification validation failed: {str(e)}")

    def _load_common_parameters(self) -> Dict[str, Any]:
        """Load common IEC parameters."""
        return {
            "standard_voltages": [24, 48, 110, 230, 400, 690],
            "standard_frequencies": [50, 60],
            "protection_levels": ["ia", "ib", "ic", "ma", "mb", "mc"],
            "ingress_protection": ["IP54", "IP65", "IP66", "IP67", "IP68"],
            "safety_margins": {
                "temperature": 20,  # °C
                "current": 0.8,     # 80% derating
                "voltage": 0.9,     # 90% derating
            },
        }
