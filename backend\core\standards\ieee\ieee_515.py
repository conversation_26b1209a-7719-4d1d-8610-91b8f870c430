# backend/core/standards/ieee/ieee_515.py
"""
IEEE 515-2017 Standard.

IEEE Standard for the Testing, Design, Installation, and Maintenance 
of Electrical Resistance Trace Heating for Industrial Applications.
"""

import logging
import math
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class IEEE515(BaseStandard):
    """
    IEEE 515-2017 Standard implementation.
    
    This standard covers the testing, design, installation, and maintenance
    of electrical resistance trace heating for industrial applications.
    """

    def __init__(self):
        """Initialize IEEE 515 standard."""
        super().__init__(
            standard_id="IEEE-515-2017",
            title="IEEE Standard for the Testing, Design, Installation, and Maintenance of Electrical Resistance Trace Heating for Industrial Applications",
            version="2017",
            standard_type=StandardType.IEEE
        )
        
        self._load_standard_rules()
        self._load_standard_parameters()
        logger.debug("IEEE 515-2017 standard initialized")

    def validate_design(self, design_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate design against IEEE 515 requirements.

        Args:
            design_data: Design data to validate

        Returns:
            ValidationResult with compliance information
        """
        logger.info("Validating design against IEEE 515-2017")

        try:
            result = ValidationResult()
            
            # Required fields for IEEE 515 validation
            required_fields = [
                "voltage", "power_density", "maintain_temperature", 
                "ambient_temperature", "pipe_diameter", "insulation_type"
            ]
            
            self.validate_input_data(design_data, required_fields)

            # Apply validation rules
            applicable_rules = self.get_applicable_rules(design_data)
            
            for rule_id in applicable_rules:
                rule = self.get_rule(rule_id)
                if rule:
                    self._apply_validation_rule(rule_id, rule, design_data, result)

            # Additional IEEE 515 specific validations
            self._validate_voltage_requirements(design_data, result)
            self._validate_power_density_limits(design_data, result)
            self._validate_temperature_limits(design_data, result)
            self._validate_installation_requirements(design_data, result)
            self._validate_safety_requirements(design_data, result)

            logger.info(f"IEEE 515 validation completed: {result.compliance_level.value}")
            return result

        except Exception as e:
            logger.error(f"IEEE 515 validation failed: {e}")
            raise CalculationError(f"IEEE 515 validation failed: {str(e)}")

    def get_applicable_rules(self, design_data: Dict[str, Any]) -> List[str]:
        """
        Get applicable IEEE 515 rules for the design.

        Args:
            design_data: Design data to analyze

        Returns:
            List of applicable rule IDs
        """
        applicable_rules = []

        try:
            # Always applicable rules
            applicable_rules.extend([
                "IEEE515_VOLTAGE_LIMITS",
                "IEEE515_POWER_DENSITY",
                "IEEE515_TEMPERATURE_LIMITS",
                "IEEE515_SAFETY_FACTORS",
            ])

            # Conditional rules based on application
            application_type = design_data.get("application_type", "standard")
            
            if application_type == "hazardous_area":
                applicable_rules.extend([
                    "IEEE515_HAZARDOUS_AREA",
                    "IEEE515_EXPLOSION_PROOF",
                ])

            if design_data.get("outdoor_installation", False):
                applicable_rules.append("IEEE515_OUTDOOR_INSTALLATION")

            if design_data.get("corrosive_environment", False):
                applicable_rules.append("IEEE515_CORROSIVE_ENVIRONMENT")

            # Rules based on voltage level
            voltage = design_data.get("voltage", 0)
            if voltage > 300:
                applicable_rules.append("IEEE515_HIGH_VOLTAGE")

            return applicable_rules

        except Exception as e:
            logger.error(f"Failed to get applicable IEEE 515 rules: {e}")
            return []

    def calculate_parameters(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate IEEE 515 specific parameters.

        Args:
            input_data: Input data for calculations

        Returns:
            Dict with calculated parameters
        """
        logger.info("Calculating IEEE 515 parameters")

        try:
            calculated_params = {}

            # Calculate power requirements with IEEE 515 safety factors
            if "heat_loss_per_meter" in input_data:
                heat_loss = input_data["heat_loss_per_meter"]
                application_type = input_data.get("application_type", "standard")
                
                # IEEE 515 safety factors
                safety_factors = {
                    "freeze_protection": 1.4,
                    "process_temperature": 1.3,
                    "critical_service": 1.5,
                    "standard": 1.3,
                }
                
                safety_factor = safety_factors.get(application_type, 1.3)
                design_power_density = heat_loss * safety_factor
                
                calculated_params["ieee515_power_density"] = {
                    "value": design_power_density,
                    "unit": "W/m",
                    "description": f"IEEE 515 design power density with safety factor {safety_factor}",
                }

            # Calculate circuit parameters
            if all(key in input_data for key in ["voltage", "power_per_meter", "circuit_length"]):
                voltage = input_data["voltage"]
                power_per_meter = input_data["power_per_meter"]
                circuit_length = input_data["circuit_length"]
                
                total_power = power_per_meter * circuit_length
                operating_current = total_power / voltage
                
                # IEEE 515 current derating
                derated_current = operating_current / 0.8  # 80% derating factor
                
                calculated_params.update({
                    "ieee515_operating_current": {
                        "value": operating_current,
                        "unit": "A",
                        "description": "Operating current per IEEE 515",
                    },
                    "ieee515_derated_current": {
                        "value": derated_current,
                        "unit": "A",
                        "description": "Derated current per IEEE 515 (80% factor)",
                    },
                })

            # Calculate temperature parameters
            if all(key in input_data for key in ["maintain_temperature", "ambient_temperature"]):
                maintain_temp = input_data["maintain_temperature"]
                ambient_temp = input_data["ambient_temperature"]
                
                temp_differential = maintain_temp - ambient_temp
                
                # IEEE 515 temperature safety margin
                safety_margin = 10  # °C
                design_temp_differential = temp_differential + safety_margin
                
                calculated_params["ieee515_temperature_differential"] = {
                    "value": design_temp_differential,
                    "unit": "°C",
                    "description": f"Design temperature differential with {safety_margin}°C safety margin",
                }

            # Calculate cable selection parameters
            if "power_density" in input_data:
                power_density = input_data["power_density"]
                
                # IEEE 515 cable selection guidelines
                if power_density <= 15:
                    cable_type = "Self-regulating"
                elif power_density <= 30:
                    cable_type = "Constant wattage parallel"
                else:
                    cable_type = "Constant wattage series"
                
                calculated_params["ieee515_recommended_cable_type"] = {
                    "value": cable_type,
                    "unit": "",
                    "description": "Recommended cable type per IEEE 515 guidelines",
                }

            return calculated_params

        except Exception as e:
            logger.error(f"IEEE 515 parameter calculation failed: {e}")
            raise CalculationError(f"IEEE 515 parameter calculation failed: {str(e)}")

    def _load_standard_rules(self):
        """Load IEEE 515 specific rules."""
        # Voltage limits rule
        self.add_rule("IEEE515_VOLTAGE_LIMITS", {
            "description": "Voltage shall be within IEEE 515 specified limits",
            "min_voltage": 120,
            "max_voltage": 600,
            "standard_voltages": [120, 208, 240, 277, 480, 600],
            "severity": "major",
        })

        # Power density rule
        self.add_rule("IEEE515_POWER_DENSITY", {
            "description": "Power density shall be appropriate for application",
            "min_power_density": 5,
            "max_power_density": 100,
            "typical_range": [10, 50],
            "severity": "major",
        })

        # Temperature limits rule
        self.add_rule("IEEE515_TEMPERATURE_LIMITS", {
            "description": "Temperature limits per IEEE 515",
            "min_ambient": -40,
            "max_ambient": 60,
            "max_maintain": 250,
            "max_exposure": 300,
            "severity": "critical",
        })

        # Safety factors rule
        self.add_rule("IEEE515_SAFETY_FACTORS", {
            "description": "Safety factors per IEEE 515",
            "power_safety_factor": 1.3,
            "current_safety_factor": 1.25,
            "temperature_safety_margin": 10,
            "severity": "major",
        })

        # Hazardous area rule
        self.add_rule("IEEE515_HAZARDOUS_AREA", {
            "description": "Hazardous area requirements per IEEE 515",
            "requires_intrinsic_safety": True,
            "max_surface_temperature": 200,
            "certification_required": True,
            "severity": "critical",
        })

    def _load_standard_parameters(self):
        """Load IEEE 515 specific parameters."""
        self.add_parameter("power_safety_factor", {
            "value": 1.3,
            "unit": "",
            "description": "Standard power safety factor",
            "application": "general",
        })

        self.add_parameter("current_derating_factor", {
            "value": 0.8,
            "unit": "",
            "description": "Current derating factor for continuous operation",
            "application": "electrical",
        })

        self.add_parameter("temperature_safety_margin", {
            "value": 10,
            "unit": "°C",
            "description": "Temperature safety margin",
            "application": "thermal",
        })

    def _apply_validation_rule(
        self,
        rule_id: str,
        rule: Dict[str, Any],
        design_data: Dict[str, Any],
        result: ValidationResult,
    ):
        """Apply a specific validation rule."""
        try:
            if rule_id == "IEEE515_VOLTAGE_LIMITS":
                voltage = design_data.get("voltage", 0)
                min_voltage = rule.get("min_voltage", 120)
                max_voltage = rule.get("max_voltage", 600)
                
                if voltage < min_voltage or voltage > max_voltage:
                    result.add_violation(
                        rule_id,
                        f"Voltage {voltage}V is outside IEEE 515 limits ({min_voltage}-{max_voltage}V)",
                        rule.get("severity", "major")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

            elif rule_id == "IEEE515_POWER_DENSITY":
                power_density = design_data.get("power_density", 0)
                min_power = rule.get("min_power_density", 5)
                max_power = rule.get("max_power_density", 100)
                
                if power_density < min_power or power_density > max_power:
                    result.add_violation(
                        rule_id,
                        f"Power density {power_density}W/m is outside IEEE 515 limits ({min_power}-{max_power}W/m)",
                        rule.get("severity", "major")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

            elif rule_id == "IEEE515_TEMPERATURE_LIMITS":
                maintain_temp = design_data.get("maintain_temperature", 0)
                max_maintain = rule.get("max_maintain", 250)
                
                if maintain_temp > max_maintain:
                    result.add_violation(
                        rule_id,
                        f"Maintain temperature {maintain_temp}°C exceeds IEEE 515 limit ({max_maintain}°C)",
                        rule.get("severity", "critical")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

        except Exception as e:
            logger.error(f"Failed to apply rule {rule_id}: {e}")
            result.add_violation(
                rule_id,
                f"Rule application failed: {str(e)}",
                "critical"
            )

    def _validate_voltage_requirements(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate voltage requirements."""
        voltage = design_data.get("voltage", 0)
        
        # Check for standard voltages
        standard_voltages = [120, 208, 240, 277, 480, 600]
        if voltage not in standard_voltages:
            result.add_warning(
                "IEEE515_NON_STANDARD_VOLTAGE",
                f"Voltage {voltage}V is not a standard IEEE voltage. Consider using: {standard_voltages}"
            )

    def _validate_power_density_limits(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate power density limits."""
        power_density = design_data.get("power_density", 0)
        
        # Recommendations based on power density
        if power_density > 50:
            result.add_recommendation(
                "High power density detected. Consider using constant wattage cables for better control.",
                "medium"
            )
        elif power_density < 10:
            result.add_recommendation(
                "Low power density detected. Self-regulating cables may be suitable.",
                "low"
            )

    def _validate_temperature_limits(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate temperature limits."""
        maintain_temp = design_data.get("maintain_temperature", 0)
        ambient_temp = design_data.get("ambient_temperature", 0)
        
        temp_diff = maintain_temp - ambient_temp
        
        if temp_diff > 200:
            result.add_warning(
                "IEEE515_HIGH_TEMP_DIFF",
                f"High temperature differential ({temp_diff}°C) may require special considerations"
            )

    def _validate_installation_requirements(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate installation requirements."""
        installation_type = design_data.get("installation_type", "standard")
        
        if installation_type == "buried":
            result.add_recommendation(
                "Buried installation requires special cable protection and moisture sealing.",
                "high"
            )

    def _validate_safety_requirements(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate safety requirements."""
        application_type = design_data.get("application_type", "standard")
        
        if application_type == "hazardous_area":
            hazardous_class = design_data.get("hazardous_class")
            if not hazardous_class:
                result.add_violation(
                    "IEEE515_HAZARDOUS_AREA_CLASS",
                    "Hazardous area classification must be specified for hazardous area applications",
                    "critical"
                )
