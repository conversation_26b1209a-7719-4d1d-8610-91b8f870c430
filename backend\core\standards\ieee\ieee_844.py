# backend/core/standards/ieee/ieee_844.py
"""
IEEE 844-2000 Standard.

IEEE Recommended Practice for Electrical Impedance, Induction, 
and Skin Effect Heating of Pipelines and Vessels.
"""

import logging
import math
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class IEEE844(BaseStandard):
    """
    IEEE 844-2000 Standard implementation.
    
    This standard covers electrical impedance, induction, and skin effect
    heating of pipelines and vessels.
    """

    def __init__(self):
        """Initialize IEEE 844 standard."""
        super().__init__(
            standard_id="IEEE-844-2000",
            title="IEEE Recommended Practice for Electrical Impedance, Induction, and Skin Effect Heating of Pipelines and Vessels",
            version="2000",
            standard_type=StandardType.IEEE
        )
        
        self._load_standard_rules()
        self._load_standard_parameters()
        logger.debug("IEEE 844-2000 standard initialized")

    def validate_design(self, design_data: Dict[str, Any]) -> ValidationResult:
        """
        Validate design against IEEE 844 requirements.

        Args:
            design_data: Design data to validate

        Returns:
            ValidationResult with compliance information
        """
        logger.info("Validating design against IEEE 844-2000")

        try:
            result = ValidationResult()
            
            # Required fields for IEEE 844 validation
            required_fields = [
                "heating_method", "frequency", "pipe_diameter", 
                "pipe_material", "current_density"
            ]
            
            self.validate_input_data(design_data, required_fields)

            # Apply validation rules
            applicable_rules = self.get_applicable_rules(design_data)
            
            for rule_id in applicable_rules:
                rule = self.get_rule(rule_id)
                if rule:
                    self._apply_validation_rule(rule_id, rule, design_data, result)

            # Additional IEEE 844 specific validations
            self._validate_heating_method(design_data, result)
            self._validate_frequency_requirements(design_data, result)
            self._validate_current_density_limits(design_data, result)
            self._validate_skin_effect_parameters(design_data, result)

            logger.info(f"IEEE 844 validation completed: {result.compliance_level.value}")
            return result

        except Exception as e:
            logger.error(f"IEEE 844 validation failed: {e}")
            raise CalculationError(f"IEEE 844 validation failed: {str(e)}")

    def get_applicable_rules(self, design_data: Dict[str, Any]) -> List[str]:
        """
        Get applicable IEEE 844 rules for the design.

        Args:
            design_data: Design data to analyze

        Returns:
            List of applicable rule IDs
        """
        applicable_rules = []

        try:
            heating_method = design_data.get("heating_method", "")

            # Always applicable rules
            applicable_rules.extend([
                "IEEE844_FREQUENCY_LIMITS",
                "IEEE844_CURRENT_DENSITY",
                "IEEE844_MATERIAL_COMPATIBILITY",
            ])

            # Method-specific rules
            if heating_method == "impedance":
                applicable_rules.extend([
                    "IEEE844_IMPEDANCE_HEATING",
                    "IEEE844_SKIN_EFFECT",
                ])
            elif heating_method == "induction":
                applicable_rules.extend([
                    "IEEE844_INDUCTION_HEATING",
                    "IEEE844_MAGNETIC_COUPLING",
                ])

            # Material-specific rules
            pipe_material = design_data.get("pipe_material", "")
            if "steel" in pipe_material.lower():
                applicable_rules.append("IEEE844_FERROMAGNETIC_MATERIAL")
            elif "aluminum" in pipe_material.lower():
                applicable_rules.append("IEEE844_NON_FERROMAGNETIC_MATERIAL")

            return applicable_rules

        except Exception as e:
            logger.error(f"Failed to get applicable IEEE 844 rules: {e}")
            return []

    def calculate_parameters(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate IEEE 844 specific parameters.

        Args:
            input_data: Input data for calculations

        Returns:
            Dict with calculated parameters
        """
        logger.info("Calculating IEEE 844 parameters")

        try:
            calculated_params = {}

            # Calculate skin depth for impedance heating
            if all(key in input_data for key in ["frequency", "resistivity", "permeability"]):
                frequency = input_data["frequency"]  # Hz
                resistivity = input_data["resistivity"]  # Ω·m
                permeability = input_data.get("permeability", 4 * math.pi * 1e-7)  # H/m
                
                skin_depth = math.sqrt(2 * resistivity / (2 * math.pi * frequency * permeability))
                
                calculated_params["ieee844_skin_depth"] = {
                    "value": skin_depth * 1000,  # Convert to mm
                    "unit": "mm",
                    "description": "Skin depth for impedance heating per IEEE 844",
                }

            # Calculate impedance for pipeline heating
            if all(key in input_data for key in ["pipe_diameter", "wall_thickness", "frequency"]):
                diameter = input_data["pipe_diameter"]  # m
                wall_thickness = input_data["wall_thickness"]  # m
                frequency = input_data["frequency"]  # Hz
                
                # Simplified impedance calculation
                resistance_per_length = self._calculate_ac_resistance(diameter, wall_thickness, frequency)
                reactance_per_length = self._calculate_reactance(diameter, frequency)
                
                impedance_per_length = math.sqrt(resistance_per_length**2 + reactance_per_length**2)
                
                calculated_params.update({
                    "ieee844_resistance_per_length": {
                        "value": resistance_per_length,
                        "unit": "Ω/m",
                        "description": "AC resistance per unit length",
                    },
                    "ieee844_reactance_per_length": {
                        "value": reactance_per_length,
                        "unit": "Ω/m",
                        "description": "Reactance per unit length",
                    },
                    "ieee844_impedance_per_length": {
                        "value": impedance_per_length,
                        "unit": "Ω/m",
                        "description": "Total impedance per unit length",
                    },
                })

            # Calculate power requirements for impedance heating
            if all(key in input_data for key in ["current", "impedance_per_length", "length"]):
                current = input_data["current"]  # A
                impedance = input_data["impedance_per_length"]  # Ω/m
                length = input_data["length"]  # m
                
                power_per_length = current**2 * impedance
                total_power = power_per_length * length
                
                calculated_params.update({
                    "ieee844_power_per_length": {
                        "value": power_per_length,
                        "unit": "W/m",
                        "description": "Power dissipation per unit length",
                    },
                    "ieee844_total_power": {
                        "value": total_power,
                        "unit": "W",
                        "description": "Total power requirement",
                    },
                })

            # Calculate efficiency factors
            heating_method = input_data.get("heating_method", "")
            if heating_method == "impedance":
                # Impedance heating efficiency
                efficiency = 0.85  # Typical efficiency
                calculated_params["ieee844_heating_efficiency"] = {
                    "value": efficiency,
                    "unit": "",
                    "description": "Typical impedance heating efficiency",
                }
            elif heating_method == "induction":
                # Induction heating efficiency
                efficiency = 0.75  # Typical efficiency
                calculated_params["ieee844_heating_efficiency"] = {
                    "value": efficiency,
                    "unit": "",
                    "description": "Typical induction heating efficiency",
                }

            return calculated_params

        except Exception as e:
            logger.error(f"IEEE 844 parameter calculation failed: {e}")
            raise CalculationError(f"IEEE 844 parameter calculation failed: {str(e)}")

    def _load_standard_rules(self):
        """Load IEEE 844 specific rules."""
        # Frequency limits rule
        self.add_rule("IEEE844_FREQUENCY_LIMITS", {
            "description": "Frequency shall be within IEEE 844 specified limits",
            "min_frequency": 50,
            "max_frequency": 400,
            "standard_frequencies": [50, 60],
            "severity": "major",
        })

        # Current density rule
        self.add_rule("IEEE844_CURRENT_DENSITY", {
            "description": "Current density limits per IEEE 844",
            "max_current_density": 5.0,  # A/mm²
            "recommended_max": 3.0,  # A/mm²
            "severity": "major",
        })

        # Impedance heating rule
        self.add_rule("IEEE844_IMPEDANCE_HEATING", {
            "description": "Impedance heating requirements",
            "min_wall_thickness": 3,  # mm
            "max_diameter": 2.0,  # m
            "material_requirements": ["ferromagnetic"],
            "severity": "major",
        })

        # Skin effect rule
        self.add_rule("IEEE844_SKIN_EFFECT", {
            "description": "Skin effect considerations",
            "min_skin_depth_ratio": 0.1,
            "max_frequency_for_uniform": 100,  # Hz
            "severity": "minor",
        })

    def _load_standard_parameters(self):
        """Load IEEE 844 specific parameters."""
        self.add_parameter("impedance_safety_factor", {
            "value": 1.2,
            "unit": "",
            "description": "Safety factor for impedance calculations",
            "application": "impedance_heating",
        })

        self.add_parameter("skin_effect_factor", {
            "value": 1.5,
            "unit": "",
            "description": "Factor for skin effect calculations",
            "application": "ac_heating",
        })

    def _apply_validation_rule(
        self,
        rule_id: str,
        rule: Dict[str, Any],
        design_data: Dict[str, Any],
        result: ValidationResult,
    ):
        """Apply a specific validation rule."""
        try:
            if rule_id == "IEEE844_FREQUENCY_LIMITS":
                frequency = design_data.get("frequency", 0)
                min_freq = rule.get("min_frequency", 50)
                max_freq = rule.get("max_frequency", 400)
                
                if frequency < min_freq or frequency > max_freq:
                    result.add_violation(
                        rule_id,
                        f"Frequency {frequency}Hz is outside IEEE 844 limits ({min_freq}-{max_freq}Hz)",
                        rule.get("severity", "major")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

            elif rule_id == "IEEE844_CURRENT_DENSITY":
                current_density = design_data.get("current_density", 0)
                max_density = rule.get("max_current_density", 5.0)
                
                if current_density > max_density:
                    result.add_violation(
                        rule_id,
                        f"Current density {current_density}A/mm² exceeds IEEE 844 limit ({max_density}A/mm²)",
                        rule.get("severity", "major")
                    )
                else:
                    result.add_applied_rule(rule_id, rule["description"], "passed")

        except Exception as e:
            logger.error(f"Failed to apply rule {rule_id}: {e}")
            result.add_violation(
                rule_id,
                f"Rule application failed: {str(e)}",
                "critical"
            )

    def _validate_heating_method(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate heating method selection."""
        heating_method = design_data.get("heating_method", "")
        pipe_diameter = design_data.get("pipe_diameter", 0)
        
        if heating_method == "impedance" and pipe_diameter > 2.0:
            result.add_warning(
                "IEEE844_LARGE_DIAMETER",
                f"Impedance heating may not be efficient for large diameter pipes ({pipe_diameter}m)"
            )

    def _validate_frequency_requirements(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate frequency requirements."""
        frequency = design_data.get("frequency", 0)
        heating_method = design_data.get("heating_method", "")
        
        if heating_method == "induction" and frequency < 100:
            result.add_recommendation(
                "Consider higher frequency for induction heating to improve efficiency.",
                "medium"
            )

    def _validate_current_density_limits(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate current density limits."""
        current_density = design_data.get("current_density", 0)
        
        if current_density > 3.0:
            result.add_warning(
                "IEEE844_HIGH_CURRENT_DENSITY",
                f"High current density ({current_density}A/mm²) may cause excessive heating"
            )

    def _validate_skin_effect_parameters(self, design_data: Dict[str, Any], result: ValidationResult):
        """Validate skin effect parameters."""
        frequency = design_data.get("frequency", 0)
        wall_thickness = design_data.get("wall_thickness", 0)
        
        if frequency > 100 and wall_thickness < 5:
            result.add_recommendation(
                "Thin wall thickness with high frequency may result in non-uniform heating.",
                "medium"
            )

    def _calculate_ac_resistance(self, diameter: float, wall_thickness: float, frequency: float) -> float:
        """Calculate AC resistance per unit length."""
        # Simplified calculation - real implementation would be more complex
        dc_resistance = 1.0 / (math.pi * diameter * wall_thickness * 1e7)  # Simplified
        skin_effect_factor = 1 + 0.1 * math.sqrt(frequency / 60)  # Simplified
        return dc_resistance * skin_effect_factor

    def _calculate_reactance(self, diameter: float, frequency: float) -> float:
        """Calculate reactance per unit length."""
        # Simplified calculation
        inductance_per_length = 2e-7 * math.log(diameter / 0.001)  # Simplified
        return 2 * math.pi * frequency * inductance_per_length
