# backend/core/standards/ieee/ieee_standards.py
"""
IEEE Standards.

This module provides the main IEEE standards interface and common
functionality for IEEE electrical standards.
"""

import logging
from typing import Dict, List, Any, Optional

from ..general import BaseStandard, StandardType, ValidationResult
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class IEEEStandards:
    """
    Main interface for IEEE standards implementations.
    
    This class provides access to various IEEE standards and common
    functionality for IEEE electrical design standards.
    """

    def __init__(self):
        """Initialize IEEE standards interface."""
        self.available_standards = {}
        self.common_parameters = self._load_common_parameters()
        logger.debug("IEEEStandards initialized")

    def get_available_standards(self) -> List[Dict[str, Any]]:
        """
        Get list of available IEEE standards.

        Returns:
            List of available IEEE standards with metadata
        """
        return [
            {
                "standard_id": "IEEE-515-2017",
                "title": "IEEE Standard for the Testing, Design, Installation, and Maintenance of Electrical Resistance Trace Heating for Industrial Applications",
                "version": "2017",
                "description": "Comprehensive standard for electrical resistance trace heating systems",
                "application_areas": ["industrial", "process", "freeze_protection", "temperature_maintenance"],
                "status": "active",
            },
            {
                "standard_id": "IEEE-844-2000",
                "title": "IEEE Recommended Practice for Electrical Impedance, Induction, and Skin Effect Heating of Pipelines and Vessels",
                "version": "2000",
                "description": "Standard for impedance and induction heating applications",
                "application_areas": ["pipelines", "vessels", "impedance_heating", "induction_heating"],
                "status": "active",
            },
        ]

    def validate_electrical_design(
        self,
        design_data: Dict[str, Any],
        standard_ids: Optional[List[str]] = None,
    ) -> Dict[str, ValidationResult]:
        """
        Validate electrical design against IEEE standards.

        Args:
            design_data: Electrical design data
            standard_ids: Specific IEEE standards to validate against

        Returns:
            Dict mapping standard IDs to validation results
        """
        logger.info("Validating electrical design against IEEE standards")

        try:
            # Use all available standards if none specified
            if standard_ids is None:
                standard_ids = [std["standard_id"] for std in self.get_available_standards()]

            results = {}

            for standard_id in standard_ids:
                try:
                    if standard_id == "IEEE-515-2017":
                        from .ieee_515 import IEEE515
                        standard = IEEE515()
                        result = standard.validate_design(design_data)
                        results[standard_id] = result
                    
                    elif standard_id == "IEEE-844-2000":
                        from .ieee_844 import IEEE844
                        standard = IEEE844()
                        result = standard.validate_design(design_data)
                        results[standard_id] = result
                    
                    else:
                        logger.warning(f"Unknown IEEE standard: {standard_id}")

                except Exception as e:
                    logger.error(f"Validation failed for {standard_id}: {e}")
                    # Create error result
                    error_result = ValidationResult()
                    error_result.add_violation(
                        "VALIDATION_ERROR",
                        f"Validation failed: {str(e)}",
                        "critical"
                    )
                    results[standard_id] = error_result

            return results

        except Exception as e:
            logger.error(f"IEEE standards validation failed: {e}")
            raise CalculationError(f"IEEE standards validation failed: {str(e)}")

    def calculate_electrical_parameters(
        self,
        input_data: Dict[str, Any],
        standard_id: str,
    ) -> Dict[str, Any]:
        """
        Calculate electrical parameters using IEEE standards.

        Args:
            input_data: Input data for calculations
            standard_id: IEEE standard to use for calculations

        Returns:
            Dict with calculated electrical parameters
        """
        logger.info(f"Calculating electrical parameters using {standard_id}")

        try:
            if standard_id == "IEEE-515-2017":
                from .ieee_515 import IEEE515
                standard = IEEE515()
                return standard.calculate_parameters(input_data)
            
            elif standard_id == "IEEE-844-2000":
                from .ieee_844 import IEEE844
                standard = IEEE844()
                return standard.calculate_parameters(input_data)
            
            else:
                raise InvalidInputError(f"Unknown IEEE standard: {standard_id}")

        except Exception as e:
            logger.error(f"IEEE calculation failed: {e}")
            raise CalculationError(f"IEEE calculation failed: {str(e)}")

    def get_safety_factors(self, standard_id: str, application_type: str) -> Dict[str, float]:
        """
        Get safety factors for specific IEEE standard and application.

        Args:
            standard_id: IEEE standard identifier
            application_type: Type of application

        Returns:
            Dict with safety factors
        """
        logger.debug(f"Getting safety factors for {standard_id}, application: {application_type}")

        try:
            # IEEE 515 safety factors
            if standard_id == "IEEE-515-2017":
                safety_factors = {
                    "power_safety_factor": 1.3,
                    "current_safety_factor": 1.25,
                    "voltage_safety_factor": 1.1,
                    "temperature_safety_factor": 1.2,
                }
                
                # Adjust based on application type
                if application_type == "critical":
                    safety_factors["power_safety_factor"] = 1.5
                elif application_type == "freeze_protection":
                    safety_factors["power_safety_factor"] = 1.4

                return safety_factors

            # IEEE 844 safety factors
            elif standard_id == "IEEE-844-2000":
                return {
                    "impedance_safety_factor": 1.2,
                    "current_safety_factor": 1.25,
                    "power_safety_factor": 1.3,
                }

            else:
                logger.warning(f"Unknown IEEE standard for safety factors: {standard_id}")
                return {}

        except Exception as e:
            logger.error(f"Failed to get IEEE safety factors: {e}")
            return {}

    def get_design_limits(self, standard_id: str) -> Dict[str, Dict[str, Any]]:
        """
        Get design limits for specific IEEE standard.

        Args:
            standard_id: IEEE standard identifier

        Returns:
            Dict with design limits
        """
        logger.debug(f"Getting design limits for {standard_id}")

        try:
            if standard_id == "IEEE-515-2017":
                return {
                    "voltage": {
                        "min": 120,  # V
                        "max": 600,  # V
                        "recommended": [120, 208, 240, 277, 480, 600],
                    },
                    "power_density": {
                        "min": 5,    # W/m
                        "max": 100,  # W/m
                        "typical": [10, 15, 20, 25, 30, 40, 50],
                    },
                    "temperature": {
                        "min": -40,  # °C
                        "max": 250,  # °C
                        "maintain_max": 200,  # °C
                    },
                    "current": {
                        "max_per_circuit": 20,  # A
                        "max_per_panel": 100,   # A
                    },
                }

            elif standard_id == "IEEE-844-2000":
                return {
                    "frequency": {
                        "min": 50,   # Hz
                        "max": 400,  # Hz
                        "standard": [50, 60],
                    },
                    "impedance": {
                        "min": 0.1,  # Ω/m
                        "max": 10.0, # Ω/m
                    },
                    "current_density": {
                        "max": 5.0,  # A/mm²
                    },
                }

            else:
                logger.warning(f"Unknown IEEE standard for design limits: {standard_id}")
                return {}

        except Exception as e:
            logger.error(f"Failed to get IEEE design limits: {e}")
            return {}

    def check_code_compliance(
        self,
        design_data: Dict[str, Any],
        local_codes: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """
        Check compliance with IEEE standards and local electrical codes.

        Args:
            design_data: Design data to check
            local_codes: List of local codes to consider

        Returns:
            Dict with compliance check results
        """
        logger.info("Checking IEEE code compliance")

        try:
            compliance_result = {
                "overall_compliant": True,
                "ieee_compliance": {},
                "local_code_compliance": {},
                "recommendations": [],
                "violations": [],
            }

            # Check IEEE 515 compliance
            ieee_515_result = self.validate_electrical_design(design_data, ["IEEE-515-2017"])
            compliance_result["ieee_compliance"]["IEEE-515-2017"] = ieee_515_result.get("IEEE-515-2017", {})

            # Check local codes if specified
            if local_codes:
                for code in local_codes:
                    # This would integrate with local code databases
                    compliance_result["local_code_compliance"][code] = {
                        "status": "not_implemented",
                        "message": f"Local code {code} checking not implemented",
                    }

            # Generate recommendations
            voltage = design_data.get("voltage", 0)
            if voltage > 0:
                if voltage not in [120, 208, 240, 277, 480, 600]:
                    compliance_result["recommendations"].append({
                        "category": "voltage",
                        "message": f"Consider using standard voltage ({voltage}V is non-standard)",
                        "priority": "medium",
                    })

            return compliance_result

        except Exception as e:
            logger.error(f"IEEE code compliance check failed: {e}")
            raise CalculationError(f"IEEE code compliance check failed: {str(e)}")

    def _load_common_parameters(self) -> Dict[str, Any]:
        """Load common IEEE parameters."""
        return {
            "standard_voltages": [120, 208, 240, 277, 480, 600],
            "standard_frequencies": [50, 60],
            "insulation_classes": ["Class I", "Class II", "Class III"],
            "protection_ratings": ["IP65", "IP66", "IP67", "IP68"],
            "temperature_ratings": {
                "T1": 450,  # °C
                "T2": 300,  # °C
                "T3": 200,  # °C
                "T4": 135,  # °C
                "T5": 100,  # °C
                "T6": 85,   # °C
            },
        }
