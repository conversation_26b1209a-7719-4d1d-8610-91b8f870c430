# backend/core/standards/standards_service.py
"""
Standards Service.

This module provides the main service interface for all engineering standards
functionality, orchestrating validation, calculations, and compliance checking.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from .general import StandardsValidator, StandardsCalculator, BaseStandard
from .ieee import IEEEStandards, IEEE515, IEEE844
from .iec import IECStandards, IEC60079, IEC62395
from .api import APIStandards, APIRP14F, APIRP14FZ
from .nfpa import NFPAStandards, NFPA70, NFPA497
from .iso import ISOStandards, ISO13623, ISO14692
from core.errors.exceptions import InvalidInputError, CalculationError

logger = logging.getLogger(__name__)


class StandardsService:
    """
    Main service for orchestrating all engineering standards operations.
    
    This service provides a unified interface for validation, calculations,
    and compliance checking across all supported engineering standards.
    """

    def __init__(self):
        """Initialize the standards service."""
        self.validator = StandardsValidator()
        self.calculator = StandardsCalculator()
        self.standards_registry = {}
        self.standards_interfaces = {}
        
        # Initialize standards interfaces
        self._initialize_standards_interfaces()
        
        # Register all standards
        self._register_all_standards()
        
        logger.debug("StandardsService initialized")

    def get_available_standards(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get all available standards organized by type.

        Returns:
            Dict mapping standard types to lists of available standards
        """
        logger.debug("Getting available standards")

        try:
            available_standards = {}

            # Get standards from each interface
            for std_type, interface in self.standards_interfaces.items():
                if hasattr(interface, 'get_available_standards'):
                    available_standards[std_type] = interface.get_available_standards()
                else:
                    available_standards[std_type] = []

            return available_standards

        except Exception as e:
            logger.error(f"Failed to get available standards: {e}")
            return {}

    def validate_design_against_standards(
        self,
        design_data: Dict[str, Any],
        standard_ids: Optional[List[str]] = None,
        validation_options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Validate design against specified standards.

        Args:
            design_data: Design data to validate
            standard_ids: List of standard IDs to validate against (optional)
            validation_options: Validation configuration options

        Returns:
            Dict with comprehensive validation results

        Raises:
            InvalidInputError: If input data is invalid
            CalculationError: If validation fails
        """
        logger.info(f"Validating design against standards: {standard_ids or 'all'}")

        try:
            # Use all registered standards if none specified
            if standard_ids is None:
                standard_ids = list(self.standards_registry.keys())

            # Validate against multiple standards
            validation_results = self.validator.validate_against_multiple_standards(
                design_data, standard_ids, validation_options
            )

            # Generate compliance report
            compliance_report = self.validator.generate_compliance_report(
                validation_results, design_data, validation_options
            )

            # Check for cross-standard conflicts
            conflicts = self.validator.check_cross_standard_conflicts(validation_results)

            # Compile comprehensive results
            comprehensive_results = {
                "validation_summary": {
                    "total_standards_checked": len(standard_ids),
                    "compliant_standards": sum(
                        1 for result in validation_results.values() if result.is_compliant
                    ),
                    "validation_timestamp": datetime.now().isoformat(),
                },
                "individual_results": {
                    std_id: result.to_dict() for std_id, result in validation_results.items()
                },
                "compliance_report": compliance_report,
                "cross_standard_conflicts": conflicts,
                "recommendations": self._generate_design_recommendations(
                    design_data, validation_results
                ),
            }

            logger.info(f"Design validation completed: {len(validation_results)} standards checked")
            return comprehensive_results

        except Exception as e:
            logger.error(f"Design validation failed: {e}")
            raise CalculationError(f"Design validation failed: {str(e)}")

    def calculate_standards_parameters(
        self,
        input_data: Dict[str, Any],
        standard_ids: Optional[List[str]] = None,
        calculation_options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Calculate parameters according to specified standards.

        Args:
            input_data: Input data for calculations
            standard_ids: List of standard IDs to use for calculations
            calculation_options: Calculation configuration options

        Returns:
            Dict with calculated parameters from all standards

        Raises:
            InvalidInputError: If input data is invalid
            CalculationError: If calculation fails
        """
        logger.info(f"Calculating parameters using standards: {standard_ids or 'all'}")

        try:
            # Use all registered standards if none specified
            if standard_ids is None:
                standard_ids = list(self.standards_registry.keys())

            calculation_results = {}
            
            for standard_id in standard_ids:
                try:
                    result = self.calculator.calculate_with_standard(
                        standard_id, input_data, calculation_options
                    )
                    calculation_results[standard_id] = result.to_dict()
                    
                except Exception as e:
                    logger.error(f"Calculation failed for standard {standard_id}: {e}")
                    calculation_results[standard_id] = {
                        "success": False,
                        "error": str(e),
                        "calculated_values": {},
                    }

            # Compile comprehensive results
            comprehensive_results = {
                "calculation_summary": {
                    "total_standards_used": len(standard_ids),
                    "successful_calculations": sum(
                        1 for result in calculation_results.values() 
                        if result.get("success", False)
                    ),
                    "calculation_timestamp": datetime.now().isoformat(),
                },
                "individual_results": calculation_results,
                "consolidated_parameters": self._consolidate_calculated_parameters(
                    calculation_results
                ),
                "parameter_comparisons": self._compare_calculated_parameters(
                    calculation_results
                ),
            }

            logger.info(f"Parameter calculation completed: {len(calculation_results)} standards used")
            return comprehensive_results

        except Exception as e:
            logger.error(f"Parameter calculation failed: {e}")
            raise CalculationError(f"Parameter calculation failed: {str(e)}")

    def get_standards_recommendations(
        self,
        project_data: Dict[str, Any],
        application_type: str,
    ) -> Dict[str, Any]:
        """
        Get recommendations for applicable standards based on project data.

        Args:
            project_data: Project information
            application_type: Type of application

        Returns:
            Dict with standards recommendations
        """
        logger.info(f"Getting standards recommendations for {application_type}")

        try:
            recommendations = {
                "recommended_standards": [],
                "optional_standards": [],
                "not_applicable": [],
                "reasoning": {},
            }

            # Analyze project requirements
            location = project_data.get("location", "")
            industry = project_data.get("industry", "")
            hazardous_area = project_data.get("hazardous_area", False)
            offshore = project_data.get("offshore", False)

            # IEEE standards recommendations
            if application_type in ["heat_tracing", "electrical_heating"]:
                recommendations["recommended_standards"].append("IEEE-515-2017")
                recommendations["reasoning"]["IEEE-515-2017"] = "Primary standard for electrical resistance trace heating"

            if application_type in ["impedance_heating", "induction_heating"]:
                recommendations["recommended_standards"].append("IEEE-844-2000")
                recommendations["reasoning"]["IEEE-844-2000"] = "Covers impedance and induction heating methods"

            # IEC standards recommendations
            if hazardous_area:
                recommendations["recommended_standards"].append("IEC-60079-30-1")
                recommendations["reasoning"]["IEC-60079-30-1"] = "Required for hazardous area applications"

            if "international" in location.lower() or "europe" in location.lower():
                recommendations["recommended_standards"].append("IEC-62395")
                recommendations["reasoning"]["IEC-62395"] = "International standard for trace heating systems"

            # API standards recommendations
            if offshore or "petroleum" in industry.lower() or "oil" in industry.lower():
                recommendations["recommended_standards"].append("API-RP-14F")
                recommendations["reasoning"]["API-RP-14F"] = "Required for offshore petroleum facilities"

            if offshore and hazardous_area:
                recommendations["recommended_standards"].append("API-RP-14FZ")
                recommendations["reasoning"]["API-RP-14FZ"] = "Required for offshore platforms in explosive atmospheres"

            # NFPA standards recommendations
            if "usa" in location.lower() or "north america" in location.lower():
                recommendations["optional_standards"].append("NFPA-70")
                recommendations["reasoning"]["NFPA-70"] = "National Electrical Code for US installations"

            if hazardous_area and ("usa" in location.lower()):
                recommendations["optional_standards"].append("NFPA-497")
                recommendations["reasoning"]["NFPA-497"] = "US standard for hazardous location classification"

            return recommendations

        except Exception as e:
            logger.error(f"Failed to get standards recommendations: {e}")
            return {"error": str(e)}

    def get_compliance_summary(
        self,
        project_id: Optional[str] = None,
        time_period: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Get compliance summary for projects.

        Args:
            project_id: Specific project ID (optional)
            time_period: Time period for summary (optional)

        Returns:
            Dict with compliance summary
        """
        logger.debug(f"Getting compliance summary for project: {project_id}")

        try:
            # Get validation history
            validation_history = self.validator.get_validation_history()
            calculation_history = self.calculator.get_calculation_history()

            # Filter by project if specified
            if project_id:
                validation_history = [
                    h for h in validation_history 
                    if h.get("project_id") == project_id
                ]

            # Generate summary
            summary = {
                "summary_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "project_id": project_id,
                    "time_period": time_period,
                },
                "validation_statistics": {
                    "total_validations": len(validation_history),
                    "recent_validations": len([
                        h for h in validation_history[-10:]
                    ]),
                },
                "calculation_statistics": {
                    "total_calculations": len(calculation_history),
                    "recent_calculations": len([
                        h for h in calculation_history[-10:]
                    ]),
                },
                "standards_usage": self._analyze_standards_usage(
                    validation_history, calculation_history
                ),
            }

            return summary

        except Exception as e:
            logger.error(f"Failed to get compliance summary: {e}")
            return {"error": str(e)}

    def _initialize_standards_interfaces(self):
        """Initialize standards interfaces."""
        self.standards_interfaces = {
            "ieee": IEEEStandards(),
            "iec": IECStandards(),
            "api": APIStandards(),
            "nfpa": NFPAStandards(),
            "iso": ISOStandards(),
        }

    def _register_all_standards(self):
        """Register all available standards."""
        try:
            # Register IEEE standards
            self.validator.register_standard(IEEE515())
            self.calculator.register_standard(IEEE515())
            self.standards_registry["IEEE-515-2017"] = IEEE515()

            self.validator.register_standard(IEEE844())
            self.calculator.register_standard(IEEE844())
            self.standards_registry["IEEE-844-2000"] = IEEE844()

            # Register IEC standards
            self.validator.register_standard(IEC60079())
            self.calculator.register_standard(IEC60079())
            self.standards_registry["IEC-60079-30-1"] = IEC60079()

            self.validator.register_standard(IEC62395())
            self.calculator.register_standard(IEC62395())
            self.standards_registry["IEC-62395"] = IEC62395()

            # Register API standards
            self.validator.register_standard(APIRP14F())
            self.calculator.register_standard(APIRP14F())
            self.standards_registry["API-RP-14F"] = APIRP14F()

            self.validator.register_standard(APIRP14FZ())
            self.calculator.register_standard(APIRP14FZ())
            self.standards_registry["API-RP-14FZ"] = APIRP14FZ()

            # Register NFPA standards
            self.validator.register_standard(NFPA70())
            self.calculator.register_standard(NFPA70())
            self.standards_registry["NFPA-70"] = NFPA70()

            self.validator.register_standard(NFPA497())
            self.calculator.register_standard(NFPA497())
            self.standards_registry["NFPA-497"] = NFPA497()

            # Register ISO standards
            self.validator.register_standard(ISO13623())
            self.calculator.register_standard(ISO13623())
            self.standards_registry["ISO-13623"] = ISO13623()

            self.validator.register_standard(ISO14692())
            self.calculator.register_standard(ISO14692())
            self.standards_registry["ISO-14692"] = ISO14692()

            logger.info(f"Registered {len(self.standards_registry)} standards")

        except Exception as e:
            logger.error(f"Failed to register standards: {e}")

    def _generate_design_recommendations(
        self,
        design_data: Dict[str, Any],
        validation_results: Dict[str, Any],
    ) -> List[Dict[str, Any]]:
        """Generate design recommendations based on validation results."""
        recommendations = []

        try:
            # Analyze validation results for recommendations
            for std_id, result in validation_results.items():
                if hasattr(result, 'recommendations'):
                    for rec in result.recommendations:
                        recommendations.append({
                            "source_standard": std_id,
                            "recommendation": rec.get("description", ""),
                            "priority": rec.get("priority", "medium"),
                            "category": "design_improvement",
                        })

            return recommendations

        except Exception as e:
            logger.error(f"Failed to generate design recommendations: {e}")
            return []

    def _consolidate_calculated_parameters(
        self,
        calculation_results: Dict[str, Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Consolidate calculated parameters from multiple standards."""
        consolidated = {}

        try:
            for std_id, result in calculation_results.items():
                if result.get("success", False):
                    calculated_values = result.get("calculated_values", {})
                    for param_name, param_data in calculated_values.items():
                        if param_name not in consolidated:
                            consolidated[param_name] = []
                        
                        consolidated[param_name].append({
                            "standard": std_id,
                            "value": param_data.get("value"),
                            "unit": param_data.get("unit", ""),
                            "description": param_data.get("description", ""),
                        })

            return consolidated

        except Exception as e:
            logger.error(f"Failed to consolidate parameters: {e}")
            return {}

    def _compare_calculated_parameters(
        self,
        calculation_results: Dict[str, Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Compare calculated parameters across standards."""
        comparisons = {}

        try:
            # This would implement parameter comparison logic
            # For now, return basic structure
            comparisons = {
                "parameter_variations": {},
                "conflicting_values": [],
                "consensus_values": {},
            }

            return comparisons

        except Exception as e:
            logger.error(f"Failed to compare parameters: {e}")
            return {}

    def _analyze_standards_usage(
        self,
        validation_history: List[Dict[str, Any]],
        calculation_history: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """Analyze standards usage patterns."""
        usage_analysis = {}

        try:
            # Count usage by standard
            standard_counts = {}
            
            for record in validation_history:
                std_id = record.get("standard_id", "unknown")
                standard_counts[std_id] = standard_counts.get(std_id, 0) + 1

            usage_analysis = {
                "most_used_standards": sorted(
                    standard_counts.items(), key=lambda x: x[1], reverse=True
                )[:5],
                "total_usage_count": sum(standard_counts.values()),
                "unique_standards_used": len(standard_counts),
            }

            return usage_analysis

        except Exception as e:
            logger.error(f"Failed to analyze standards usage: {e}")
            return {}
