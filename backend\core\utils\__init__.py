# backend/core/utils/__init__.py
"""
Core Utilities Module

This module provides a collection of generic, reusable utility functions
and helpers that encapsulate common logic not specific to any particular
business domain.

The utilities are organized by functional area:
- UUID management and generation
- Date/time operations and formatting
- String manipulation and sanitization
- Database query helpers (pagination, filtering)
- JSON validation for database columns
- File I/O operations
- CRUD endpoint factory for FastAPI
"""

from .uuid_utils import (
    generate_uuid7,
    generate_uuid7_str,
    is_valid_uuid,
    uuid_to_str,
    str_to_uuid,
)

from .datetime_utils import (
    utcnow_aware,
    format_datetime,
    parse_datetime,
    convert_timezone,
    calculate_time_difference,
)

from .string_utils import (
    slugify,
    sanitize_text,
    hash_string,
    truncate_string,
    pad_string,
)

from .pagination_utils import (
    PaginationParams,
    SortParams,
    PaginationResult,
    paginate_query,
    parse_pagination_params,
    parse_sort_params,
    create_pagination_response,
)

from .json_validation import (
    validate_json_data,
    validate_json_string,
    JSONValidationError,
    ValidatedJSON,
    FlexibleJSON,
)

from .query_utils import (
    QueryBuilder,
    build_search_query,
    apply_filters_from_dict,
)

from .file_io_utils import (
    FileIOError,
    validate_file_path,
    safe_read_file,
    safe_write_file,
    read_csv_file,
    write_csv_file,
    read_json_file,
    write_json_file,
    temporary_file,
    temporary_directory,
)

from .crud_endpoint_factory import (
    CRUDEndpointConfig,
    create_crud_router,
    create_simple_crud_router,
    create_project_crud_router,
    create_user_crud_router,
)

from .unit_conversion_utils import (
    UnitConversionError,
    convert_units,
    detect_unit_type,
    validate_conversion,
    get_all_supported_units,
    parse_unit_string,
    convert_unit_string,
    get_unit_info,
    list_all_unit_types,
    list_units_by_type,
)

__all__ = [
    # UUID utilities
    "generate_uuid7",
    "generate_uuid7_str",
    "is_valid_uuid",
    "uuid_to_str",
    "str_to_uuid",
    # DateTime utilities
    "utcnow_aware",
    "format_datetime",
    "parse_datetime",
    "convert_timezone",
    "calculate_time_difference",
    # String utilities
    "slugify",
    "sanitize_text",
    "hash_string",
    "truncate_string",
    "pad_string",
    # Pagination utilities
    "PaginationParams",
    "SortParams",
    "PaginationResult",
    "paginate_query",
    "parse_pagination_params",
    "parse_sort_params",
    "create_pagination_response",
    # JSON validation utilities
    "validate_json_data",
    "validate_json_string",
    "JSONValidationError",
    "ValidatedJSON",
    "FlexibleJSON",
    # Query utilities
    "QueryBuilder",
    "build_search_query",
    "apply_filters_from_dict",
    # File I/O utilities
    "FileIOError",
    "validate_file_path",
    "safe_read_file",
    "safe_write_file",
    "read_csv_file",
    "write_csv_file",
    "read_json_file",
    "write_json_file",
    "temporary_file",
    "temporary_directory",
    # CRUD Factory utilities
    "CRUDEndpointConfig",
    "create_crud_router",
    "create_simple_crud_router",
    "create_project_crud_router",
    "create_user_crud_router",
    # Unit Conversion utilities
    "UnitConversionError",
    "convert_units",
    "detect_unit_type",
    "validate_conversion",
    "get_all_supported_units",
    "parse_unit_string",
    "convert_unit_string",
    "get_unit_info",
    "list_all_unit_types",
    "list_units_by_type",
]
