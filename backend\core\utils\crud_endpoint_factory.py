# backend/core/utils/crud_endpoint_factory.py
"""
CRUD Endpoint Factory

This module provides a factory function for dynamically generating standard CRUD
(Create, Read, Update, Delete) FastAPI endpoints with minimal boilerplate code.

Key Features:
- Dynamic FastAPI router generation
- Standard CRUD endpoint patterns
- Customizable dependencies and middleware
- Automatic OpenAPI documentation
- Error handling integration
- Pagination and filtering support
"""

from typing import Any, Callable, Dict, List, Optional, Type, TypeVar, Union
from fastapi import APIRouter, Depends, HTTPException, Query, Response, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from sqlalchemy.orm import Session

from config.logging_config import get_logger
from core.errors.error_factory import ErrorFactory
from core.schemas.error import ErrorResponseSchema
from core.utils.pagination_utils import (
    PaginationParams,
    SortParams,
    parse_pagination_params,
    parse_sort_params,
    create_pagination_response,
)

logger = get_logger(__name__)

# Type variables
CreateSchemaType = TypeVar('CreateSchemaType', bound=BaseModel)
ReadSchemaType = TypeVar('ReadSchemaType', bound=BaseModel)
UpdateSchemaType = TypeVar('UpdateSchemaType', bound=BaseModel)
ListSchemaType = TypeVar('ListSchemaType', bound=BaseModel)
ServiceType = TypeVar('ServiceType')


class CRUDEndpointConfig:
    """Configuration for CRUD endpoint generation."""
    
    def __init__(
        self,
        entity_name: str,
        entity_name_plural: str,
        create_schema: Type[CreateSchemaType],
        read_schema: Type[ReadSchemaType],
        update_schema: Type[UpdateSchemaType],
        list_response_schema: Type[ListSchemaType],
        service_class: Type[ServiceType],
        service_dependency: Callable,
        id_field: str = "id",
        id_type: Type = str,
        enable_create: bool = True,
        enable_read: bool = True,
        enable_update: bool = True,
        enable_delete: bool = True,
        enable_list: bool = True,
        enable_search: bool = True,
        searchable_fields: Optional[List[str]] = None,
        sortable_fields: Optional[List[str]] = None,
        custom_responses: Optional[Dict[int, Dict]] = None,
    ):
        self.entity_name = entity_name
        self.entity_name_plural = entity_name_plural
        self.create_schema = create_schema
        self.read_schema = read_schema
        self.update_schema = update_schema
        self.list_response_schema = list_response_schema
        self.service_class = service_class
        self.service_dependency = service_dependency
        self.id_field = id_field
        self.id_type = id_type
        self.enable_create = enable_create
        self.enable_read = enable_read
        self.enable_update = enable_update
        self.enable_delete = enable_delete
        self.enable_list = enable_list
        self.enable_search = enable_search
        self.searchable_fields = searchable_fields or []
        self.sortable_fields = sortable_fields or ['id', 'created_at', 'updated_at']
        self.custom_responses = custom_responses or {}


def create_crud_router(config: CRUDEndpointConfig) -> APIRouter:
    """
    Create a FastAPI router with standard CRUD endpoints.
    
    Args:
        config: CRUD endpoint configuration
        
    Returns:
        APIRouter: Router with CRUD endpoints
    """
    router = APIRouter()
    
    # Standard error responses
    standard_responses = {
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": f"{config.entity_name.capitalize()} not found",
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "model": ErrorResponseSchema,
            "description": "Internal server error",
        },
    }
    
    # Merge with custom responses
    standard_responses.update(config.custom_responses)
    
    # CREATE endpoint
    if config.enable_create:
        @router.post(
            "/",
            response_model=config.read_schema,
            status_code=status.HTTP_201_CREATED,
            summary=f"Create a new {config.entity_name}",
            responses={
                **standard_responses,
                status.HTTP_409_CONFLICT: {
                    "model": ErrorResponseSchema,
                    "description": f"{config.entity_name.capitalize()} already exists",
                },
            },
        )
        async def create_entity(
            entity_data: config.create_schema,
            service: config.service_class = Depends(config.service_dependency),
        ):
            """Create a new entity."""
            try:
                method_name = f"create_{config.entity_name.lower()}"
                create_method = getattr(service, method_name)
                new_entity = create_method(entity_data)
                return new_entity
            except Exception as e:
                logger.error(f"Error creating {config.entity_name}: {e}")
                app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
                raise HTTPException(
                    status_code=app_exception.status_code,
                    detail=app_exception.detail
                )
    
    # READ endpoint
    if config.enable_read:
        @router.get(
            f"/{{{config.id_field}}}",
            response_model=config.read_schema,
            summary=f"Retrieve {config.entity_name} details by {config.id_field}",
            responses=standard_responses,
        )
        async def get_entity(
            entity_id: config.id_type,
            service: config.service_class = Depends(config.service_dependency),
        ):
            """Retrieve entity details by ID."""
            try:
                method_name = f"get_{config.entity_name.lower()}_details"
                get_method = getattr(service, method_name)
                entity = get_method(entity_id)
                return entity
            except Exception as e:
                logger.error(f"Error retrieving {config.entity_name} {entity_id}: {e}")
                app_exception = ErrorFactory.create_exception("404_001", reason=str(e))
                raise HTTPException(
                    status_code=app_exception.status_code,
                    detail=app_exception.detail
                )
    
    # UPDATE endpoint
    if config.enable_update:
        @router.put(
            f"/{{{config.id_field}}}",
            response_model=config.read_schema,
            summary=f"Update {config.entity_name} details",
            responses=standard_responses,
        )
        async def update_entity(
            entity_id: config.id_type,
            entity_data: config.update_schema,
            service: config.service_class = Depends(config.service_dependency),
        ):
            """Update entity details."""
            try:
                method_name = f"update_{config.entity_name.lower()}"
                update_method = getattr(service, method_name)
                updated_entity = update_method(entity_id, entity_data)
                return updated_entity
            except Exception as e:
                logger.error(f"Error updating {config.entity_name} {entity_id}: {e}")
                app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
                raise HTTPException(
                    status_code=app_exception.status_code,
                    detail=app_exception.detail
                )
    
    # DELETE endpoint
    if config.enable_delete:
        @router.delete(
            f"/{{{config.id_field}}}",
            status_code=status.HTTP_204_NO_CONTENT,
            summary=f"Delete a {config.entity_name}",
            responses=standard_responses,
        )
        async def delete_entity(
            entity_id: config.id_type,
            service: config.service_class = Depends(config.service_dependency),
        ):
            """Delete entity (soft delete)."""
            try:
                method_name = f"delete_{config.entity_name.lower()}"
                delete_method = getattr(service, method_name)
                delete_method(entity_id)
                return Response(status_code=status.HTTP_204_NO_CONTENT)
            except Exception as e:
                logger.error(f"Error deleting {config.entity_name} {entity_id}: {e}")
                app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
                raise HTTPException(
                    status_code=app_exception.status_code,
                    detail=app_exception.detail
                )
    
    # LIST endpoint with pagination and search
    if config.enable_list:
        @router.get(
            "/",
            response_model=config.list_response_schema,
            summary=f"List {config.entity_name_plural} with pagination",
            responses=standard_responses,
        )
        async def list_entities(
            page: int = Query(1, ge=1, description="Page number (1-based)"),
            per_page: int = Query(10, ge=1, le=100, description=f"Number of {config.entity_name_plural} per page"),
            sort_by: Optional[str] = Query(None, description="Sort field"),
            sort_order: Optional[str] = Query("asc", description="Sort order (asc/desc)"),
            search: Optional[str] = Query(None, description="Search term") if config.enable_search else None,
            include_deleted: bool = Query(False, description="Include soft-deleted records"),
            service: config.service_class = Depends(config.service_dependency),
        ):
            """List entities with pagination and optional search."""
            try:
                # Parse parameters
                pagination_params = parse_pagination_params(page, per_page)
                sort_params = parse_sort_params(sort_by, sort_order)
                
                # Validate sort field
                if sort_params.sort_by and sort_params.sort_by not in config.sortable_fields:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Invalid sort field. Allowed fields: {config.sortable_fields}"
                    )
                
                # Build filters
                filters = {"include_deleted": include_deleted}
                if search and config.enable_search:
                    filters["search"] = search
                
                # Call service method
                method_name = f"get_{config.entity_name_plural.lower()}_list"
                list_method = getattr(service, method_name)
                
                # Try enhanced method with pagination/search first
                try:
                    result = list_method(
                        pagination_params=pagination_params,
                        sort_params=sort_params,
                        filters=filters
                    )
                except TypeError:
                    # Fallback to basic method
                    result = list_method(
                        page=page,
                        per_page=per_page,
                        include_deleted=include_deleted
                    )
                
                return result
                
            except Exception as e:
                logger.error(f"Error listing {config.entity_name_plural}: {e}")
                app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
                raise HTTPException(
                    status_code=app_exception.status_code,
                    detail=app_exception.detail
                )
    
    return router


def create_simple_crud_router(
    entity_name: str,
    create_schema: Type[BaseModel],
    read_schema: Type[BaseModel],
    update_schema: Type[BaseModel],
    list_response_schema: Type[BaseModel],
    service_class: Type,
    service_dependency: Callable,
    **kwargs
) -> APIRouter:
    """
    Simplified function to create CRUD router with minimal configuration.
    
    Args:
        entity_name: Name of the entity (e.g., "project")
        create_schema: Pydantic schema for creation
        read_schema: Pydantic schema for reading
        update_schema: Pydantic schema for updates
        list_response_schema: Pydantic schema for list responses
        service_class: Service class type
        service_dependency: FastAPI dependency for service injection
        **kwargs: Additional configuration options
        
    Returns:
        APIRouter: Configured CRUD router
    """
    entity_name_plural = kwargs.get('entity_name_plural', f"{entity_name}s")
    
    config = CRUDEndpointConfig(
        entity_name=entity_name,
        entity_name_plural=entity_name_plural,
        create_schema=create_schema,
        read_schema=read_schema,
        update_schema=update_schema,
        list_response_schema=list_response_schema,
        service_class=service_class,
        service_dependency=service_dependency,
        **kwargs
    )
    
    return create_crud_router(config)


# Utility functions for common patterns

def create_project_crud_router(service_dependency: Callable) -> APIRouter:
    """Create CRUD router specifically for projects."""
    from core.schemas.project_schemas import (
        ProjectCreateSchema,
        ProjectReadSchema,
        ProjectUpdateSchema,
        ProjectListResponseSchema,
    )
    from core.services.project_service import ProjectService
    
    return create_simple_crud_router(
        entity_name="project",
        entity_name_plural="projects",
        create_schema=ProjectCreateSchema,
        read_schema=ProjectReadSchema,
        update_schema=ProjectUpdateSchema,
        list_response_schema=ProjectListResponseSchema,
        service_class=ProjectService,
        service_dependency=service_dependency,
        searchable_fields=["name", "description", "project_number"],
        sortable_fields=["name", "created_at", "updated_at", "project_number"],
    )


def create_user_crud_router(service_dependency: Callable) -> APIRouter:
    """Create CRUD router specifically for users."""
    from core.schemas.user_schemas import (
        UserCreateSchema,
        UserReadSchema,
        UserUpdateSchema,
        UserListResponseSchema,
    )
    from core.services.user_service import UserService
    
    return create_simple_crud_router(
        entity_name="user",
        entity_name_plural="users",
        create_schema=UserCreateSchema,
        read_schema=UserReadSchema,
        update_schema=UserUpdateSchema,
        list_response_schema=UserListResponseSchema,
        service_class=UserService,
        service_dependency=service_dependency,
        searchable_fields=["username", "email", "first_name", "last_name"],
        sortable_fields=["username", "email", "created_at", "last_login"],
    )
