# backend/core/utils/json_validation.py
"""
JSON Validation Utilities

This module provides utilities for validating structured JSON data stored
in database columns using Pydantic schemas.

Key Features:
- Pydantic-based JSON validation
- Custom SQLAlchemy type decorators
- JSON column validation helpers
- Error handling for malformed JSON
"""

import json
from typing import Any, Dict, Optional, Type, Union

from pydantic import BaseModel, ValidationError
from sqlalchemy import TypeDecorator, Text
from sqlalchemy.types import UserDefinedType

from config.logging_config import get_logger

logger = get_logger(__name__)


class JSONValidationError(Exception):
    """Exception raised when JSON validation fails."""
    
    def __init__(self, message: str, validation_errors: Optional[Dict] = None):
        self.message = message
        self.validation_errors = validation_errors
        super().__init__(message)


def validate_json_data(
    json_data: Union[str, Dict, Any],
    schema_class: Type[BaseModel],
    strict: bool = True
) -> BaseModel:
    """
    Validate JSON data against a Pydantic schema.
    
    Args:
        json_data: JSON data to validate (string, dict, or object)
        schema_class: Pydantic schema class for validation
        strict: Whether to use strict validation
        
    Returns:
        BaseModel: Validated Pydantic model instance
        
    Raises:
        JSONValidationError: If validation fails
    """
    try:
        # Parse JSON string if needed
        if isinstance(json_data, str):
            try:
                parsed_data = json.loads(json_data)
            except json.JSONDecodeError as e:
                raise JSONValidationError(f"Invalid JSON format: {e}")
        else:
            parsed_data = json_data
        
        # Validate with Pydantic schema
        if hasattr(schema_class, 'model_validate'):
            # Pydantic v2
            return schema_class.model_validate(parsed_data, strict=strict)
        else:
            # Pydantic v1
            return schema_class.parse_obj(parsed_data)
            
    except ValidationError as e:
        error_details = {}
        for error in e.errors():
            field_path = '.'.join(str(loc) for loc in error['loc'])
            error_details[field_path] = error['msg']
        
        raise JSONValidationError(
            f"JSON validation failed for schema {schema_class.__name__}",
            validation_errors=error_details
        )
    except Exception as e:
        raise JSONValidationError(f"Unexpected error during JSON validation: {e}")


def validate_json_string(
    json_string: str,
    schema_class: Type[BaseModel],
    strict: bool = True
) -> str:
    """
    Validate JSON string and return normalized JSON string.
    
    Args:
        json_string: JSON string to validate
        schema_class: Pydantic schema class for validation
        strict: Whether to use strict validation
        
    Returns:
        str: Normalized JSON string
        
    Raises:
        JSONValidationError: If validation fails
    """
    validated_model = validate_json_data(json_string, schema_class, strict)
    
    # Convert back to JSON string
    if hasattr(validated_model, 'model_dump_json'):
        # Pydantic v2
        return validated_model.model_dump_json()
    else:
        # Pydantic v1
        return validated_model.json()


def safe_json_loads(
    json_string: str,
    default: Any = None
) -> Any:
    """
    Safely parse JSON string with fallback.
    
    Args:
        json_string: JSON string to parse
        default: Default value if parsing fails
        
    Returns:
        Parsed JSON data or default value
    """
    try:
        return json.loads(json_string)
    except (json.JSONDecodeError, TypeError):
        logger.warning(f"Failed to parse JSON: {json_string}")
        return default


def safe_json_dumps(
    data: Any,
    default: str = "null",
    **kwargs
) -> str:
    """
    Safely serialize data to JSON string.
    
    Args:
        data: Data to serialize
        default: Default string if serialization fails
        **kwargs: Additional arguments for json.dumps
        
    Returns:
        JSON string or default value
    """
    try:
        return json.dumps(data, **kwargs)
    except (TypeError, ValueError):
        logger.warning(f"Failed to serialize to JSON: {data}")
        return default


# SQLAlchemy Custom Types

class ValidatedJSON(TypeDecorator):
    """
    SQLAlchemy type decorator for validated JSON columns.
    
    This type automatically validates JSON data against a Pydantic schema
    when storing and retrieving from the database.
    """
    
    impl = Text
    cache_ok = True
    
    def __init__(self, schema_class: Type[BaseModel], *args, **kwargs):
        self.schema_class = schema_class
        super().__init__(*args, **kwargs)
    
    def process_bind_param(self, value, dialect):
        """Process value when storing to database."""
        if value is None:
            return None
        
        try:
            # Validate the data
            validated_model = validate_json_data(value, self.schema_class)
            
            # Convert to JSON string
            if hasattr(validated_model, 'model_dump_json'):
                # Pydantic v2
                return validated_model.model_dump_json()
            else:
                # Pydantic v1
                return validated_model.json()
                
        except JSONValidationError as e:
            logger.error(f"JSON validation failed for column: {e.message}")
            raise
    
    def process_result_value(self, value, dialect):
        """Process value when retrieving from database."""
        if value is None:
            return None
        
        try:
            # Parse and validate JSON
            return validate_json_data(value, self.schema_class)
        except JSONValidationError as e:
            logger.error(f"Failed to validate JSON from database: {e.message}")
            # Return raw data if validation fails (for backwards compatibility)
            return safe_json_loads(value, {})


class FlexibleJSON(TypeDecorator):
    """
    SQLAlchemy type decorator for flexible JSON columns.
    
    This type stores JSON data without validation but provides
    safe parsing and serialization.
    """
    
    impl = Text
    cache_ok = True
    
    def process_bind_param(self, value, dialect):
        """Process value when storing to database."""
        if value is None:
            return None
        
        return safe_json_dumps(value)
    
    def process_result_value(self, value, dialect):
        """Process value when retrieving from database."""
        if value is None:
            return None
        
        return safe_json_loads(value, {})


# Helper functions for common validation patterns

def validate_settings_json(
    json_data: Union[str, Dict],
    required_keys: Optional[list] = None,
    allowed_keys: Optional[list] = None
) -> Dict:
    """
    Validate settings/configuration JSON data.
    
    Args:
        json_data: JSON data to validate
        required_keys: List of required keys
        allowed_keys: List of allowed keys (None for any)
        
    Returns:
        Dict: Validated settings dictionary
        
    Raises:
        JSONValidationError: If validation fails
    """
    # Parse JSON if string
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise JSONValidationError(f"Invalid JSON format: {e}")
    else:
        data = json_data
    
    if not isinstance(data, dict):
        raise JSONValidationError("Settings JSON must be an object/dictionary")
    
    # Check required keys
    if required_keys:
        missing_keys = set(required_keys) - set(data.keys())
        if missing_keys:
            raise JSONValidationError(f"Missing required keys: {missing_keys}")
    
    # Check allowed keys
    if allowed_keys:
        invalid_keys = set(data.keys()) - set(allowed_keys)
        if invalid_keys:
            raise JSONValidationError(f"Invalid keys: {invalid_keys}")
    
    return data


def validate_metadata_json(
    json_data: Union[str, Dict],
    max_depth: int = 5,
    max_keys: int = 100
) -> Dict:
    """
    Validate metadata JSON with size and depth limits.
    
    Args:
        json_data: JSON data to validate
        max_depth: Maximum nesting depth
        max_keys: Maximum number of keys (total)
        
    Returns:
        Dict: Validated metadata dictionary
        
    Raises:
        JSONValidationError: If validation fails
    """
    # Parse JSON if string
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise JSONValidationError(f"Invalid JSON format: {e}")
    else:
        data = json_data
    
    if not isinstance(data, dict):
        raise JSONValidationError("Metadata JSON must be an object/dictionary")
    
    # Check depth
    def check_depth(obj, current_depth=0):
        if current_depth > max_depth:
            raise JSONValidationError(f"JSON depth exceeds maximum of {max_depth}")
        
        if isinstance(obj, dict):
            for value in obj.values():
                check_depth(value, current_depth + 1)
        elif isinstance(obj, list):
            for item in obj:
                check_depth(item, current_depth + 1)
    
    check_depth(data)
    
    # Check total key count
    def count_keys(obj):
        count = 0
        if isinstance(obj, dict):
            count += len(obj)
            for value in obj.values():
                count += count_keys(value)
        elif isinstance(obj, list):
            for item in obj:
                count += count_keys(item)
        return count
    
    total_keys = count_keys(data)
    if total_keys > max_keys:
        raise JSONValidationError(f"Total key count {total_keys} exceeds maximum of {max_keys}")
    
    return data
