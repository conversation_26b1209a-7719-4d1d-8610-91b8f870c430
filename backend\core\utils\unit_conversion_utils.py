# backend/core/utils/unit_conversion_utils.py
"""
Unit Conversion Utilities

This module provides a unified interface for unit conversions across the application.
It integrates the existing calculation-specific unit conversions and provides
additional general-purpose conversion utilities.

Key Features:
- Temperature, pressure, power, length, area, volume, mass, and energy conversions
- Validation of unit types and values
- Support for engineering-specific units
- Extensible conversion framework
"""

from typing import Dict, List, Optional, Union
import re

from config.logging_config import get_logger

# Import existing conversion functions from calculations
from core.calculations.utils.units_conversion import (
    convert_temperature,
    convert_pressure,
    convert_power,
    convert_length,
    convert_area,
    convert_volume,
    convert_mass,
    convert_energy,
    get_supported_units,
    validate_unit,
)

logger = get_logger(__name__)

# Additional conversion types not in calculations
TIME_CONVERSIONS = {
    "second": 1.0,
    "minute": 60.0,
    "hour": 3600.0,
    "day": 86400.0,
    "week": 604800.0,
    "month": 2629746.0,  # Average month
    "year": 31556952.0,  # Average year
}

FREQUENCY_CONVERSIONS = {
    "hertz": 1.0,
    "kilohertz": 1000.0,
    "megahertz": 1000000.0,
    "gigahertz": 1000000000.0,
    "rpm": 1.0 / 60.0,  # Revolutions per minute to Hz
}

VELOCITY_CONVERSIONS = {
    "meter_per_second": 1.0,
    "kilometer_per_hour": 1.0 / 3.6,
    "mile_per_hour": 0.44704,
    "foot_per_second": 0.3048,
    "knot": 0.514444,
}

# Unit type mappings
UNIT_TYPE_MAPPINGS = {
    "temperature": ["celsius", "fahrenheit", "kelvin", "rankine"],
    "pressure": ["pascal", "kilopascal", "megapascal", "bar", "psi", "atmosphere"],
    "power": ["watt", "kilowatt", "megawatt", "horsepower", "btu_per_hour"],
    "length": ["meter", "millimeter", "centimeter", "inch", "foot", "yard"],
    "area": ["square_meter", "square_inch", "square_foot", "acre", "hectare"],
    "volume": ["cubic_meter", "liter", "cubic_foot", "gallon_us", "gallon_imperial"],
    "mass": ["kilogram", "gram", "pound", "ounce", "ton"],
    "energy": ["joule", "kilojoule", "calorie", "kilocalorie", "btu", "kwh"],
    "time": list(TIME_CONVERSIONS.keys()),
    "frequency": list(FREQUENCY_CONVERSIONS.keys()),
    "velocity": list(VELOCITY_CONVERSIONS.keys()),
}


class UnitConversionError(Exception):
    """Exception raised for unit conversion errors."""
    pass


def convert_units(
    value: Union[float, int],
    from_unit: str,
    to_unit: str,
    unit_type: Optional[str] = None
) -> float:
    """
    Universal unit conversion function.
    
    Args:
        value: Value to convert
        from_unit: Source unit
        to_unit: Target unit
        unit_type: Optional unit type hint for validation
        
    Returns:
        float: Converted value
        
    Raises:
        UnitConversionError: If conversion fails or units are invalid
    """
    try:
        # Auto-detect unit type if not provided
        if unit_type is None:
            unit_type = detect_unit_type(from_unit, to_unit)
        
        # Validate units
        if not validate_conversion(from_unit, to_unit, unit_type):
            raise UnitConversionError(
                f"Invalid conversion: {from_unit} to {to_unit} for type {unit_type}"
            )
        
        # Route to appropriate conversion function
        if unit_type == "temperature":
            return convert_temperature(value, from_unit, to_unit)
        elif unit_type == "pressure":
            return convert_pressure(value, from_unit, to_unit)
        elif unit_type == "power":
            return convert_power(value, from_unit, to_unit)
        elif unit_type == "length":
            return convert_length(value, from_unit, to_unit)
        elif unit_type == "area":
            return convert_area(value, from_unit, to_unit)
        elif unit_type == "volume":
            return convert_volume(value, from_unit, to_unit)
        elif unit_type == "mass":
            return convert_mass(value, from_unit, to_unit)
        elif unit_type == "energy":
            return convert_energy(value, from_unit, to_unit)
        elif unit_type == "time":
            return convert_time(value, from_unit, to_unit)
        elif unit_type == "frequency":
            return convert_frequency(value, from_unit, to_unit)
        elif unit_type == "velocity":
            return convert_velocity(value, from_unit, to_unit)
        else:
            raise UnitConversionError(f"Unsupported unit type: {unit_type}")
            
    except Exception as e:
        logger.error(f"Unit conversion failed: {e}")
        raise UnitConversionError(f"Conversion failed: {str(e)}") from e


def detect_unit_type(from_unit: str, to_unit: str) -> str:
    """
    Auto-detect unit type from unit names.
    
    Args:
        from_unit: Source unit
        to_unit: Target unit
        
    Returns:
        str: Detected unit type
        
    Raises:
        UnitConversionError: If unit type cannot be determined
    """
    from_unit = from_unit.lower()
    to_unit = to_unit.lower()
    
    for unit_type, units in UNIT_TYPE_MAPPINGS.items():
        if from_unit in units and to_unit in units:
            return unit_type
    
    raise UnitConversionError(
        f"Cannot determine unit type for conversion: {from_unit} to {to_unit}"
    )


def validate_conversion(from_unit: str, to_unit: str, unit_type: str) -> bool:
    """
    Validate if a conversion is possible.
    
    Args:
        from_unit: Source unit
        to_unit: Target unit
        unit_type: Unit type
        
    Returns:
        bool: True if conversion is valid
    """
    try:
        supported_units = get_all_supported_units(unit_type)
        return (
            from_unit.lower() in supported_units and 
            to_unit.lower() in supported_units
        )
    except Exception:
        return False


def get_all_supported_units(unit_type: str) -> List[str]:
    """
    Get all supported units for a given type.
    
    Args:
        unit_type: Type of unit
        
    Returns:
        List of supported unit names
    """
    if unit_type in UNIT_TYPE_MAPPINGS:
        return UNIT_TYPE_MAPPINGS[unit_type]
    else:
        # Fallback to calculation utils
        return get_supported_units(unit_type)


def convert_time(value: float, from_unit: str, to_unit: str) -> float:
    """Convert time between different units."""
    from_unit = from_unit.lower()
    to_unit = to_unit.lower()
    
    if from_unit not in TIME_CONVERSIONS:
        raise UnitConversionError(f"Unknown time unit: {from_unit}")
    if to_unit not in TIME_CONVERSIONS:
        raise UnitConversionError(f"Unknown time unit: {to_unit}")
    
    if from_unit == to_unit:
        return value
    
    # Convert to seconds first, then to target unit
    seconds = value * TIME_CONVERSIONS[from_unit]
    result = seconds / TIME_CONVERSIONS[to_unit]
    
    return result


def convert_frequency(value: float, from_unit: str, to_unit: str) -> float:
    """Convert frequency between different units."""
    from_unit = from_unit.lower()
    to_unit = to_unit.lower()
    
    if from_unit not in FREQUENCY_CONVERSIONS:
        raise UnitConversionError(f"Unknown frequency unit: {from_unit}")
    if to_unit not in FREQUENCY_CONVERSIONS:
        raise UnitConversionError(f"Unknown frequency unit: {to_unit}")
    
    if from_unit == to_unit:
        return value
    
    # Convert to Hz first, then to target unit
    hz = value * FREQUENCY_CONVERSIONS[from_unit]
    result = hz / FREQUENCY_CONVERSIONS[to_unit]
    
    return result


def convert_velocity(value: float, from_unit: str, to_unit: str) -> float:
    """Convert velocity between different units."""
    from_unit = from_unit.lower()
    to_unit = to_unit.lower()
    
    if from_unit not in VELOCITY_CONVERSIONS:
        raise UnitConversionError(f"Unknown velocity unit: {from_unit}")
    if to_unit not in VELOCITY_CONVERSIONS:
        raise UnitConversionError(f"Unknown velocity unit: {to_unit}")
    
    if from_unit == to_unit:
        return value
    
    # Convert to m/s first, then to target unit
    mps = value * VELOCITY_CONVERSIONS[from_unit]
    result = mps / VELOCITY_CONVERSIONS[to_unit]
    
    return result


def parse_unit_string(unit_string: str) -> tuple[str, str]:
    """
    Parse a unit string like "25.5 celsius" into value and unit.
    
    Args:
        unit_string: String containing value and unit
        
    Returns:
        tuple: (value_str, unit)
        
    Example:
        parse_unit_string("25.5 celsius") -> ("25.5", "celsius")
    """
    # Pattern to match number followed by unit
    pattern = r'^([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)\s*([a-zA-Z_]+)$'
    match = re.match(pattern, unit_string.strip())
    
    if not match:
        raise UnitConversionError(f"Invalid unit string format: {unit_string}")
    
    return match.group(1), match.group(2)


def convert_unit_string(
    unit_string: str,
    to_unit: str,
    unit_type: Optional[str] = None
) -> str:
    """
    Convert a unit string to a different unit.
    
    Args:
        unit_string: String like "25.5 celsius"
        to_unit: Target unit
        unit_type: Optional unit type hint
        
    Returns:
        str: Converted unit string
        
    Example:
        convert_unit_string("25.5 celsius", "fahrenheit") -> "77.9 fahrenheit"
    """
    value_str, from_unit = parse_unit_string(unit_string)
    value = float(value_str)
    
    converted_value = convert_units(value, from_unit, to_unit, unit_type)
    
    return f"{converted_value:.2f} {to_unit}"


def get_unit_info(unit: str) -> Dict[str, str]:
    """
    Get information about a unit.
    
    Args:
        unit: Unit name
        
    Returns:
        Dict with unit information
    """
    unit = unit.lower()
    
    for unit_type, units in UNIT_TYPE_MAPPINGS.items():
        if unit in units:
            return {
                "unit": unit,
                "type": unit_type,
                "supported": True,
                "compatible_units": units
            }
    
    return {
        "unit": unit,
        "type": "unknown",
        "supported": False,
        "compatible_units": []
    }


def list_all_unit_types() -> List[str]:
    """Get list of all supported unit types."""
    return list(UNIT_TYPE_MAPPINGS.keys())


def list_units_by_type(unit_type: str) -> List[str]:
    """Get list of units for a specific type."""
    return UNIT_TYPE_MAPPINGS.get(unit_type.lower(), [])
