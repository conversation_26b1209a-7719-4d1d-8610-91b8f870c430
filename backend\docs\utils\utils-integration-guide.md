# Utils Integration Guide

## Quick Start

This guide shows how to immediately start using the new utilities in the existing Ultimate Electrical Designer backend codebase.

## 1. Immediate Wins - Low Risk Integrations

### A. String Utilities in Services

**Current Pattern** (in `core/services/project_service.py`):
```python
# Manual string processing
project_name = project_data.name.strip().lower()
```

**Improved Pattern**:
```python
from core.utils import slugify, sanitize_text

# Standardized string processing
project_slug = slugify(project_data.name)
clean_description = sanitize_text(project_data.description)
```

### B. DateTime Utilities in Models

**Current Pattern** (in `core/models/base.py`):
```python
from sqlalchemy import func
created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=func.now())
```

**Improved Pattern**:
```python
from core.utils import utcnow_aware
created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utcnow_aware)
```

### C. Pagination in API Routes

**Current Pattern** (in `api/v1/project_routes.py`):
```python
# Manual pagination logic
skip = (page - 1) * per_page
projects = project_service.get_projects_list(page=page, per_page=per_page)
```

**Improved Pattern**:
```python
from core.utils import parse_pagination_params, create_pagination_response

pagination_params = parse_pagination_params(page, per_page)
result = project_service.get_projects_paginated(pagination_params)
return create_pagination_response(result.items, result)
```

## 2. Service Layer Integration

### Update Project Service

Add these imports to `core/services/project_service.py`:

```python
from core.utils import (
    generate_uuid7_str,
    utcnow_aware,
    format_datetime,
    slugify,
    sanitize_text
)
```

Enhance the `create_project` method:

```python
def create_project(self, project_data: ProjectCreateSchema) -> ProjectReadSchema:
    logger.info(f"Creating project: '{project_data.name}'")
    
    try:
        # Enhanced data processing with utilities
        project_dict = project_data.model_dump()
        project_dict.update({
            'slug': slugify(project_data.name),  # URL-friendly identifier
            'description': sanitize_text(project_data.description or ''),  # Clean HTML
            'created_at': utcnow_aware(),  # Timezone-aware timestamp
            'updated_at': utcnow_aware()
        })
        
        # Existing creation logic...
        new_project = self.project_repository.create(project_dict)
        # ... rest of method
```

### Update Repository Base Class

Enhance `core/repositories/base_repository.py`:

```python
from core.utils import PaginationParams, paginate_query, QueryBuilder

class BaseRepository(Generic[ModelType]):
    # ... existing code ...
    
    def get_paginated(
        self, 
        pagination_params: PaginationParams,
        filters: Optional[Dict[str, Any]] = None
    ) -> PaginationResult:
        """Get paginated results with optional filters."""
        query = self.db_session.query(self.model)
        
        if filters:
            builder = QueryBuilder(self.db_session, self.model)
            for field, value in filters.items():
                builder.filter_by_field(field, value)
            query = builder.build()
        
        return paginate_query(
            self.db_session, 
            query, 
            self.model, 
            pagination_params
        )
```

## 3. API Layer Integration

### Standardize List Endpoints

Update `api/v1/project_routes.py`:

```python
from core.utils import parse_pagination_params, parse_sort_params

@router.get("/", response_model=ProjectListResponseSchema)
async def list_projects(
    page: int = Query(1, ge=1, description="Page number"),
    per_page: int = Query(10, ge=1, le=100, description="Items per page"),
    sort_by: Optional[str] = Query(None, description="Sort field"),
    sort_order: Optional[str] = Query("asc", description="Sort order"),
    search: Optional[str] = Query(None, description="Search term"),
    project_service: ProjectService = Depends(get_project_service),
):
    """List projects with standardized pagination and search."""
    
    # Parse parameters using utilities
    pagination_params = parse_pagination_params(page, per_page)
    sort_params = parse_sort_params(sort_by, sort_order)
    
    # Build search filters
    filters = {}
    if search:
        filters['search'] = search
    
    # Get paginated results
    result = project_service.get_projects_paginated(
        pagination_params, 
        sort_params, 
        filters
    )
    
    return result
```

### Add Search Functionality

```python
from core.utils import QueryBuilder, build_search_query

def search_projects(
    self, 
    search_term: str, 
    pagination_params: PaginationParams
) -> PaginationResult:
    """Search projects with full-text capabilities."""
    
    searchable_fields = ['name', 'description', 'project_number']
    
    query = build_search_query(
        self.db_session,
        Project,
        {'search': search_term},
        searchable_fields
    )
    
    return paginate_query(
        self.db_session,
        query,
        Project,
        pagination_params
    )
```

## 4. Schema Integration

### JSON Validation for Settings

Update project schemas to use JSON validation:

```python
from core.utils import ValidatedJSON, validate_json_data
from pydantic import BaseModel, validator

class ProjectSettingsSchema(BaseModel):
    max_ambient_temp_c: float
    min_ambient_temp_c: float
    desired_maintenance_temp_c: float
    units: str = "celsius"
    notifications_enabled: bool = True

class ProjectCreateSchema(BaseModel):
    # ... existing fields ...
    settings: Optional[Dict[str, Any]] = None
    
    @validator('settings')
    def validate_settings(cls, v):
        if v is not None:
            return validate_json_data(v, ProjectSettingsSchema)
        return v
```

### Model Integration

Update `core/models/project.py`:

```python
from core.utils import ValidatedJSON
from core.schemas.project_schemas import ProjectSettingsSchema

class Project(Base, CommonColumns):
    # ... existing columns ...
    
    # Use validated JSON for settings
    settings = mapped_column(
        ValidatedJSON(ProjectSettingsSchema), 
        nullable=True
    )
```

## 5. File Operations Integration

### Import/Export Enhancement

Update import/export services:

```python
from core.utils import (
    read_csv_file, 
    write_csv_file, 
    temporary_file,
    FileIOError
)

class ProjectImportService:
    def import_from_csv(self, file_path: str) -> List[Dict]:
        """Import projects from CSV with validation."""
        try:
            projects_data = read_csv_file(
                file_path,
                max_rows=1000,  # Limit for safety
                encoding='utf-8'
            )
            
            # Process each project
            processed_projects = []
            for row in projects_data:
                # Use string utilities for data cleaning
                row['name'] = sanitize_text(row.get('name', ''))
                row['slug'] = slugify(row['name'])
                processed_projects.append(row)
            
            return processed_projects
            
        except FileIOError as e:
            logger.error(f"CSV import failed: {e}")
            raise
    
    def export_to_csv(self, projects: List[Dict]) -> str:
        """Export projects to CSV file."""
        with temporary_file(suffix='.csv') as temp_path:
            write_csv_file(temp_path, projects)
            return str(temp_path)
```

## 6. Testing Integration

### Update Existing Tests

Add utility testing to existing test files:

```python
# In tests/test_services/test_project_service.py
from core.utils import is_valid_uuid, parse_datetime

class TestProjectService:
    def test_create_project_with_utilities(self, project_service, sample_project_data):
        """Test project creation uses utilities correctly."""
        result = project_service.create_project(sample_project_data)
        
        # Verify UUID generation
        assert is_valid_uuid(result.id)
        
        # Verify slug generation
        assert result.slug == slugify(sample_project_data.name)
        
        # Verify timezone-aware timestamps
        assert result.created_at.tzinfo is not None
```

## 7. Migration Strategy

### Phase 1: Non-Breaking Changes (Week 1)
1. Add utility imports to services
2. Use string utilities for data processing
3. Implement pagination utilities in new endpoints

### Phase 2: Enhanced Features (Week 2)
1. Add search functionality using query utilities
2. Implement JSON validation for settings
3. Enhance file import/export with file I/O utilities

### Phase 3: Database Changes (Week 3+)
1. Plan UUID migration strategy
2. Update models to use UUID primary keys
3. Migrate existing data (if needed)

## 8. Best Practices

### Import Organization
```python
# Group utility imports at the top
from core.utils import (
    # UUID utilities
    generate_uuid7_str,
    is_valid_uuid,
    
    # DateTime utilities  
    utcnow_aware,
    format_datetime,
    
    # String utilities
    slugify,
    sanitize_text,
    
    # Pagination utilities
    parse_pagination_params,
    paginate_query,
)
```

### Error Handling
```python
from core.utils import JSONValidationError, FileIOError

try:
    result = validate_json_data(data, schema)
except JSONValidationError as e:
    logger.error(f"Validation failed: {e.message}")
    # Handle validation errors appropriately
```

### Logging Integration
```python
from core.utils import hash_string

# Use hashing for sensitive data in logs
logger.info(f"Processing user data: {hash_string(user_email)[:8]}...")
```

## 9. Performance Considerations

- **UUID Generation**: UUIDs are slightly larger than integers but provide better distribution
- **Pagination**: Use database-level pagination instead of loading all records
- **String Operations**: Utilities are optimized but cache results for repeated operations
- **File Operations**: Use temporary files for large operations to avoid memory issues

## 10. Monitoring and Validation

After integration, monitor:
- Database query performance with new pagination
- Memory usage with file operations
- Error rates with validation utilities
- Response times for paginated endpoints

The utilities are designed to be backward-compatible and can be integrated incrementally without breaking existing functionality.
