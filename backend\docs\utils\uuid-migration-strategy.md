# UUID Migration Strategy

## Overview

This document outlines the strategy for migrating from integer primary keys to UUIDv7 primary keys in the Ultimate Electrical Designer backend. UUIDv7 provides better database performance, global uniqueness, and improved scalability.

## Benefits of UUIDv7 Migration

### Performance Benefits
- **Better Index Distribution**: UUIDs reduce index hotspots compared to sequential integers
- **Reduced Lock Contention**: Distributed key generation reduces database locks
- **Improved Write Performance**: Less index fragmentation in high-concurrency scenarios

### Scalability Benefits
- **Global Uniqueness**: No need for centralized ID generation
- **Distributed Systems**: Better support for microservices and database sharding
- **Replication**: Easier master-master replication without ID conflicts

### Security Benefits
- **Non-Sequential**: Harder to guess or enumerate records
- **Larger Key Space**: 128-bit vs 64-bit integers
- **No Information Leakage**: IDs don't reveal creation order or count

## Migration Phases

### Phase 1: Preparation (Low Risk)
**Status**: ✅ **COMPLETE**
- ✅ UUID utilities implemented and tested
- ✅ UUIDv7 generation functions available
- ✅ Validation and conversion utilities ready
- ✅ Database column defaults prepared

### Phase 2: Dual Column Approach (Medium Risk)
**Timeline**: 2-4 weeks
**Approach**: Add UUID columns alongside existing integer IDs

#### Step 1: Add UUID Columns
```sql
-- Add UUID columns to existing tables
ALTER TABLE projects ADD COLUMN uuid VARCHAR(36) UNIQUE;
ALTER TABLE users ADD COLUMN uuid VARCHAR(36) UNIQUE;
ALTER TABLE components ADD COLUMN uuid VARCHAR(36) UNIQUE;

-- Add indexes for performance
CREATE INDEX idx_projects_uuid ON projects(uuid);
CREATE INDEX idx_users_uuid ON users(uuid);
CREATE INDEX idx_components_uuid ON components(uuid);
```

#### Step 2: Populate UUID Columns
```python
# Migration script to populate UUIDs
from core.utils import generate_uuid7_str
from core.models import Project, User, Component

def populate_uuids():
    # Generate UUIDs for existing records
    for project in session.query(Project).filter(Project.uuid.is_(None)):
        project.uuid = generate_uuid7_str()
    
    for user in session.query(User).filter(User.uuid.is_(None)):
        user.uuid = generate_uuid7_str()
    
    # ... repeat for all entities
    session.commit()
```

#### Step 3: Update Application Code
- Modify services to use UUID for new records
- Update API responses to include both ID and UUID
- Add UUID-based lookup methods
- Maintain backward compatibility with integer IDs

### Phase 3: Transition Period (Medium Risk)
**Timeline**: 4-8 weeks
**Approach**: Support both ID types, prefer UUIDs

#### API Changes
```python
# Support both ID types in routes
@router.get("/projects/{project_id}")
async def get_project(project_id: str):  # Accept both int and UUID strings
    if is_valid_uuid(project_id):
        return project_service.get_by_uuid(project_id)
    else:
        return project_service.get_by_id(int(project_id))
```

#### Service Layer Updates
```python
class ProjectService:
    def get_by_uuid(self, uuid: str) -> ProjectReadSchema:
        """Get project by UUID (preferred method)."""
        project = self.project_repository.get_by_uuid(uuid)
        return ProjectReadSchema.model_validate(project)
    
    def get_by_id(self, id: int) -> ProjectReadSchema:
        """Get project by integer ID (legacy method)."""
        project = self.project_repository.get_by_id(id)
        return ProjectReadSchema.model_validate(project)
```

#### Frontend Updates
- Update frontend to use UUIDs for new operations
- Maintain compatibility with existing bookmarks/links
- Add UUID support to all CRUD operations

### Phase 4: UUID Primary Keys (High Risk)
**Timeline**: 8-12 weeks
**Approach**: Make UUIDs the primary keys

#### Database Schema Changes
```sql
-- Create new tables with UUID primary keys
CREATE TABLE projects_new (
    id VARCHAR(36) PRIMARY KEY,  -- UUID as primary key
    name VARCHAR(100) NOT NULL,
    -- ... other columns
    legacy_id INTEGER UNIQUE  -- Keep old ID for reference
);

-- Migrate data
INSERT INTO projects_new (id, name, ..., legacy_id)
SELECT uuid, name, ..., id FROM projects;

-- Update foreign key references
-- This is the most complex part requiring careful planning
```

#### Application Updates
- Update all models to use UUID primary keys
- Update all foreign key references
- Update all repository methods
- Update all service methods
- Update all API routes

### Phase 5: Cleanup (Low Risk)
**Timeline**: 2-4 weeks
**Approach**: Remove legacy integer columns

#### Final Steps
- Remove legacy integer ID columns
- Update database constraints
- Clean up application code
- Update documentation
- Performance testing and optimization

## Implementation Strategy

### Recommended Approach: Gradual Migration

#### Option 1: Conservative (Recommended)
1. **Phase 2**: Add UUID columns, populate them
2. **Phase 3**: Use UUIDs for new records, support both for reads
3. **Phase 4**: Gradually migrate foreign keys
4. **Phase 5**: Remove integer IDs when fully migrated

#### Option 2: Aggressive (Higher Risk)
1. **Phase 2**: Add UUID columns
2. **Phase 4**: Direct migration to UUID primary keys
3. **Phase 5**: Cleanup

### Risk Mitigation

#### Database Risks
- **Backup Strategy**: Full database backups before each phase
- **Rollback Plan**: Ability to revert to integer IDs if needed
- **Testing**: Comprehensive testing in staging environment
- **Performance Monitoring**: Monitor query performance during migration

#### Application Risks
- **Backward Compatibility**: Maintain API compatibility during transition
- **Gradual Rollout**: Feature flags for UUID vs integer ID usage
- **Monitoring**: Application performance and error monitoring
- **Rollback Capability**: Ability to switch back to integer IDs

#### Data Integrity Risks
- **Validation**: Ensure all UUIDs are properly generated and unique
- **Referential Integrity**: Careful handling of foreign key relationships
- **Data Consistency**: Verify data consistency throughout migration

## Technical Implementation

### Model Updates
```python
# Current model
class Project(Base):
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    
# Transition model (Phase 2-3)
class Project(Base):
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    uuid: Mapped[str] = mapped_column(String(36), unique=True, default=uuid_column_default)
    
# Final model (Phase 4+)
class Project(Base):
    id: Mapped[str] = mapped_column(String(36), primary_key=True, default=uuid_column_default)
    legacy_id: Mapped[Optional[int]] = mapped_column(Integer, unique=True, nullable=True)
```

### Repository Updates
```python
class ProjectRepository:
    def get_by_uuid(self, uuid: str) -> Optional[Project]:
        """Get project by UUID."""
        return self.db_session.query(Project).filter(Project.uuid == uuid).first()
    
    def create_with_uuid(self, project_data: dict) -> Project:
        """Create project with UUID."""
        if 'uuid' not in project_data:
            project_data['uuid'] = generate_uuid7_str()
        return self.create(project_data)
```

### Service Updates
```python
class ProjectService:
    def create_project(self, project_data: ProjectCreateSchema) -> ProjectReadSchema:
        """Create project with UUID."""
        project_dict = project_data.model_dump()
        project_dict['uuid'] = generate_uuid7_str()  # Ensure UUID is set
        
        new_project = self.project_repository.create_with_uuid(project_dict)
        return ProjectReadSchema.model_validate(new_project)
```

## Testing Strategy

### Unit Tests
- UUID generation and validation
- Model creation with UUIDs
- Repository methods with UUID lookup
- Service methods with UUID handling

### Integration Tests
- API endpoints with UUID parameters
- Database operations with UUIDs
- Foreign key relationships with UUIDs
- Performance tests with UUID indexes

### Migration Tests
- Data migration scripts
- Backward compatibility
- Rollback procedures
- Data integrity validation

## Performance Considerations

### Database Performance
- **Index Strategy**: Proper indexing on UUID columns
- **Query Optimization**: Ensure queries use UUID indexes efficiently
- **Storage**: UUIDs use more storage than integers (36 bytes vs 8 bytes)

### Application Performance
- **Memory Usage**: UUIDs use more memory than integers
- **Serialization**: JSON serialization of UUIDs vs integers
- **Caching**: Cache key strategies with UUIDs

### Monitoring Metrics
- Query execution times
- Index usage statistics
- Memory consumption
- API response times

## Timeline and Milestones

### Month 1: Preparation
- Week 1-2: UUID utilities implementation ✅ **COMPLETE**
- Week 3-4: Migration planning and testing strategy

### Month 2: Dual Column Implementation
- Week 1-2: Add UUID columns to all tables
- Week 3-4: Populate UUIDs and update application code

### Month 3: Transition Period
- Week 1-2: Support both ID types in APIs
- Week 3-4: Frontend updates and testing

### Month 4: Primary Key Migration
- Week 1-2: Database schema changes
- Week 3-4: Application updates and testing

### Month 5: Cleanup and Optimization
- Week 1-2: Remove legacy columns
- Week 3-4: Performance optimization and documentation

## Success Criteria

### Technical Success
- ✅ All tables use UUID primary keys
- ✅ All foreign key relationships updated
- ✅ No data loss during migration
- ✅ Performance meets or exceeds current levels

### Business Success
- ✅ Zero downtime during migration
- ✅ No impact on user experience
- ✅ Improved system scalability
- ✅ Enhanced security posture

## Conclusion

The UUID migration strategy provides a clear path from integer IDs to UUIDv7 primary keys with minimal risk and maximum benefit. The gradual approach ensures system stability while providing immediate benefits from UUID adoption.

**Current Status**: Phase 1 complete, ready to begin Phase 2 when team decides to proceed.

**Recommendation**: Begin with Phase 2 (dual column approach) for new projects while maintaining existing integer IDs for current data.
