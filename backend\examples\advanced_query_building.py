# backend/examples/advanced_query_building.py
"""
Advanced Query Building Demonstration

This file demonstrates how to use the query utilities for building
complex database queries with filtering, searching, and sorting.
"""

import sys
sys.path.append('.')

from datetime import datetime, timedelta
from typing import Dict, Any, List

def demonstrate_query_builder():
    """Demonstrate the QueryBuilder utility."""
    print("🔍 QUERY BUILDER DEMONSTRATION")
    print("=" * 40)
    
    try:
        from core.utils import QueryBuilder
        from core.models.project import Project
        from core.database.session import get_db_session
        
        # Note: This is a demonstration of the API, not actual execution
        print("📝 QueryBuilder API Examples:")
        print()
        
        # Example 1: Basic filtering
        print("1. Basic Filtering:")
        print("```python")
        print("builder = QueryBuilder(session, Project)")
        print("builder.filter_by_field('is_deleted', False)")
        print("builder.filter_by_field('designer', '<PERSON>')")
        print("query = builder.build()")
        print("```")
        print("   → Filters projects by active status and designer")
        print()
        
        # Example 2: Text search
        print("2. Multi-field Text Search:")
        print("```python")
        print("builder = QueryBuilder(session, Project)")
        print("builder.filter_by_text_search(")
        print("    'heat tracing',")
        print("    ['name', 'description', 'project_number'],")
        print("    case_sensitive=False")
        print(")")
        print("query = builder.build()")
        print("```")
        print("   → Searches for 'heat tracing' across multiple fields")
        print()
        
        # Example 3: Date range filtering
        print("3. Date Range Filtering:")
        print("```python")
        print("from datetime import datetime, timedelta")
        print("builder = QueryBuilder(session, Project)")
        print("start_date = datetime.now() - timedelta(days=30)")
        print("end_date = datetime.now()")
        print("builder.filter_by_date_range('created_at', start_date, end_date)")
        print("query = builder.build()")
        print("```")
        print("   → Filters projects created in the last 30 days")
        print()
        
        # Example 4: Complex combined filtering
        print("4. Complex Combined Filtering:")
        print("```python")
        print("builder = QueryBuilder(session, Project)")
        print("builder.filter_by_field('is_deleted', False)")
        print("builder.filter_by_text_search('industrial', ['name', 'description'])")
        print("builder.filter_by_date_range('created_at', start_date, end_date)")
        print("builder.filter_by_field('installation_environment', 'outdoor')")
        print("query = builder.build()")
        print("```")
        print("   → Combines multiple filters for complex queries")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ QueryBuilder demonstration failed: {e}")
        return False


def demonstrate_search_query_building():
    """Demonstrate search query building utilities."""
    print("🔎 SEARCH QUERY BUILDING")
    print("-" * 40)
    
    try:
        from core.utils import build_search_query, apply_filters_from_dict
        
        print("📝 Search Query Examples:")
        print()
        
        # Example 1: Basic search
        print("1. Basic Search Query:")
        print("```python")
        print("search_params = {")
        print("    'search': 'heat tracing system',")
        print("    'case_sensitive': False")
        print("}")
        print("searchable_fields = ['name', 'description', 'project_number']")
        print("query = build_search_query(session, Project, search_params, searchable_fields)")
        print("```")
        print("   → Creates search query across specified fields")
        print()
        
        # Example 2: Advanced search with filters
        print("2. Advanced Search with Filters:")
        print("```python")
        print("search_params = {")
        print("    'search': 'industrial',")
        print("    'designer': 'John Smith',")
        print("    'installation_environment': 'outdoor',")
        print("    'created_at_start': '2024-01-01',")
        print("    'created_at_end': '2024-12-31',")
        print("    'include_deleted': False")
        print("}")
        print("query = build_search_query(session, Project, search_params)")
        print("```")
        print("   → Combines search with multiple filters")
        print()
        
        # Example 3: Dynamic filtering
        print("3. Dynamic Filtering from Request:")
        print("```python")
        print("filters = {")
        print("    'designer': 'Jane Doe',")
        print("    'installation_environment': 'indoor',")
        print("    'is_deleted': False")
        print("}")
        print("allowed_fields = ['designer', 'installation_environment', 'is_deleted']")
        print("query = apply_filters_from_dict(base_query, Project, filters, allowed_fields)")
        print("```")
        print("   → Applies filters dynamically with field validation")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Search query building failed: {e}")
        return False


def demonstrate_advanced_sorting():
    """Demonstrate advanced sorting capabilities."""
    print("📊 ADVANCED SORTING")
    print("-" * 40)
    
    try:
        from core.utils import build_dynamic_order_by
        
        print("📝 Advanced Sorting Examples:")
        print()
        
        # Example 1: Multi-field sorting
        print("1. Multi-field Sorting:")
        print("```python")
        print("sort_fields = [")
        print("    {'field': 'installation_environment', 'direction': 'asc'},")
        print("    {'field': 'created_at', 'direction': 'desc'},")
        print("    {'field': 'name', 'direction': 'asc'}")
        print("]")
        print("allowed_fields = ['name', 'created_at', 'installation_environment']")
        print("query = build_dynamic_order_by(query, Project, sort_fields, allowed_fields)")
        print("```")
        print("   → Sorts by environment (asc), then date (desc), then name (asc)")
        print()
        
        # Example 2: API-driven sorting
        print("2. API-driven Sorting:")
        print("```python")
        print("# From API request: ?sort=name:asc,created_at:desc")
        print("sort_string = 'name:asc,created_at:desc'")
        print("sort_fields = []")
        print("for sort_item in sort_string.split(','):")
        print("    field, direction = sort_item.split(':')")
        print("    sort_fields.append({'field': field, 'direction': direction})")
        print("query = build_dynamic_order_by(query, Project, sort_fields)")
        print("```")
        print("   → Parses sorting from API parameters")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced sorting demonstration failed: {e}")
        return False


def demonstrate_full_text_search():
    """Demonstrate full-text search capabilities."""
    print("📚 FULL-TEXT SEARCH")
    print("-" * 40)
    
    try:
        from core.utils import add_full_text_search
        
        print("📝 Full-text Search Examples:")
        print()
        
        print("1. PostgreSQL Full-text Search:")
        print("```python")
        print("# Requires PostgreSQL with full-text search setup")
        print("search_term = 'heat tracing industrial system'")
        print("search_fields = ['name', 'description', 'project_notes']")
        print("query = add_full_text_search(")
        print("    base_query, Project, search_term, search_fields, language='english'")
        print(")")
        print("```")
        print("   → Uses PostgreSQL's advanced full-text search")
        print()
        
        print("2. Ranked Search Results:")
        print("```python")
        print("# Full-text search with ranking")
        print("from sqlalchemy import func")
        print("query = query.add_columns(")
        print("    func.ts_rank(")
        print("        func.to_tsvector('english', Project.description),")
        print("        func.plainto_tsquery('english', search_term)")
        print("    ).label('rank')")
        print(").order_by('rank DESC')")
        print("```")
        print("   → Returns results ranked by relevance")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Full-text search demonstration failed: {e}")
        return False


def demonstrate_real_world_scenarios():
    """Demonstrate real-world query scenarios."""
    print("🌍 REAL-WORLD SCENARIOS")
    print("-" * 40)
    
    scenarios = [
        {
            "name": "Project Dashboard Query",
            "description": "Get recent projects for dashboard",
            "code": """
# Dashboard: Recent active projects by current user
builder = QueryBuilder(session, Project)
builder.filter_by_field('is_deleted', False)
builder.filter_by_field('designer', current_user.name)
builder.filter_by_date_range(
    'updated_at', 
    datetime.now() - timedelta(days=7), 
    datetime.now()
)
query = builder.build().order_by(Project.updated_at.desc()).limit(10)
"""
        },
        {
            "name": "Advanced Project Search",
            "description": "Complex search with multiple criteria",
            "code": """
# Advanced search: Industrial outdoor projects with specific voltage
search_params = {
    'search': 'industrial heat tracing',
    'installation_environment': 'outdoor',
    'min_ambient_temp_c_start': -20,
    'min_ambient_temp_c_end': 0,
    'include_deleted': False
}
query = build_search_query(session, Project, search_params)
"""
        },
        {
            "name": "Reporting Query",
            "description": "Generate reports with aggregations",
            "code": """
# Reporting: Projects by environment and designer
from sqlalchemy import func
query = session.query(
    Project.installation_environment,
    Project.designer,
    func.count(Project.id).label('project_count'),
    func.avg(Project.max_ambient_temp_c).label('avg_max_temp')
).filter(Project.is_deleted == False)
.group_by(Project.installation_environment, Project.designer)
.order_by('project_count DESC')
"""
        },
        {
            "name": "Maintenance Query",
            "description": "Find projects needing attention",
            "code": """
# Maintenance: Projects not updated in 30 days
builder = QueryBuilder(session, Project)
builder.filter_by_field('is_deleted', False)
builder.filter_by_date_range(
    'updated_at',
    None,  # No start date
    datetime.now() - timedelta(days=30)  # End date 30 days ago
)
stale_projects = builder.build().all()
"""
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['name']}:")
        print(f"   {scenario['description']}")
        print("```python")
        print(scenario['code'].strip())
        print("```")
        print()
    
    return True


def demonstrate_performance_optimization():
    """Demonstrate query performance optimization."""
    print("⚡ PERFORMANCE OPTIMIZATION")
    print("-" * 40)
    
    print("📈 Query Optimization Tips:")
    print()
    
    tips = [
        {
            "title": "Use Indexes Effectively",
            "description": "Ensure filtered fields have proper indexes",
            "example": "CREATE INDEX idx_projects_designer_env ON projects(designer, installation_environment);"
        },
        {
            "title": "Limit Result Sets",
            "description": "Always use pagination for large datasets",
            "example": "query.offset(skip).limit(per_page)"
        },
        {
            "title": "Select Only Needed Columns",
            "description": "Use specific column selection for large tables",
            "example": "session.query(Project.id, Project.name, Project.designer)"
        },
        {
            "title": "Use Database-Level Filtering",
            "description": "Filter in SQL, not in Python",
            "example": "query.filter(Project.is_deleted == False)  # Good\nprojects = [p for p in all_projects if not p.is_deleted]  # Bad"
        },
        {
            "title": "Optimize Text Search",
            "description": "Use database full-text search for complex queries",
            "example": "Use PostgreSQL's to_tsvector() instead of multiple LIKE queries"
        }
    ]
    
    for i, tip in enumerate(tips, 1):
        print(f"{i}. {tip['title']}:")
        print(f"   {tip['description']}")
        print(f"   Example: {tip['example']}")
        print()
    
    return True


def run_advanced_query_demonstration():
    """Run the complete advanced query building demonstration."""
    print("🔍 ADVANCED QUERY BUILDING DEMONSTRATION")
    print("=" * 50)
    
    demonstrations = [
        ("Query Builder", demonstrate_query_builder),
        ("Search Query Building", demonstrate_search_query_building),
        ("Advanced Sorting", demonstrate_advanced_sorting),
        ("Full-text Search", demonstrate_full_text_search),
        ("Real-world Scenarios", demonstrate_real_world_scenarios),
        ("Performance Optimization", demonstrate_performance_optimization),
    ]
    
    passed = 0
    total = len(demonstrations)
    
    for demo_name, demo_func in demonstrations:
        try:
            if demo_func():
                passed += 1
            else:
                print(f"\n❌ {demo_name} failed")
        except Exception as e:
            print(f"\n❌ {demo_name} error: {e}")
    
    print("\n" + "=" * 50)
    print(f"ADVANCED QUERY BUILDING: {passed}/{total} demonstrations completed")
    
    if passed == total:
        print("🎉 ADVANCED QUERY BUILDING READY!")
        print("\n📋 Advanced Query Capabilities:")
        print("✅ Dynamic query building with fluent interface")
        print("✅ Multi-field text search with case sensitivity options")
        print("✅ Date range filtering and complex conditions")
        print("✅ Multi-field sorting with validation")
        print("✅ Full-text search support (PostgreSQL)")
        print("✅ Performance optimization guidelines")
        print("✅ Real-world scenario examples")
    else:
        print(f"⚠️  {total - passed} demonstrations had issues.")
    
    return passed == total


if __name__ == "__main__":
    success = run_advanced_query_demonstration()
    sys.exit(0 if success else 1)
