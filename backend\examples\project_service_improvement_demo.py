# backend/examples/project_service_improvement_demo.py
"""
Demonstration of how the new utilities can improve the existing ProjectService.

This file shows a side-by-side comparison of the current implementation
vs. an improved version using the new utilities.
"""

import sys
sys.path.append('.')

from typing import Optional
from sqlalchemy.exc import SQLAlchemyError

# Import existing components
from config.logging_config import get_logger
from core.errors.exceptions import DatabaseError
from core.schemas.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectSummarySchema,
)

# Import our new utilities
from core.utils import (
    # Pagination utilities
    PaginationParams,
    SortParams,
    parse_pagination_params,
    parse_sort_params,
    paginate_query,
    create_pagination_response,
    
    # String utilities
    slugify,
    sanitize_text,
    
    # DateTime utilities
    utcnow_aware,
    format_datetime,
    
    # UUID utilities
    generate_uuid7_str,
)

logger = get_logger(__name__)


class CurrentProjectService:
    """Current implementation (simplified for demo)."""
    
    def __init__(self, project_repository):
        self.project_repository = project_repository
    
    def get_projects_list(
        self, page: int = 1, per_page: int = 10, include_deleted: bool = False
    ) -> ProjectListResponseSchema:
        """
        CURRENT IMPLEMENTATION - Manual pagination with issues.
        
        Issues with current approach:
        1. Manual offset calculation
        2. Inefficient total count (loads all records)
        3. In-memory filtering for soft deletes
        4. Manual pagination metadata calculation
        5. No sorting support
        6. No search functionality
        """
        logger.debug(
            f"Retrieving projects list: page={page}, per_page={per_page}, include_deleted={include_deleted}"
        )

        try:
            # Manual offset calculation
            skip = (page - 1) * per_page

            # Get projects from repository
            projects = self.project_repository.get_all(skip=skip, limit=per_page)

            # Inefficient filtering in memory
            if not include_deleted:
                projects = [p for p in projects if not p.is_deleted]

            # VERY inefficient total count - loads ALL records!
            all_projects = self.project_repository.get_all(skip=0, limit=1000)
            if not include_deleted:
                all_projects = [p for p in all_projects if not p.is_deleted]
            total = len(all_projects)

            # Convert to summary schemas
            project_summaries = [
                ProjectSummarySchema.model_validate(p) for p in projects
            ]

            # Manual pagination metadata calculation
            import math
            total_pages = math.ceil(total / per_page) if total > 0 else 1

            logger.debug(f"Retrieved {len(project_summaries)} projects (total: {total})")

            return ProjectListResponseSchema(
                projects=project_summaries,
                total=total,
                page=page,
                per_page=per_page,
                total_pages=total_pages,
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving projects list: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to retrieve projects list due to database error: {str(e)}",
                original_exception=e,
            )


class ImprovedProjectService:
    """Improved implementation using utilities."""
    
    def __init__(self, project_repository):
        self.project_repository = project_repository
    
    def get_projects_list(
        self, 
        page: int = 1, 
        per_page: int = 10, 
        include_deleted: bool = False,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "asc",
        search: Optional[str] = None
    ) -> ProjectListResponseSchema:
        """
        IMPROVED IMPLEMENTATION - Using utilities for better performance and features.
        
        Improvements:
        1. Automatic parameter validation
        2. Efficient database-level pagination
        3. Database-level filtering (no in-memory operations)
        4. Automatic pagination metadata calculation
        5. Built-in sorting support
        6. Search functionality
        7. Standardized response format
        """
        logger.debug(
            f"Retrieving projects list: page={page}, per_page={per_page}, "
            f"include_deleted={include_deleted}, sort_by={sort_by}, search='{search}'"
        )

        try:
            # Parse and validate parameters using utilities
            pagination_params = parse_pagination_params(page, per_page)
            sort_params = parse_sort_params(sort_by, sort_order)
            
            # Build base query
            query = self.project_repository.db_session.query(self.project_repository.model)
            
            # Apply soft delete filter at database level
            if not include_deleted:
                query = query.filter(self.project_repository.model.is_deleted == False)
            
            # Apply search filter if provided
            if search:
                search_term = search.strip()
                if search_term:
                    query = query.filter(
                        self.project_repository.model.name.ilike(f'%{search_term}%') |
                        self.project_repository.model.description.ilike(f'%{search_term}%') |
                        self.project_repository.model.project_number.ilike(f'%{search_term}%')
                    )
            
            # Use pagination utility for efficient database-level pagination
            result = paginate_query(
                self.project_repository.db_session,
                query,
                self.project_repository.model,
                pagination_params,
                sort_params,
                allowed_sort_fields=['name', 'created_at', 'updated_at', 'project_number']
            )
            
            # Convert to summary schemas
            project_summaries = [
                ProjectSummarySchema.model_validate(p) for p in result.items
            ]
            
            logger.debug(f"Retrieved {len(project_summaries)} projects (total: {result.total})")
            
            # Create standardized response using utility
            return create_pagination_response(project_summaries, result)

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving projects list: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to retrieve projects list due to database error: {str(e)}",
                original_exception=e,
            )
    
    def create_project(self, project_data: ProjectCreateSchema) -> dict:
        """
        IMPROVED PROJECT CREATION - Using utilities for better data processing.
        
        Improvements:
        1. Automatic slug generation for URLs
        2. Text sanitization for security
        3. Timezone-aware timestamps
        4. UUID generation (when ready to migrate)
        5. Standardized datetime formatting
        """
        logger.info(f"Creating project with utilities: '{project_data.name}'")
        
        try:
            # Convert schema to dict
            project_dict = project_data.model_dump()
            
            # Enhance with utilities
            now = utcnow_aware()
            project_dict.update({
                # Generate URL-friendly slug
                'slug': slugify(project_data.name),
                
                # Sanitize description for security
                'description': sanitize_text(project_data.description or ''),
                
                # Timezone-aware timestamps
                'created_at': now,
                'updated_at': now,
                
                # Ready for UUID migration (commented out for now)
                # 'uuid': generate_uuid7_str(),
            })
            
            # Create project via repository
            new_project = self.project_repository.create(project_dict)
            
            # Commit and refresh
            self.project_repository.db_session.commit()
            self.project_repository.db_session.refresh(new_project)
            
            logger.info(f"Project created successfully: {new_project.name} (ID: {new_project.id})")
            
            # Return with formatted timestamps
            result = {
                'id': new_project.id,
                'name': new_project.name,
                'slug': getattr(new_project, 'slug', slugify(new_project.name)),
                'created_at': format_datetime(new_project.created_at),
                'updated_at': format_datetime(new_project.updated_at),
            }
            
            return result
            
        except Exception as e:
            self.project_repository.db_session.rollback()
            logger.error(f"Error creating project: {e}", exc_info=True)
            raise


def demonstrate_improvements():
    """
    Demonstrate the improvements provided by utilities.
    """
    print("=" * 60)
    print("PROJECT SERVICE IMPROVEMENT DEMONSTRATION")
    print("=" * 60)
    print()
    
    print("🔧 CURRENT IMPLEMENTATION ISSUES:")
    print("  ❌ Manual pagination calculations")
    print("  ❌ Inefficient total count queries (loads all records)")
    print("  ❌ In-memory filtering instead of database-level")
    print("  ❌ No sorting or search capabilities")
    print("  ❌ Manual metadata calculations")
    print("  ❌ No input validation")
    print("  ❌ Inconsistent datetime handling")
    print()
    
    print("✨ IMPROVED IMPLEMENTATION BENEFITS:")
    print("  ✅ Automatic parameter validation")
    print("  ✅ Efficient database-level pagination")
    print("  ✅ Database-level filtering (better performance)")
    print("  ✅ Built-in sorting and search")
    print("  ✅ Automatic pagination metadata")
    print("  ✅ Standardized response format")
    print("  ✅ Timezone-aware datetime handling")
    print("  ✅ Security through text sanitization")
    print("  ✅ SEO-friendly slug generation")
    print("  ✅ Ready for UUID migration")
    print()
    
    print("📊 PERFORMANCE COMPARISON:")
    print("  Current: O(n) for total count (loads all records)")
    print("  Improved: O(1) for total count (database COUNT query)")
    print()
    print("  Current: In-memory filtering after database fetch")
    print("  Improved: Database-level filtering (WHERE clause)")
    print()
    print("  Current: No search capabilities")
    print("  Improved: Full-text search across multiple fields")
    print()
    
    print("🚀 INTEGRATION STEPS:")
    print("  1. Add utility imports to existing service")
    print("  2. Replace manual pagination with paginate_query()")
    print("  3. Add search and sorting parameters to API routes")
    print("  4. Use string utilities for data processing")
    print("  5. Implement timezone-aware datetime handling")
    print("  6. Plan UUID migration for future performance gains")
    print()
    
    print("💡 IMMEDIATE BENEFITS:")
    print("  • Reduced code complexity (50+ lines → 20 lines)")
    print("  • Better performance (database-level operations)")
    print("  • Enhanced security (input sanitization)")
    print("  • Improved user experience (search & sort)")
    print("  • Future-ready (UUID support)")
    print("  • Consistent patterns across all services")
    print()
    
    # Demonstrate utility usage
    print("🔍 UTILITY EXAMPLES:")
    print(f"  Slug generation: '{slugify('My Awesome Project!')}' → 'my-awesome-project'")
    print(f"  Text sanitization: '{sanitize_text('<script>alert(\"xss\")</script>Safe Text')}' → 'Safe Text'")
    print(f"  UUID generation: '{generate_uuid7_str()}'")
    print(f"  Timezone-aware time: '{format_datetime(utcnow_aware())}'")
    print()
    
    print("✅ UTILITIES ARE READY FOR INTEGRATION!")
    print("   See utils-integration-guide.md for step-by-step instructions.")


if __name__ == "__main__":
    demonstrate_improvements()
