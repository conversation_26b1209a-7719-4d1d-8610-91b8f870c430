# backend/examples/refactoring_demonstration.py
"""
Refactoring Demonstration

This file shows how to refactor existing code to use the new utilities,
demonstrating the before and after patterns for real improvement.
"""

import sys
sys.path.append('.')

from typing import Optional
from fastapi import APIRouter, Depends, Query

# Import utilities
from core.utils import (
    # Pagination utilities
    parse_pagination_params,
    parse_sort_params,
    create_pagination_response,
    
    # String utilities
    slugify,
    sanitize_text,
    
    # DateTime utilities
    utcnow_aware,
    format_datetime,
    
    # Unit conversion utilities
    convert_units,
    convert_unit_string,
    
    # CRUD factory
    create_simple_crud_router,
)


def demonstrate_api_route_refactoring():
    """
    Demonstrate how to refactor API routes using utilities.
    """
    print("=" * 60)
    print("API ROUTE REFACTORING DEMONSTRATION")
    print("=" * 60)
    print()
    
    print("🔧 BEFORE: Manual pagination implementation")
    print("   - 50+ lines of boilerplate code")
    print("   - Manual parameter validation")
    print("   - Inconsistent error handling")
    print("   - No search or sorting capabilities")
    print()
    
    print("✨ AFTER: Using utilities")
    print("   - 15 lines of clean code")
    print("   - Automatic parameter validation")
    print("   - Standardized error handling")
    print("   - Built-in search and sorting")
    print()
    
    # Show the code difference
    print("📝 CODE COMPARISON:")
    print()
    print("BEFORE (Current implementation):")
    print("```python")
    print("""
@router.get("/projects")
async def list_projects(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    project_service = Depends(get_project_service),
):
    # Manual validation
    if page < 1:
        page = 1
    if per_page < 1 or per_page > 100:
        per_page = 10
    
    # Manual pagination calculation
    skip = (page - 1) * per_page
    
    # Get projects (inefficient)
    projects = project_service.get_all(skip=skip, limit=per_page)
    
    # Manual total count (loads all records!)
    all_projects = project_service.get_all(skip=0, limit=1000)
    total = len(all_projects)
    
    # Manual metadata calculation
    import math
    total_pages = math.ceil(total / per_page) if total > 0 else 1
    has_next = page < total_pages
    has_prev = page > 1
    
    # Manual response construction
    return {
        "projects": [ProjectSummarySchema.model_validate(p) for p in projects],
        "total": total,
        "page": page,
        "per_page": per_page,
        "total_pages": total_pages,
        "has_next": has_next,
        "has_prev": has_prev,
    }
""")
    print("```")
    print()
    
    print("AFTER (Using utilities):")
    print("```python")
    print("""
@router.get("/projects")
async def list_projects(
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    sort_by: Optional[str] = Query(None),
    sort_order: Optional[str] = Query("asc"),
    search: Optional[str] = Query(None),
    project_service = Depends(get_project_service),
):
    # Automatic parameter validation and parsing
    pagination_params = parse_pagination_params(page, per_page)
    sort_params = parse_sort_params(sort_by, sort_order)
    
    # Enhanced service call with search and sorting
    result = project_service.get_projects_paginated(
        pagination_params, sort_params, {"search": search}
    )
    
    # Standardized response with automatic metadata
    return create_pagination_response(result.items, result)
""")
    print("```")
    print()


def demonstrate_service_refactoring():
    """
    Demonstrate service layer improvements.
    """
    print("🔧 SERVICE LAYER REFACTORING")
    print("=" * 40)
    print()
    
    print("BEFORE (Manual data processing):")
    print("```python")
    print("""
def create_project(self, project_data):
    # Manual string processing
    name = project_data.name.strip()
    description = project_data.description or ""
    
    # Manual datetime handling
    from datetime import datetime
    now = datetime.utcnow()  # Naive datetime!
    
    # Manual slug creation
    import re
    slug = re.sub(r'[^\\w\\s-]', '', name.lower())
    slug = re.sub(r'[-\\s]+', '-', slug)
    
    project_dict = {
        "name": name,
        "description": description,
        "slug": slug,
        "created_at": now,
        "updated_at": now,
    }
    
    return self.repository.create(project_dict)
""")
    print("```")
    print()
    
    print("AFTER (Using utilities):")
    print("```python")
    print("""
def create_project(self, project_data):
    # Automatic string processing with utilities
    project_dict = project_data.model_dump()
    project_dict.update({
        "slug": slugify(project_data.name),
        "description": sanitize_text(project_data.description or ""),
        "created_at": utcnow_aware(),  # Timezone-aware!
        "updated_at": utcnow_aware(),
    })
    
    return self.repository.create(project_dict)
""")
    print("```")
    print()


def demonstrate_crud_factory():
    """
    Demonstrate CRUD endpoint factory usage.
    """
    print("🏭 CRUD FACTORY DEMONSTRATION")
    print("=" * 40)
    print()
    
    print("BEFORE (Manual CRUD endpoints):")
    print("   - 200+ lines per entity")
    print("   - Repetitive boilerplate")
    print("   - Inconsistent error handling")
    print("   - Manual documentation")
    print()
    
    print("AFTER (CRUD Factory):")
    print("```python")
    print("""
# Create complete CRUD router in 10 lines!
from core.utils import create_simple_crud_router

project_router = create_simple_crud_router(
    entity_name="project",
    create_schema=ProjectCreateSchema,
    read_schema=ProjectReadSchema,
    update_schema=ProjectUpdateSchema,
    list_response_schema=ProjectListResponseSchema,
    service_class=ProjectService,
    service_dependency=get_project_service,
    searchable_fields=["name", "description"],
    sortable_fields=["name", "created_at", "project_number"],
)

# Automatically generates:
# - POST / (create)
# - GET /{id} (read)
# - PUT /{id} (update)
# - DELETE /{id} (delete)
# - GET / (list with pagination, search, sort)
""")
    print("```")
    print()


def demonstrate_unit_conversion():
    """
    Demonstrate unit conversion utilities.
    """
    print("🔄 UNIT CONVERSION DEMONSTRATION")
    print("=" * 40)
    print()
    
    # Temperature conversion
    temp_celsius = 25.0
    temp_fahrenheit = convert_units(temp_celsius, "celsius", "fahrenheit")
    print(f"Temperature: {temp_celsius}°C = {temp_fahrenheit:.1f}°F")
    
    # Power conversion
    power_watts = 1500
    power_hp = convert_units(power_watts, "watt", "horsepower")
    print(f"Power: {power_watts}W = {power_hp:.2f} HP")
    
    # Length conversion
    length_meters = 10.5
    length_feet = convert_units(length_meters, "meter", "foot")
    print(f"Length: {length_meters}m = {length_feet:.2f} ft")
    
    # String-based conversion
    unit_string = "85.5 fahrenheit"
    converted = convert_unit_string(unit_string, "celsius")
    print(f"String conversion: '{unit_string}' → '{converted}'")
    print()


def demonstrate_comprehensive_improvement():
    """
    Show comprehensive improvement using multiple utilities.
    """
    print("🚀 COMPREHENSIVE IMPROVEMENT EXAMPLE")
    print("=" * 40)
    print()
    
    print("Enhanced Project Creation with Multiple Utilities:")
    print("```python")
    print("""
from core.utils import (
    slugify, sanitize_text, utcnow_aware, format_datetime,
    convert_units, generate_uuid7_str, hash_string
)

def create_enhanced_project(self, project_data):
    # Process and validate data
    processed_data = {
        # String utilities
        "name": sanitize_text(project_data.name),
        "slug": slugify(project_data.name),
        "description": sanitize_text(project_data.description or ""),
        
        # DateTime utilities
        "created_at": utcnow_aware(),
        "updated_at": utcnow_aware(),
        
        # Unit conversion for temperature settings
        "max_temp_celsius": convert_units(
            project_data.max_temp, 
            project_data.temp_unit, 
            "celsius"
        ),
        
        # UUID for future migration
        "uuid": generate_uuid7_str(),
        
        # Data integrity
        "data_hash": hash_string(f"{project_data.name}:{project_data.description}"),
    }
    
    # Create project
    project = self.repository.create(processed_data)
    
    # Return with formatted timestamps
    return {
        **project.__dict__,
        "created_at": format_datetime(project.created_at),
        "updated_at": format_datetime(project.updated_at),
    }
""")
    print("```")
    print()


def show_performance_benefits():
    """
    Show performance improvements.
    """
    print("📊 PERFORMANCE BENEFITS")
    print("=" * 40)
    print()
    
    print("Database Operations:")
    print("  BEFORE: O(n) pagination (loads all records for count)")
    print("  AFTER:  O(1) pagination (database COUNT query)")
    print()
    
    print("Memory Usage:")
    print("  BEFORE: Loads all records into memory for filtering")
    print("  AFTER:  Database-level filtering with WHERE clauses")
    print()
    
    print("Code Complexity:")
    print("  BEFORE: 200+ lines for CRUD endpoints per entity")
    print("  AFTER:  10 lines using CRUD factory")
    print()
    
    print("Developer Productivity:")
    print("  BEFORE: Manual implementation of common patterns")
    print("  AFTER:  Reusable utilities for instant productivity")
    print()


if __name__ == "__main__":
    print("ULTIMATE ELECTRICAL DESIGNER - REFACTORING DEMONSTRATION")
    print("=" * 60)
    print()
    
    demonstrate_api_route_refactoring()
    demonstrate_service_refactoring()
    demonstrate_crud_factory()
    demonstrate_unit_conversion()
    demonstrate_comprehensive_improvement()
    show_performance_benefits()
    
    print("🎉 REFACTORING COMPLETE!")
    print("The utilities provide immediate value and can be integrated incrementally.")
    print("Start with low-risk improvements like string utilities and pagination.")
    print("Then move to more advanced features like CRUD factory and UUID migration.")
    print()
    print("📚 Next Steps:")
    print("1. Review utils-integration-guide.md")
    print("2. Start with Phase 1 integrations")
    print("3. Gradually adopt more advanced utilities")
    print("4. Monitor performance improvements")
    print("5. Update team documentation and training")
