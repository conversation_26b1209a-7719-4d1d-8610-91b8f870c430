# backend/examples/utils_integration_examples.py
"""
Examples of how to integrate the new utilities into the existing codebase.

This file demonstrates practical usage patterns for the utility functions
and shows how they can improve existing code.
"""

from typing import List, Dict, Any
from datetime import datetime

# Import our new utilities
from core.utils import (
    # UUID utilities
    generate_uuid7_str,
    is_valid_uuid,
    
    # DateTime utilities
    utcnow_aware,
    format_datetime,
    parse_datetime,
    
    # String utilities
    slugify,
    sanitize_text,
    hash_string,
    
    # Pagination utilities
    PaginationParams,
    parse_pagination_params,
    create_pagination_response,
    
    # File I/O utilities
    safe_write_file,
    read_json_file,
    write_json_file,
    temporary_file,
    
    # JSON validation utilities
    validate_json_data,
    JSONValidationError,
)

# Import existing schemas for examples
from core.schemas.project_schemas import ProjectCreateSchema, ProjectReadSchema


def example_uuid_integration():
    """
    Example: Using UUID utilities in model creation.
    
    This shows how to integrate UUIDv7 generation into existing models
    for better database performance.
    """
    print("=== UUID Integration Example ===")
    
    # Generate UUIDs for new records
    project_id = generate_uuid7_str()
    user_id = generate_uuid7_str()
    
    print(f"Generated Project ID: {project_id}")
    print(f"Generated User ID: {user_id}")
    print(f"Project ID is valid: {is_valid_uuid(project_id)}")
    
    # Example of how this could be used in a service
    def create_project_with_uuid(project_data: dict) -> dict:
        """Create project with auto-generated UUID."""
        project_data['id'] = generate_uuid7_str()
        project_data['created_at'] = utcnow_aware()
        return project_data
    
    sample_project = create_project_with_uuid({
        'name': 'Sample Project',
        'description': 'A test project'
    })
    
    print(f"Sample project: {sample_project}")
    print()


def example_datetime_integration():
    """
    Example: Using datetime utilities for consistent timestamp handling.
    
    This shows how to replace manual datetime handling with standardized utilities.
    """
    print("=== DateTime Integration Example ===")
    
    # Current approach in codebase (manual)
    current_time_manual = datetime.utcnow()
    print(f"Manual UTC time: {current_time_manual}")
    
    # Improved approach with utilities
    current_time_aware = utcnow_aware()
    formatted_time = format_datetime(current_time_aware)
    
    print(f"Timezone-aware UTC time: {current_time_aware}")
    print(f"Formatted time: {formatted_time}")
    
    # Parse datetime from string (useful for API inputs)
    parsed_time = parse_datetime("2023-12-25T15:30:45Z")
    print(f"Parsed datetime: {parsed_time}")
    
    # Example service method improvement
    def create_audit_log_entry(action: str, user_id: str) -> dict:
        """Create audit log with standardized timestamp."""
        return {
            'id': generate_uuid7_str(),
            'action': action,
            'user_id': user_id,
            'timestamp': format_datetime(utcnow_aware()),
            'created_at': utcnow_aware()
        }
    
    audit_entry = create_audit_log_entry("project_created", "user123")
    print(f"Audit entry: {audit_entry}")
    print()


def example_string_utilities_integration():
    """
    Example: Using string utilities for data processing.
    
    This shows how string utilities can improve data handling and validation.
    """
    print("=== String Utilities Integration Example ===")
    
    # Project name processing
    project_names = [
        "My Awesome Project!",
        "Café & Restaurant Design",
        "  Multiple   Spaces   Project  ",
        "<script>alert('xss')</script>Project"
    ]
    
    for name in project_names:
        slug = slugify(name)
        sanitized = sanitize_text(name)
        name_hash = hash_string(name)
        
        print(f"Original: '{name}'")
        print(f"  Slug: '{slug}'")
        print(f"  Sanitized: '{sanitized}'")
        print(f"  Hash: {name_hash[:8]}...")
        print()


def example_pagination_integration():
    """
    Example: Using pagination utilities in API endpoints.
    
    This shows how to standardize pagination across all list endpoints.
    """
    print("=== Pagination Integration Example ===")
    
    # Simulate API request parameters
    request_params = {
        'page': 2,
        'per_page': 5,
        'sort_by': 'name',
        'sort_order': 'asc'
    }
    
    # Parse pagination parameters
    pagination_params = parse_pagination_params(
        page=request_params.get('page'),
        per_page=request_params.get('per_page')
    )
    
    print(f"Pagination params: {pagination_params}")
    
    # Simulate database results
    mock_projects = [
        {'id': f'proj_{i}', 'name': f'Project {i}', 'status': 'active'}
        for i in range(1, 21)  # 20 total projects
    ]
    
    # Apply pagination (simplified example)
    start_idx = (pagination_params.page - 1) * pagination_params.per_page
    end_idx = start_idx + pagination_params.per_page
    page_items = mock_projects[start_idx:end_idx]
    
    # Create standardized response
    from core.utils.pagination_utils import PaginationResult, calculate_pagination_metadata
    
    metadata = calculate_pagination_metadata(
        total=len(mock_projects),
        page=pagination_params.page,
        per_page=pagination_params.per_page
    )
    
    pagination_result = PaginationResult(
        items=page_items,
        **metadata
    )
    
    response = create_pagination_response(page_items, pagination_result)
    print(f"Paginated response: {response}")
    print()


def example_file_io_integration():
    """
    Example: Using file I/O utilities for data import/export.
    
    This shows how to safely handle file operations with proper error handling.
    """
    print("=== File I/O Integration Example ===")
    
    # Example: Export project data to JSON
    project_data = {
        'id': generate_uuid7_str(),
        'name': 'Export Test Project',
        'created_at': format_datetime(utcnow_aware()),
        'settings': {
            'max_temp': 85.0,
            'min_temp': -10.0,
            'units': 'celsius'
        }
    }
    
    try:
        with temporary_file(suffix='.json') as temp_path:
            # Write project data
            write_json_file(temp_path, project_data)
            print(f"Exported project data to: {temp_path}")
            
            # Read it back
            imported_data = read_json_file(temp_path)
            print(f"Imported data: {imported_data}")
            
            # Verify data integrity
            data_hash = hash_string(str(imported_data))
            print(f"Data integrity hash: {data_hash[:16]}...")
            
    except Exception as e:
        print(f"File operation failed: {e}")
    
    print()


def example_json_validation_integration():
    """
    Example: Using JSON validation for configuration data.
    
    This shows how to validate JSON data stored in database columns.
    """
    print("=== JSON Validation Integration Example ===")
    
    from pydantic import BaseModel
    from typing import Optional
    
    # Define a schema for project settings
    class ProjectSettingsSchema(BaseModel):
        max_ambient_temp: float
        min_ambient_temp: float
        maintenance_temp: float
        units: str = "celsius"
        notifications_enabled: bool = True
        backup_frequency: Optional[str] = "daily"
    
    # Valid settings data
    valid_settings = {
        "max_ambient_temp": 45.0,
        "min_ambient_temp": -20.0,
        "maintenance_temp": 65.0,
        "units": "celsius"
    }
    
    # Invalid settings data
    invalid_settings = {
        "max_ambient_temp": "not_a_number",  # Should be float
        "min_ambient_temp": -20.0,
        # Missing required maintenance_temp
    }
    
    try:
        # Validate valid settings
        validated_settings = validate_json_data(valid_settings, ProjectSettingsSchema)
        print(f"Valid settings: {validated_settings}")
        print(f"Notifications enabled (default): {validated_settings.notifications_enabled}")
        
    except JSONValidationError as e:
        print(f"Validation failed: {e}")
    
    try:
        # Validate invalid settings
        validate_json_data(invalid_settings, ProjectSettingsSchema)
        
    except JSONValidationError as e:
        print(f"Expected validation error: {e.message}")
        print(f"Validation errors: {e.validation_errors}")
    
    print()


def example_combined_usage():
    """
    Example: Combining multiple utilities in a realistic scenario.
    
    This shows how utilities work together in a typical service method.
    """
    print("=== Combined Usage Example ===")
    
    def create_project_with_utilities(project_data: dict) -> dict:
        """
        Create a project using multiple utilities for a complete solution.
        """
        # Generate unique ID
        project_id = generate_uuid7_str()
        
        # Create URL-friendly slug from name
        project_slug = slugify(project_data.get('name', ''))
        
        # Sanitize description
        description = sanitize_text(project_data.get('description', ''))
        
        # Add timestamps
        now = utcnow_aware()
        
        # Create project record
        project = {
            'id': project_id,
            'name': project_data['name'],
            'slug': project_slug,
            'description': description,
            'created_at': format_datetime(now),
            'updated_at': format_datetime(now),
            'status': 'active'
        }
        
        # Generate audit trail
        audit_hash = hash_string(f"{project_id}:{project['name']}:{now}")
        
        print(f"Created project: {project}")
        print(f"Audit hash: {audit_hash[:16]}...")
        
        return project
    
    # Test the combined function
    sample_data = {
        'name': 'Advanced Heat Tracing System!',
        'description': '<p>A comprehensive <b>heat tracing</b> solution.</p>'
    }
    
    result = create_project_with_utilities(sample_data)
    print()


if __name__ == "__main__":
    """Run all examples to demonstrate utility integration."""
    print("Ultimate Electrical Designer - Utilities Integration Examples")
    print("=" * 60)
    print()
    
    example_uuid_integration()
    example_datetime_integration()
    example_string_utilities_integration()
    example_pagination_integration()
    example_file_io_integration()
    example_json_validation_integration()
    example_combined_usage()
    
    print("All examples completed successfully!")
    print("These utilities are now ready for integration into the existing codebase.")
