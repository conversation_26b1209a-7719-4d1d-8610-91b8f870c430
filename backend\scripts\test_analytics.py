#!/usr/bin/env python3
"""
Test Analytics and Reporting System

This script provides comprehensive test analytics, reporting, and monitoring
capabilities for the Ultimate Electrical Designer test suite.
"""

import json
import time
import sqlite3
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import subprocess
import xml.etree.ElementTree as ET


class TestAnalytics:
    """Test analytics and reporting system."""
    
    def __init__(self, db_path: str = "test_analytics.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the analytics database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Test runs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_runs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                test_category TEXT NOT NULL,
                total_tests INTEGER,
                passed_tests INTEGER,
                failed_tests INTEGER,
                skipped_tests INTEGER,
                execution_time REAL,
                coverage_percentage REAL,
                success_rate REAL,
                git_commit TEXT,
                branch TEXT
            )
        """)
        
        # Test failures table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS test_failures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                run_id INTEGER,
                test_name TEXT NOT NULL,
                test_file TEXT,
                failure_message TEXT,
                failure_type TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (run_id) REFERENCES test_runs (id)
            )
        """)
        
        # Performance metrics table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                run_id INTEGER,
                test_name TEXT NOT NULL,
                execution_time REAL,
                memory_usage INTEGER,
                cpu_usage REAL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (run_id) REFERENCES test_runs (id)
            )
        """)
        
        conn.commit()
        conn.close()
    
    def record_test_run(
        self,
        test_category: str,
        results: Dict[str, Any],
        coverage_percentage: Optional[float] = None
    ) -> int:
        """Record a test run in the analytics database."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get git information
        git_commit = self.get_git_commit()
        git_branch = self.get_git_branch()
        
        # Calculate metrics
        total_tests = results.get('total', 0)
        passed_tests = results.get('passed', 0)
        failed_tests = results.get('failed', 0)
        skipped_tests = results.get('skipped', 0)
        execution_time = results.get('execution_time', 0)
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        cursor.execute("""
            INSERT INTO test_runs (
                test_category, total_tests, passed_tests, failed_tests, 
                skipped_tests, execution_time, coverage_percentage, 
                success_rate, git_commit, branch
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_category, total_tests, passed_tests, failed_tests,
            skipped_tests, execution_time, coverage_percentage,
            success_rate, git_commit, git_branch
        ))
        
        run_id = cursor.lastrowid
        
        # Record failures
        for failure in results.get('failures', []):
            cursor.execute("""
                INSERT INTO test_failures (
                    run_id, test_name, test_file, failure_message, failure_type
                ) VALUES (?, ?, ?, ?, ?)
            """, (
                run_id, failure.get('name'), failure.get('file'),
                failure.get('message'), failure.get('type')
            ))
        
        conn.commit()
        conn.close()
        
        return run_id
    
    def get_git_commit(self) -> str:
        """Get current git commit hash."""
        try:
            result = subprocess.run(
                ['git', 'rev-parse', 'HEAD'],
                capture_output=True, text=True, cwd=Path(__file__).parent
            )
            return result.stdout.strip() if result.returncode == 0 else 'unknown'
        except:
            return 'unknown'
    
    def get_git_branch(self) -> str:
        """Get current git branch."""
        try:
            result = subprocess.run(
                ['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
                capture_output=True, text=True, cwd=Path(__file__).parent
            )
            return result.stdout.strip() if result.returncode == 0 else 'unknown'
        except:
            return 'unknown'
    
    def generate_trend_report(self, days: int = 30) -> Dict[str, Any]:
        """Generate test trend report for the last N days."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        since_date = datetime.now() - timedelta(days=days)
        
        cursor.execute("""
            SELECT 
                DATE(timestamp) as date,
                test_category,
                AVG(success_rate) as avg_success_rate,
                AVG(execution_time) as avg_execution_time,
                AVG(coverage_percentage) as avg_coverage,
                COUNT(*) as run_count
            FROM test_runs 
            WHERE timestamp >= ?
            GROUP BY DATE(timestamp), test_category
            ORDER BY date DESC, test_category
        """, (since_date,))
        
        trends = cursor.fetchall()
        conn.close()
        
        return {
            'period_days': days,
            'trends': [
                {
                    'date': row[0],
                    'category': row[1],
                    'success_rate': row[2],
                    'execution_time': row[3],
                    'coverage': row[4],
                    'run_count': row[5]
                }
                for row in trends
            ]
        }
    
    def generate_failure_analysis(self, days: int = 7) -> Dict[str, Any]:
        """Generate failure analysis report."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        since_date = datetime.now() - timedelta(days=days)
        
        # Most frequent failures
        cursor.execute("""
            SELECT 
                tf.test_name,
                tf.failure_type,
                COUNT(*) as failure_count,
                MAX(tf.timestamp) as last_failure
            FROM test_failures tf
            JOIN test_runs tr ON tf.run_id = tr.id
            WHERE tr.timestamp >= ?
            GROUP BY tf.test_name, tf.failure_type
            ORDER BY failure_count DESC
            LIMIT 20
        """, (since_date,))
        
        frequent_failures = cursor.fetchall()
        
        # Failure trends by category
        cursor.execute("""
            SELECT 
                tr.test_category,
                COUNT(tf.id) as total_failures,
                COUNT(DISTINCT tf.test_name) as unique_failing_tests
            FROM test_runs tr
            LEFT JOIN test_failures tf ON tr.id = tf.run_id
            WHERE tr.timestamp >= ?
            GROUP BY tr.test_category
            ORDER BY total_failures DESC
        """, (since_date,))
        
        category_failures = cursor.fetchall()
        
        conn.close()
        
        return {
            'period_days': days,
            'frequent_failures': [
                {
                    'test_name': row[0],
                    'failure_type': row[1],
                    'count': row[2],
                    'last_failure': row[3]
                }
                for row in frequent_failures
            ],
            'category_failures': [
                {
                    'category': row[0],
                    'total_failures': row[1],
                    'unique_failing_tests': row[2]
                }
                for row in category_failures
            ]
        }
    
    def generate_performance_report(self, days: int = 7) -> Dict[str, Any]:
        """Generate performance analysis report."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        since_date = datetime.now() - timedelta(days=days)
        
        # Slowest tests
        cursor.execute("""
            SELECT 
                pm.test_name,
                AVG(pm.execution_time) as avg_time,
                MAX(pm.execution_time) as max_time,
                COUNT(*) as run_count
            FROM performance_metrics pm
            JOIN test_runs tr ON pm.run_id = tr.id
            WHERE tr.timestamp >= ?
            GROUP BY pm.test_name
            ORDER BY avg_time DESC
            LIMIT 20
        """, (since_date,))
        
        slow_tests = cursor.fetchall()
        
        # Performance trends
        cursor.execute("""
            SELECT 
                DATE(tr.timestamp) as date,
                tr.test_category,
                AVG(tr.execution_time) as avg_execution_time
            FROM test_runs tr
            WHERE tr.timestamp >= ?
            GROUP BY DATE(tr.timestamp), tr.test_category
            ORDER BY date DESC
        """, (since_date,))
        
        performance_trends = cursor.fetchall()
        
        conn.close()
        
        return {
            'period_days': days,
            'slow_tests': [
                {
                    'test_name': row[0],
                    'avg_time': row[1],
                    'max_time': row[2],
                    'run_count': row[3]
                }
                for row in slow_tests
            ],
            'performance_trends': [
                {
                    'date': row[0],
                    'category': row[1],
                    'avg_execution_time': row[2]
                }
                for row in performance_trends
            ]
        }
    
    def generate_html_report(self, output_file: str = "test_analytics_report.html"):
        """Generate comprehensive HTML report."""
        trend_report = self.generate_trend_report(30)
        failure_report = self.generate_failure_analysis(7)
        performance_report = self.generate_performance_report(7)
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Analytics Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #e9f4ff; border-radius: 3px; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .success {{ color: green; }}
                .warning {{ color: orange; }}
                .error {{ color: red; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Ultimate Electrical Designer - Test Analytics Report</h1>
                <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="section">
                <h2>Test Trends (Last 30 Days)</h2>
                <table>
                    <tr>
                        <th>Date</th>
                        <th>Category</th>
                        <th>Success Rate</th>
                        <th>Execution Time</th>
                        <th>Coverage</th>
                        <th>Runs</th>
                    </tr>
                    {''.join([
                        f"<tr><td>{trend['date']}</td><td>{trend['category']}</td>"
                        f"<td class='{'success' if trend['success_rate'] > 90 else 'warning' if trend['success_rate'] > 70 else 'error'}'>"
                        f"{trend['success_rate']:.1f}%</td>"
                        f"<td>{trend['execution_time']:.2f}s</td>"
                        f"<td>{trend['coverage']:.1f}%</td>"
                        f"<td>{trend['run_count']}</td></tr>"
                        for trend in trend_report['trends']
                    ])}
                </table>
            </div>
            
            <div class="section">
                <h2>Frequent Failures (Last 7 Days)</h2>
                <table>
                    <tr>
                        <th>Test Name</th>
                        <th>Failure Type</th>
                        <th>Count</th>
                        <th>Last Failure</th>
                    </tr>
                    {''.join([
                        f"<tr><td>{failure['test_name']}</td><td>{failure['failure_type']}</td>"
                        f"<td class='error'>{failure['count']}</td><td>{failure['last_failure']}</td></tr>"
                        for failure in failure_report['frequent_failures']
                    ])}
                </table>
            </div>
            
            <div class="section">
                <h2>Performance Analysis (Last 7 Days)</h2>
                <table>
                    <tr>
                        <th>Test Name</th>
                        <th>Avg Time</th>
                        <th>Max Time</th>
                        <th>Run Count</th>
                    </tr>
                    {''.join([
                        f"<tr><td>{test['test_name']}</td>"
                        f"<td class='{'warning' if test['avg_time'] > 2 else ''}'>{test['avg_time']:.3f}s</td>"
                        f"<td class='{'error' if test['max_time'] > 5 else ''}'>{test['max_time']:.3f}s</td>"
                        f"<td>{test['run_count']}</td></tr>"
                        for test in performance_report['slow_tests']
                    ])}
                </table>
            </div>
        </body>
        </html>
        """
        
        with open(output_file, 'w') as f:
            f.write(html_content)
        
        print(f"📊 HTML report generated: {output_file}")


def main():
    """Main function for test analytics CLI."""
    parser = argparse.ArgumentParser(description="Test Analytics and Reporting")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Record command
    record_parser = subparsers.add_parser('record', help='Record test run results')
    record_parser.add_argument('category', help='Test category')
    record_parser.add_argument('--results-file', help='JSON file with test results')
    record_parser.add_argument('--coverage', type=float, help='Coverage percentage')
    
    # Report commands
    trend_parser = subparsers.add_parser('trends', help='Generate trend report')
    trend_parser.add_argument('--days', type=int, default=30, help='Number of days')
    
    failure_parser = subparsers.add_parser('failures', help='Generate failure analysis')
    failure_parser.add_argument('--days', type=int, default=7, help='Number of days')
    
    perf_parser = subparsers.add_parser('performance', help='Generate performance report')
    perf_parser.add_argument('--days', type=int, default=7, help='Number of days')
    
    html_parser = subparsers.add_parser('html', help='Generate HTML report')
    html_parser.add_argument('--output', default='test_analytics_report.html', help='Output file')
    
    args = parser.parse_args()
    
    analytics = TestAnalytics()
    
    if args.command == 'record':
        if args.results_file:
            with open(args.results_file) as f:
                results = json.load(f)
        else:
            results = {'total': 0, 'passed': 0, 'failed': 0, 'skipped': 0}
        
        run_id = analytics.record_test_run(args.category, results, args.coverage)
        print(f"✅ Recorded test run {run_id} for category: {args.category}")
    
    elif args.command == 'trends':
        report = analytics.generate_trend_report(args.days)
        print(json.dumps(report, indent=2))
    
    elif args.command == 'failures':
        report = analytics.generate_failure_analysis(args.days)
        print(json.dumps(report, indent=2))
    
    elif args.command == 'performance':
        report = analytics.generate_performance_report(args.days)
        print(json.dumps(report, indent=2))
    
    elif args.command == 'html':
        analytics.generate_html_report(args.output)
    
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
