#!/usr/bin/env python3
"""
Critical Fixes Test Script

This script tests the specific fixes made for Phase 1 critical issues:
1. Electrical route schema validation fixes
2. Component route URL fixes  
3. Router import and registration fixes

Run this script to verify the fixes work before running the full test suite.
"""

import sys
import os
from pathlib import Path

# Add backend to path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

def test_imports():
    """Test that all critical imports work."""
    print("🔍 Testing critical imports...")
    
    try:
        from core.models.enums import ElectricalNodeType
        print("✅ ElectricalNodeType import successful")
        print(f"   Available types: {[e.value for e in ElectricalNodeType]}")
    except ImportError as e:
        print(f"❌ ElectricalNodeType import failed: {e}")
        return False
    
    try:
        from core.schemas.electrical_schemas import ElectricalNodeCreateSchema
        print("✅ ElectricalNodeCreateSchema import successful")
    except ImportError as e:
        print(f"❌ ElectricalNodeCreateSchema import failed: {e}")
        return False
    
    try:
        from api.v1.electrical_routes import router as electrical_router
        print("✅ Electrical router import successful")
    except ImportError as e:
        print(f"❌ Electrical router import failed: {e}")
        return False
    
    try:
        from api.v1.component_routes import router as component_router
        print("✅ Component router import successful")
    except ImportError as e:
        print(f"❌ Component router import failed: {e}")
        return False
    
    return True

def test_schema_validation():
    """Test that schema validation works with correct data."""
    print("\n🔍 Testing schema validation...")
    
    try:
        from core.models.enums import ElectricalNodeType
        from core.schemas.electrical_schemas import ElectricalNodeCreateSchema
        
        # Test valid data (matches our fixed test)
        valid_data = {
            "project_id": 1,
            "name": "Main Distribution Panel",
            "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
            "voltage_v": 480.0,
            "location_description": "Electrical Room A",
            "power_capacity_kva": 1000.0,
        }
        
        schema = ElectricalNodeCreateSchema(**valid_data)
        print("✅ Schema validation successful with correct field names")
        print(f"   Node type: {schema.node_type}")
        print(f"   Voltage: {schema.voltage_v}V")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema validation failed: {e}")
        return False

def test_router_registration():
    """Test that routers can be registered in FastAPI app."""
    print("\n🔍 Testing router registration...")
    
    try:
        from fastapi import FastAPI
        from api.v1.electrical_routes import router as electrical_router
        from api.v1.component_routes import router as component_router
        
        app = FastAPI()
        app.include_router(electrical_router, prefix="/api/v1/electrical")
        app.include_router(component_router, prefix="/api/v1/components")
        
        print("✅ Router registration successful")
        print(f"   Electrical routes: {len(electrical_router.routes)} routes")
        print(f"   Component routes: {len(component_router.routes)} routes")
        
        return True
        
    except Exception as e:
        print(f"❌ Router registration failed: {e}")
        return False

def test_pytest_config():
    """Test that pytest configuration is valid."""
    print("\n🔍 Testing pytest configuration...")
    
    try:
        import configparser
        
        config = configparser.ConfigParser()
        config.read('pytest.ini')
        
        if 'tool:pytest' in config:
            print("✅ pytest.ini configuration is valid")
            
            # Check for critical markers
            markers = config.get('tool:pytest', 'markers', fallback='')
            if 'electrical:' in markers and 'component:' in markers:
                print("✅ Required test markers are present")
                return True
            else:
                print("⚠️ Some test markers may be missing")
                return True
        else:
            print("❌ pytest.ini configuration section not found")
            return False
            
    except Exception as e:
        print(f"❌ pytest configuration test failed: {e}")
        return False

def main():
    """Run all critical tests."""
    print("🚀 Running Critical Fixes Verification")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_schema_validation, 
        test_router_registration,
        test_pytest_config,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("📊 RESULTS SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All critical fixes verified successfully!")
        print("✅ Ready to run full test suite")
        return 0
    else:
        print("⚠️ Some critical fixes need attention")
        print("❌ Review failed tests before proceeding")
        return 1

if __name__ == "__main__":
    sys.exit(main())
