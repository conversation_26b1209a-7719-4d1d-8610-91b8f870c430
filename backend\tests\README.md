# Ultimate Electrical Designer Backend - Test Suite

This directory contains comprehensive tests for all implemented features across the four development phases of the Ultimate Electrical Designer backend.

## Test Organization

### Test Structure
```
tests/
├── fixtures/                    # Test data and fixtures
│   ├── __init__.py
│   └── test_data.py            # Sample data for all test suites
├── test_calculations/          # Phase 1: Calculations tests
│   ├── __init__.py
│   ├── test_heat_loss_calculator.py
│   ├── test_power_calculator.py
│   └── test_calculation_service.py
├── test_import_export/         # Phase 2: Import/Export tests
│   ├── __init__.py
│   ├── test_csv_parser.py
│   └── test_import_service.py
├── test_reports/               # Phase 3: Reports tests
│   ├── __init__.py
│   ├── test_document_generator.py
│   └── test_report_service.py
├── test_standards/             # Phase 4: Standards tests
│   ├── __init__.py
│   ├── test_ieee_515.py
│   └── test_standards_service.py
├── test_api/                   # API endpoint tests
│   ├── __init__.py
│   ├── test_calculations_routes.py
│   └── test_standards_routes.py
├── test_integration/           # Integration tests
│   ├── __init__.py
│   └── test_end_to_end_workflow.py
├── conftest.py                 # Shared fixtures and configuration
└── README.md                   # This file
```

## Test Categories

### 1. Unit Tests (`@pytest.mark.unit`)
- **Purpose**: Test individual components in isolation
- **Coverage**: All calculation engines, parsers, generators, and standards
- **Location**: `test_calculations/`, `test_import_export/`, `test_reports/`, `test_standards/`
- **Examples**:
  - Heat loss calculation algorithms
  - CSV parsing functionality
  - PDF report generation
  - IEEE 515 standard validation

### 2. Integration Tests (`@pytest.mark.integration`)
- **Purpose**: Test component interactions and workflows
- **Coverage**: Service integrations, data flow between phases
- **Location**: `test_integration/`
- **Examples**:
  - Import → Calculate → Report workflow
  - Standards validation → Parameter calculation
  - Multi-service error handling

### 3. API Tests (`@pytest.mark.api`)
- **Purpose**: Test REST API endpoints
- **Coverage**: All API routes, authentication, error handling
- **Location**: `test_api/`
- **Examples**:
  - Heat tracing calculation endpoints
  - Standards validation endpoints
  - Report generation endpoints

### 4. Performance Tests (`@pytest.mark.performance`)
- **Purpose**: Test system performance and scalability
- **Coverage**: Large dataset processing, response times
- **Examples**:
  - 1000+ circuit calculations
  - Large file imports
  - Concurrent API requests

## Test Data

### Sample Data Sets
The `fixtures/test_data.py` module provides comprehensive sample data:

- **SAMPLE_HEAT_TRACING_DATA**: Complete heat tracing project data
- **SAMPLE_ELECTRICAL_DATA**: Electrical system data
- **SAMPLE_STANDARDS_DATA**: Standards validation data
- **SAMPLE_REPORT_DATA**: Report generation data
- **SAMPLE_API_REQUESTS**: API request examples

### Test File Utilities
- `get_sample_csv_file()`: Creates temporary CSV files
- `get_sample_json_file()`: Creates temporary JSON files
- `cleanup_temp_file()`: Cleans up temporary files

## Running Tests

### Prerequisites
```bash
# Install test dependencies
python run_tests.py --install-deps

# Or manually:
pip install pytest pytest-asyncio pytest-cov pytest-html pytest-mock
```

### Basic Test Execution

#### Run All Tests
```bash
python run_tests.py --all
# or
python run_tests.py
```

#### Run Specific Test Categories
```bash
# Unit tests only
python run_tests.py --unit

# Integration tests only
python run_tests.py --integration

# API tests only
python run_tests.py --api

# Performance tests only
python run_tests.py --performance
```

#### Run Specific Test Files
```bash
# Specific test file
python run_tests.py --specific tests/test_calculations/test_heat_loss_calculator.py

# Specific test function
python run_tests.py --specific tests/test_calculations/test_heat_loss_calculator.py::TestHeatLossCalculator::test_calculate_heat_loss_valid_input
```

### Advanced Options

#### With Coverage Analysis
```bash
python run_tests.py --unit --coverage
python run_tests.py --all --coverage
```

#### Verbose Output
```bash
python run_tests.py --all --verbose
```

#### Generate Comprehensive Report
```bash
python run_tests.py --report
```

### Quality Checks

#### Code Linting
```bash
python run_tests.py --lint
```

#### Type Checking
```bash
python run_tests.py --type-check
```

### Utilities

#### Clean Test Artifacts
```bash
python run_tests.py --clean
```

## Test Configuration

### Pytest Configuration (`pytest.ini`)
- Test discovery patterns
- Coverage settings (85% minimum)
- Logging configuration
- Custom markers
- Timeout settings (300 seconds)

### Coverage Configuration (`.coveragerc`)
- Source directories: `core/`, `api/`
- Exclusions: tests, migrations, cache files
- Branch coverage enabled
- HTML, XML, and JSON report generation

### Shared Fixtures (`conftest.py`)
- Database mocking
- Authentication mocking
- Service mocking
- Sample data fixtures
- Temporary file management
- Performance monitoring

## Test Implementation Examples

### Unit Test Example
```python
class TestHeatLossCalculator:
    def setup_method(self):
        self.calculator = HeatLossCalculator()
    
    def test_calculate_heat_loss_valid_input(self):
        circuit_data = {
            "pipe_diameter": 0.1,
            "insulation_thickness": 0.05,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
        }
        
        result = self.calculator.calculate_heat_loss(circuit_data)
        
        assert result["heat_loss_per_meter"] > 0
        assert "thermal_resistance" in result
```

### Integration Test Example
```python
@pytest.mark.asyncio
async def test_complete_design_workflow(self):
    # Import data
    import_result = await self.import_service.import_project_data(...)
    
    # Perform calculations
    calc_result = await self.calculation_service.calculate_heat_tracing_system(...)
    
    # Validate against standards
    validation_result = self.standards_service.validate_design_against_standards(...)
    
    # Generate report
    report_result = self.report_service.generate_calculation_report(...)
    
    assert all results are successful
```

### API Test Example
```python
@patch('api.dependencies.get_current_user')
def test_calculate_heat_tracing_valid_request(self, mock_get_user):
    mock_get_user.return_value = {"id": "test_user"}
    
    response = self.client.post(
        "/api/v1/calculations/heat-tracing",
        json=sample_request
    )
    
    assert response.status_code == 200
    assert response.json()["success"] is True
```

## Coverage Targets

### Current Coverage Goals
- **Overall**: 85% minimum
- **Core Modules**: 90% minimum
- **API Modules**: 85% minimum
- **Critical Calculations**: 95% minimum

### Coverage Reports
- **HTML Report**: `htmlcov/index.html`
- **Terminal Report**: Displayed after test execution
- **XML Report**: `coverage.xml` (for CI/CD)

## Continuous Integration

### GitHub Actions Integration
The test suite is designed to integrate with CI/CD pipelines:

```yaml
- name: Run Tests
  run: |
    python run_tests.py --all --coverage
    python run_tests.py --lint
    python run_tests.py --type-check
```

### Test Artifacts
- Test results: `test-results.xml`
- Coverage report: `coverage.xml`
- HTML test report: `test-report.html`

## Performance Benchmarks

### Target Performance Metrics
- **Single Circuit Calculation**: < 100ms
- **100 Circuit Batch**: < 5 seconds
- **Report Generation**: < 30 seconds
- **Standards Validation**: < 10 seconds per standard

### Performance Test Examples
```python
@pytest.mark.performance
def test_large_dataset_performance(self, performance_monitor):
    performance_monitor.start()
    
    # Process large dataset
    result = process_large_dataset(1000_circuits)
    
    performance_monitor.stop()
    
    assert performance_monitor.execution_time < 30.0
    assert result["success"] is True
```

## Troubleshooting

### Common Issues

#### Import Errors
```bash
# Ensure backend directory is in Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"
```

#### Database Connection Issues
```bash
# Tests use in-memory SQLite by default
# Check conftest.py for database fixtures
```

#### Async Test Issues
```bash
# Ensure pytest-asyncio is installed
pip install pytest-asyncio
```

### Debug Mode
```bash
# Run with debug output
python run_tests.py --all --verbose

# Run specific test with pdb
python -m pytest tests/test_calculations/test_heat_loss_calculator.py::test_specific_function --pdb
```

## Contributing

### Adding New Tests
1. Follow the existing test structure
2. Use appropriate markers (`@pytest.mark.unit`, etc.)
3. Include docstrings explaining test purpose
4. Use fixtures for common setup
5. Ensure tests are deterministic and isolated

### Test Naming Conventions
- Test files: `test_<module_name>.py`
- Test classes: `Test<ClassName>`
- Test methods: `test_<functionality>_<condition>`

### Best Practices
- One assertion per test when possible
- Use descriptive test names
- Mock external dependencies
- Clean up resources in teardown
- Test both success and failure cases
- Include edge cases and boundary conditions

## Maintenance

### Regular Tasks
- Review and update test data
- Monitor coverage trends
- Update performance benchmarks
- Refresh mock data
- Clean up obsolete tests

### Test Data Maintenance
- Keep sample data realistic and current
- Update test data when business rules change
- Ensure test data covers edge cases
- Maintain data consistency across test suites
