# backend/tests/conftest.py
"""
Pytest configuration and shared fixtures for the test suite.

This module provides common test fixtures and configuration that can be
used across all test modules.
"""

import os
import sys
import tempfile
import asyncio
from typing import Dict, Any, Generator
from unittest.mock import Mock, patch

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.models.enums import InstallationEnvironment
from core.models.project import Project

# Import heat tracing models for testing
from core.models.heat_tracing import Pipe, Vessel, HTCircuit, ControlCircuit
from core.models.components import Component, ComponentCategory
from core.models.switchboard import Switchboard, Feeder
from core.models.users import User, UserPreference
from core.models.documents import (
    ImportedDataRevision,
    ExportedDocument,
    CalculationStandard,
)
from core.models.base import Base

# Import only the models we need for testing to avoid dependency issues
# from core.models import *  # Don't import all models


@pytest.fixture(scope="session")
def test_engine():
    """Create an in-memory SQLite database engine for testing."""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False,  # Set to True for SQL debugging
    )

    # Create all tables using the Base metadata
    # This will create all tables that have been imported
    Base.metadata.create_all(engine)

    return engine


@pytest.fixture(scope="function")
def db_session(test_engine):
    """Create a database session for testing with automatic rollback."""
    connection = test_engine.connect()
    transaction = connection.begin()

    # Create session bound to the connection
    Session = sessionmaker(bind=connection)
    session = Session()

    yield session

    # Rollback transaction and close connection
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def sample_project_data():
    """Sample project data for testing."""
    return {
        "name": "Test Heat Tracing Project",
        "project_number": "HT-TEST-001",
        "description": "A test project for heat tracing design",
        "designer": "Test Engineer",
        "notes": "Test project notes",
        "min_ambient_temp_c": -20.0,
        "max_ambient_temp_c": 40.0,
        "desired_maintenance_temp_c": 65.0,
        "wind_speed_ms": 5.0,
        "installation_environment": InstallationEnvironment.OUTDOOR,
        "available_voltages_json": "[120, 240, 480]",
        "default_cable_manufacturer": "Test Manufacturer",
        "default_control_device_manufacturer": "Test Control Manufacturer",
    }


@pytest.fixture
def sample_project_create_data():
    """Sample project creation data (without auto-generated fields)."""
    return {
        "name": "Test Heat Tracing Project",
        "project_number": "HT-TEST-001",
        "description": "A test project for heat tracing design",
        "designer": "Test Engineer",
        "notes": "Test project notes",
        "min_ambient_temp_c": -20.0,
        "max_ambient_temp_c": 40.0,
        "desired_maintenance_temp_c": 65.0,
        "wind_speed_ms": 5.0,
        "installation_environment": InstallationEnvironment.OUTDOOR,
        "available_voltages_json": "[120, 240, 480]",
        "default_cable_manufacturer": "Test Manufacturer",
        "default_control_device_manufacturer": "Test Control Manufacturer",
    }


@pytest.fixture
def sample_project_update_data():
    """Sample project update data."""
    return {
        "name": "Updated Test Project",
        "description": "Updated description",
        "max_ambient_temp_c": 45.0,
        "default_cable_manufacturer": "Updated Manufacturer",
    }


@pytest.fixture
def sample_project_orm(db_session, sample_project_data):
    """Create a sample Project ORM instance in the database."""
    project = Project(**sample_project_data)
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def multiple_projects_orm(db_session):
    """Create multiple Project ORM instances for testing pagination."""
    projects = []
    for i in range(15):
        project_data = {
            "name": f"Test Project {i + 1}",
            "project_number": f"HT-TEST-{i + 1:03d}",
            "description": f"Test project {i + 1} description",
            "designer": f"Engineer {i + 1}",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
            "wind_speed_ms": 5.0,
            "installation_environment": InstallationEnvironment.OUTDOOR
            if i % 2 == 0
            else InstallationEnvironment.INDOOR,
        }
        project = Project(**project_data)
        db_session.add(project)
        projects.append(project)

    db_session.commit()

    # Refresh all projects to get their IDs
    for project in projects:
        db_session.refresh(project)

    return projects


@pytest.fixture
def invalid_project_data():
    """Invalid project data for testing validation."""
    return {
        "name": "",  # Invalid: empty name
        "project_number": "invalid chars!@#",  # Invalid: special characters
        "min_ambient_temp_c": 50.0,  # Invalid: min > max
        "max_ambient_temp_c": 40.0,
        "desired_maintenance_temp_c": -10.0,  # Invalid: negative maintenance temp
        "wind_speed_ms": -5.0,  # Invalid: negative wind speed
        "available_voltages_json": "invalid json",  # Invalid: malformed JSON
    }


# Test utilities
class TestUtils:
    """Utility functions for testing."""

    @staticmethod
    def assert_project_fields_equal(project1, project2, exclude_fields=None):
        """Assert that two projects have equal field values."""
        exclude_fields = exclude_fields or ["id", "created_at", "updated_at"]

        for field in project1.__table__.columns.keys():
            if field not in exclude_fields:
                assert getattr(project1, field) == getattr(project2, field), (
                    f"Field {field} differs"
                )

    @staticmethod
    def create_test_project(session, **overrides):
        """Create a test project with optional field overrides."""
        default_data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 65.0,
        }
        default_data.update(overrides)

        project = Project(**default_data)
        session.add(project)
        session.commit()
        session.refresh(project)
        return project


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils


# Test data setup will be added later when needed


@pytest.fixture
def sample_project(db_session, sample_project_data):
    """Create a sample Project instance in the database."""
    project = Project(**sample_project_data)
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project


@pytest.fixture
def sample_user(db_session):
    """Create a sample User instance in the database."""
    user_data = {
        "name": "Test User",
        "email": "<EMAIL>",
        "is_active": True,
    }
    user = User(**user_data)
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


# FastAPI test client fixtures
from fastapi import FastAPI
from fastapi.testclient import TestClient


@pytest.fixture
def app(db_session):
    """Create a FastAPI app for testing with database session override."""
    app = FastAPI()

    # Import and include all API routers
    from api.v1.document_routes import router as document_router
    from api.v1.project_routes import router as project_router
    from api.v1.user_routes import router as user_router
    from api.v1.switchboard_routes import router as switchboard_router
    from api.v1.heat_tracing_routes import router as heat_tracing_router
    from api.v1.activity_log_routes import router as activity_log_router

    # Import the missing routers that were causing 404 errors
    electrical_router = None
    component_router = None
    electrical_router_available = False
    component_router_available = False

    try:
        from api.v1.electrical_routes import router as electrical_router

        electrical_router_available = True
        print("✅ Successfully imported electrical_routes")
    except ImportError as e:
        print(f"❌ Warning: Could not import electrical_routes: {e}")
        electrical_router_available = False

    try:
        from api.v1.component_routes import router as component_router

        component_router_available = True
        print("✅ Successfully imported component_routes")
    except ImportError as e:
        print(f"❌ Warning: Could not import component_routes: {e}")
        component_router_available = False

    # Include all routers with proper prefixes
    app.include_router(document_router, prefix="/api/v1/documents")
    app.include_router(project_router, prefix="/api/v1/projects")
    app.include_router(user_router, prefix="/api/v1/users")
    app.include_router(switchboard_router, prefix="/api/v1/switchboards")
    app.include_router(heat_tracing_router, prefix="/api/v1/heat-tracing")
    app.include_router(activity_log_router, prefix="/api/v1")

    # Include the electrical and component routers if they were imported successfully
    if electrical_router_available and electrical_router is not None:
        app.include_router(electrical_router, prefix="/api/v1/electrical")

    if component_router_available and component_router is not None:
        app.include_router(component_router, prefix="/api/v1/components")

    # Override the database session dependencies
    from core.database.session import get_db_session
    from core.database import get_db

    def override_get_db_session():
        return db_session

    def override_get_db():
        yield db_session

    app.dependency_overrides[get_db_session] = override_get_db_session
    app.dependency_overrides[get_db] = override_get_db

    # Override service dependencies to use test database session
    from api.v1.project_routes import get_project_service
    from api.v1.heat_tracing_routes import get_heat_tracing_service

    def override_get_project_service():
        from core.repositories.project_repository import ProjectRepository
        from core.services.project_service import ProjectService

        project_repo = ProjectRepository(db_session)
        return ProjectService(project_repo)

    def override_get_heat_tracing_service():
        from core.repositories.heat_tracing_repository import HeatTracingRepository
        from core.services.heat_tracing_service import HeatTracingService

        heat_tracing_repo = HeatTracingRepository(db_session)
        return HeatTracingService(heat_tracing_repo)

    # Apply service dependency overrides
    app.dependency_overrides[get_project_service] = override_get_project_service
    app.dependency_overrides[get_heat_tracing_service] = (
        override_get_heat_tracing_service
    )

    # Add service overrides for electrical and component routes if available
    # Note: These services may not have dependency injection functions,
    # so we'll handle them in the route tests with mocking instead
    print(
        f"Router availability: electrical={electrical_router_available}, component={component_router_available}"
    )

    return app


@pytest.fixture
def client(app):
    """Create a test client."""
    return TestClient(app)


# Additional fixtures for comprehensive testing

# Import test data
try:
    from tests.fixtures.test_data import (
        SAMPLE_HEAT_TRACING_DATA,
        SAMPLE_ELECTRICAL_DATA,
        SAMPLE_STANDARDS_DATA,
        SAMPLE_REPORT_DATA,
        MOCK_DATABASE_DATA,
    )
except ImportError:
    # Fallback data if test_data module is not available
    SAMPLE_HEAT_TRACING_DATA = {"circuits": [], "design_parameters": {}}
    SAMPLE_ELECTRICAL_DATA = {"switchboards": []}
    SAMPLE_STANDARDS_DATA = {"design_data": {}, "input_data": {}}
    SAMPLE_REPORT_DATA = {
        "project_data": {},
        "circuits_data": [],
        "calculations_data": [],
    }
    MOCK_DATABASE_DATA = {"projects": [], "circuits": []}


# Service fixtures
@pytest.fixture
def mock_calculation_service():
    """Mock calculation service."""
    service = Mock()
    service.calculate_heat_tracing_system.return_value = {
        "success": True,
        "circuit_results": [],
        "system_summary": {},
    }
    service.calculate_electrical_system.return_value = {
        "success": True,
        "switchboard_results": [],
        "system_summary": {},
    }
    return service


@pytest.fixture
def mock_import_service():
    """Mock import service."""
    service = Mock()
    service.import_project_data.return_value = {
        "success": True,
        "imported_data": {"circuits": []},
        "import_summary": {},
    }
    return service


@pytest.fixture
def mock_report_service():
    """Mock report service."""
    service = Mock()
    service.generate_calculation_report.return_value = {
        "success": True,
        "file_path": "/tmp/test_report.pdf",
        "report_type": "calculation_report",
    }
    service.export_data.return_value = {
        "success": True,
        "file_path": "/tmp/test_export.json",
        "format": "json",
    }
    return service


@pytest.fixture
def mock_standards_service():
    """Mock standards service."""
    service = Mock()
    service.validate_design_against_standards.return_value = {
        "validation_summary": {"total_standards_checked": 1},
        "individual_results": {},
        "compliance_report": {},
        "cross_standard_conflicts": [],
        "recommendations": [],
    }
    service.calculate_standards_parameters.return_value = {
        "calculation_summary": {"total_standards_used": 1},
        "individual_results": {},
        "consolidated_parameters": {},
        "parameter_comparisons": {},
    }
    return service


# Data fixtures
@pytest.fixture
def sample_heat_tracing_data():
    """Sample heat tracing data for testing."""
    return SAMPLE_HEAT_TRACING_DATA.copy()


@pytest.fixture
def sample_electrical_data():
    """Sample electrical data for testing."""
    return SAMPLE_ELECTRICAL_DATA.copy()


@pytest.fixture
def sample_standards_data():
    """Sample standards data for testing."""
    return SAMPLE_STANDARDS_DATA.copy()


@pytest.fixture
def sample_report_data():
    """Sample report data for testing."""
    return SAMPLE_REPORT_DATA.copy()


@pytest.fixture
def sample_circuit():
    """Single sample circuit for testing."""
    circuits = SAMPLE_HEAT_TRACING_DATA.get("circuits", [])
    if circuits:
        return circuits[0].copy()
    return {"circuit_id": "HT-001", "circuit_name": "Test Circuit"}


@pytest.fixture
def sample_design_parameters():
    """Sample design parameters for testing."""
    return SAMPLE_HEAT_TRACING_DATA.get("design_parameters", {}).copy()


# File fixtures
@pytest.fixture
def temp_directory():
    """Temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def temp_file():
    """Temporary file for testing."""
    temp_file = tempfile.NamedTemporaryFile(delete=False)
    temp_file.close()
    yield temp_file.name
    try:
        os.unlink(temp_file.name)
    except FileNotFoundError:
        pass


# Authentication fixtures
@pytest.fixture
def mock_current_user():
    """Mock current user for authentication."""
    return {
        "id": "test_user_123",
        "email": "<EMAIL>",
        "name": "Test User",
        "role": "engineer",
        "permissions": ["read", "write", "calculate", "generate_reports"],
    }


@pytest.fixture
def mock_auth_token():
    """Mock authentication token."""
    return "mock_jwt_token_123456789"


# Performance fixtures
@pytest.fixture
def performance_monitor():
    """Performance monitoring fixture."""
    import time

    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None

        def start(self):
            self.start_time = time.time()

        def stop(self):
            self.end_time = time.time()

        @property
        def execution_time(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None

    return PerformanceMonitor()


# Error handling fixtures
@pytest.fixture
def mock_logger():
    """Mock logger for testing logging functionality."""
    logger = Mock()
    logger.debug = Mock()
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.critical = Mock()
    return logger


# Validation fixtures
@pytest.fixture
def validation_rules():
    """Standard validation rules for testing."""
    return {
        "pipe_diameter": {"min": 0.001, "max": 2.0},
        "pipe_length": {"min": 0.1, "max": 10000.0},
        "voltage": {"min": 12, "max": 1000},
        "power_density": {"min": 1, "max": 200},
        "temperature": {"min": -50, "max": 300},
    }


# Configuration fixtures
@pytest.fixture
def test_config():
    """Test configuration settings."""
    return {
        "database_url": "sqlite:///:memory:",
        "secret_key": "test_secret_key_123",
        "debug": True,
        "testing": True,
        "log_level": "DEBUG",
        "cache_enabled": False,
        "performance_monitoring": True,
    }
