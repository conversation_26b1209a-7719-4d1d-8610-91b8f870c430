# backend/tests/fixtures/test_data.py
"""
Test Data Fixtures.

This module contains sample data for testing all implemented features.
"""

import tempfile
import os
from typing import Dict, Any, List


# Phase 1: Calculations Test Data
SAMPLE_HEAT_TRACING_DATA = {
    "project_id": "test_project_001",
    "project_name": "Test Heat Tracing Project",
    "circuits": [
        {
            "circuit_id": "HT-001",
            "circuit_name": "Main Process Line",
            "pipe_diameter": 0.1,  # meters
            "pipe_length": 100.0,  # meters
            "insulation_thickness": 0.05,  # meters
            "insulation_conductivity": 0.04,  # W/m·K
            "maintain_temperature": 60.0,  # °C
            "ambient_temperature": 0.0,  # °C
            "wind_speed": 5.0,  # m/s
            "application_type": "freeze_protection",
            "voltage": 240,  # V
            "power_density": 25.0,  # W/m
        },
        {
            "circuit_id": "HT-002", 
            "circuit_name": "Secondary Line",
            "pipe_diameter": 0.05,
            "pipe_length": 50.0,
            "insulation_thickness": 0.03,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 40.0,
            "ambient_temperature": -10.0,
            "wind_speed": 3.0,
            "application_type": "process_temperature",
            "voltage": 120,
            "power_density": 15.0,
        }
    ],
    "design_parameters": {
        "safety_factor": 1.3,
        "diversity_factor": 0.8,
        "ambient_design_temperature": -20.0,
        "wind_design_speed": 10.0,
        "insulation_type": "mineral_wool",
        "cable_type": "self_regulating",
    }
}

SAMPLE_ELECTRICAL_DATA = {
    "project_id": "test_electrical_001",
    "switchboards": [
        {
            "switchboard_id": "SB-001",
            "name": "Main Distribution Board",
            "voltage": 480,
            "phases": 3,
            "frequency": 60,
            "total_capacity": 1000,  # A
            "circuits": [
                {
                    "circuit_id": "C-001",
                    "name": "Heat Tracing Panel 1",
                    "current": 50.0,
                    "power": 20000,  # W
                    "power_factor": 0.95,
                    "circuit_type": "heat_tracing",
                },
                {
                    "circuit_id": "C-002",
                    "name": "Lighting Panel",
                    "current": 30.0,
                    "power": 12000,
                    "power_factor": 0.9,
                    "circuit_type": "lighting",
                }
            ]
        }
    ]
}

# Phase 2: Import/Export Test Data
SAMPLE_CSV_DATA = """Circuit ID,Circuit Name,Pipe Diameter (m),Length (m),Maintain Temp (°C),Ambient Temp (°C),Power Density (W/m)
HT-001,Main Process Line,0.1,100.0,60.0,0.0,25.0
HT-002,Secondary Line,0.05,50.0,40.0,-10.0,15.0
HT-003,Tertiary Line,0.08,75.0,50.0,-5.0,20.0"""

SAMPLE_EXCEL_DATA = {
    "sheets": {
        "Circuits": [
            ["Circuit ID", "Circuit Name", "Pipe Diameter (m)", "Length (m)", "Maintain Temp (°C)"],
            ["HT-001", "Main Process Line", 0.1, 100.0, 60.0],
            ["HT-002", "Secondary Line", 0.05, 50.0, 40.0],
        ],
        "Parameters": [
            ["Parameter", "Value", "Unit"],
            ["Safety Factor", 1.3, ""],
            ["Diversity Factor", 0.8, ""],
            ["Design Temperature", -20.0, "°C"],
        ]
    }
}

SAMPLE_JSON_DATA = {
    "project_info": {
        "project_id": "test_import_001",
        "project_name": "Import Test Project",
        "created_date": "2024-12-19",
    },
    "circuits": [
        {
            "circuit_id": "HT-001",
            "circuit_name": "Imported Circuit 1",
            "pipe_diameter": 0.1,
            "pipe_length": 100.0,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "power_density": 25.0,
        }
    ]
}

# Phase 3: Reports Test Data
SAMPLE_REPORT_DATA = {
    "project_data": {
        "project_id": "test_report_001",
        "project_name": "Test Report Project",
        "project_number": "PRJ-2024-001",
        "client": "Test Client",
        "location": "Test Location",
        "engineer": "Test Engineer",
        "date": "2024-12-19",
    },
    "circuits_data": [
        {
            "circuit_id": "HT-001",
            "circuit_name": "Main Line",
            "pipe_diameter": 0.1,
            "pipe_length": 100.0,
            "heat_loss": 2500.0,  # W
            "power_requirement": 3250.0,  # W
            "cable_type": "Self-regulating",
            "voltage": 240,
            "current": 13.5,
        }
    ],
    "calculations_data": [
        {
            "circuit_id": "HT-001",
            "heat_loss_calculation": {
                "thermal_resistance": 0.024,  # K·m/W
                "temperature_differential": 60.0,  # °C
                "heat_loss_per_meter": 25.0,  # W/m
            },
            "power_calculation": {
                "safety_factor": 1.3,
                "design_power_per_meter": 32.5,  # W/m
                "total_power": 3250.0,  # W
            }
        }
    ]
}

# Phase 4: Standards Test Data
SAMPLE_STANDARDS_DATA = {
    "design_data": {
        "voltage": 240,
        "power_density": 25.0,
        "maintain_temperature": 60.0,
        "ambient_temperature": 0.0,
        "pipe_diameter": 0.1,
        "insulation_type": "mineral_wool",
        "application_type": "freeze_protection",
        "zone_classification": "Zone 2",
        "temperature_class": "T3",
        "protection_method": "Ex e",
        "surface_temperature": 150.0,
        "cable_type": "self_regulating",
        "installation_method": "direct_attach",
        "facility_type": "fixed",
        "location_classification": "hazardous",
        "environmental_conditions": "offshore",
        "heating_method": "impedance",
        "frequency": 60,
        "current_density": 2.5,
        "pipe_material": "carbon_steel",
        "wall_thickness": 0.005,
        "resistivity": 1.7e-8,
        "permeability": 4e-7,
        "current": 15.0,
        "impedance_per_length": 0.1,
        "length": 100.0,
        "platform_type": "fixed",
        "explosive_atmosphere_classification": "Zone 1",
    },
    "input_data": {
        "heat_loss_per_meter": 25.0,
        "circuit_length": 100.0,
        "application_type": "freeze_protection",
        "voltage": 240,
        "power_per_meter": 32.5,
        "maintain_temperature": 60.0,
        "ambient_temperature": 0.0,
        "power_density": 25.0,
        "frequency": 60,
        "resistivity": 1.7e-8,
        "permeability": 4e-7,
        "pipe_diameter": 0.1,
        "wall_thickness": 0.005,
        "current": 15.0,
        "impedance_per_length": 0.1,
        "length": 100.0,
    }
}

# API Test Data
SAMPLE_API_REQUESTS = {
    "heat_tracing_calculation": {
        "project_data": SAMPLE_HEAT_TRACING_DATA,
        "calculation_options": {
            "include_safety_factors": True,
            "use_conservative_estimates": False,
            "calculation_method": "detailed",
        }
    },
    "report_generation": {
        "project_data": SAMPLE_REPORT_DATA["project_data"],
        "circuits_data": SAMPLE_REPORT_DATA["circuits_data"],
        "calculations_data": SAMPLE_REPORT_DATA["calculations_data"],
        "design_parameters": SAMPLE_HEAT_TRACING_DATA["design_parameters"],
        "output_format": "pdf",
        "template_name": "standard_report",
        "use_cache": False,
    },
    "standards_validation": {
        "design_data": SAMPLE_STANDARDS_DATA["design_data"],
        "standard_ids": ["IEEE-515-2017", "IEC-60079-30-1"],
        "validation_options": {
            "strict_mode": True,
            "include_warnings": True,
        }
    }
}

# Error Test Cases
ERROR_TEST_CASES = {
    "invalid_heat_tracing_data": {
        "circuits": [
            {
                "circuit_id": "HT-INVALID",
                "pipe_diameter": -0.1,  # Invalid negative value
                "pipe_length": 0,  # Invalid zero length
                "maintain_temperature": -300,  # Invalid temperature
                "ambient_temperature": 500,  # Invalid temperature
                "voltage": -240,  # Invalid negative voltage
            }
        ]
    },
    "missing_required_fields": {
        "circuits": [
            {
                "circuit_id": "HT-INCOMPLETE",
                # Missing required fields
            }
        ]
    },
    "invalid_file_formats": {
        "csv_with_errors": "Invalid,CSV,Data\nMissing,Columns",
        "malformed_json": '{"invalid": json syntax}',
        "empty_excel": {"sheets": {}},
    }
}


def create_temp_file(content: str, suffix: str = ".tmp") -> str:
    """Create a temporary file with given content."""
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False)
    temp_file.write(content)
    temp_file.close()
    return temp_file.name


def cleanup_temp_file(file_path: str):
    """Clean up temporary file."""
    try:
        if os.path.exists(file_path):
            os.unlink(file_path)
    except Exception:
        pass


def get_sample_csv_file() -> str:
    """Create a temporary CSV file with sample data."""
    return create_temp_file(SAMPLE_CSV_DATA, ".csv")


def get_sample_json_file() -> str:
    """Create a temporary JSON file with sample data."""
    import json
    json_content = json.dumps(SAMPLE_JSON_DATA, indent=2)
    return create_temp_file(json_content, ".json")


def get_invalid_csv_file() -> str:
    """Create a temporary CSV file with invalid data."""
    return create_temp_file(ERROR_TEST_CASES["invalid_file_formats"]["csv_with_errors"], ".csv")


def get_malformed_json_file() -> str:
    """Create a temporary JSON file with malformed data."""
    return create_temp_file(ERROR_TEST_CASES["invalid_file_formats"]["malformed_json"], ".json")


# Mock data for external dependencies
MOCK_DATABASE_DATA = {
    "projects": [
        {
            "id": "test_project_001",
            "name": "Test Project 1",
            "status": "active",
            "created_at": "2024-12-19T10:00:00Z",
        }
    ],
    "circuits": [
        {
            "id": "HT-001",
            "project_id": "test_project_001",
            "name": "Test Circuit 1",
            "pipe_diameter": 0.1,
            "pipe_length": 100.0,
        }
    ]
}

# Performance test data
PERFORMANCE_TEST_DATA = {
    "large_circuit_dataset": {
        "circuits": [
            {
                "circuit_id": f"HT-{i:03d}",
                "circuit_name": f"Circuit {i}",
                "pipe_diameter": 0.1,
                "pipe_length": 100.0,
                "maintain_temperature": 60.0,
                "ambient_temperature": 0.0,
                "power_density": 25.0,
            }
            for i in range(1, 1001)  # 1000 circuits
        ]
    }
}
