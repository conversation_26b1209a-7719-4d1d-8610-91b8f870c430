# backend/tests/fixtures/test_fixtures.py
"""
Enhanced Test Fixtures for Phase 2 Test Infrastructure Improvements

This module provides advanced fixtures for better test data generation,
mock utilities, and test scenario management.
"""

import pytest
from unittest.mock import Mock, MagicMock
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import json
import random
import string

from core.models.enums import ElectricalNodeType, CableInstallationMethod
from core.models.project import Project
from core.models.users import User


class TestDataGenerator:
    """Advanced test data generator for various scenarios."""

    @staticmethod
    def generate_electrical_node_data(
        project_id: int,
        node_type: Optional[ElectricalNodeType] = None,
        voltage_range: tuple = (120, 480),
        power_range: tuple = (10, 1000),
        **overrides,
    ) -> Dict[str, Any]:
        """Generate realistic electrical node test data."""
        if node_type is None:
            node_type = random.choice(list(ElectricalNodeType))

        base_data = {
            "project_id": project_id,
            "name": f"Node_{random.randint(1000, 9999)}",
            "node_type": node_type.value,
            "voltage_v": random.uniform(*voltage_range),
            "power_capacity_kva": random.uniform(*power_range),
            "location_description": f"Location_{random.randint(100, 999)}",
        }

        base_data.update(overrides)
        return base_data

    @staticmethod
    def generate_component_data(
        category_id: int = 1, include_specifications: bool = True, **overrides
    ) -> Dict[str, Any]:
        """Generate realistic component test data."""
        manufacturers = ["Schneider Electric", "ABB", "Siemens", "GE", "Eaton"]
        component_types = ["Motor", "Breaker", "Transformer", "Switch", "Relay"]

        base_data = {
            "name": f"{random.choice(component_types)}_{random.randint(100, 999)}",
            "category_id": category_id,
            "manufacturer": random.choice(manufacturers),
            "model": f"{''.join(random.choices(string.ascii_uppercase, k=3))}-{random.randint(100, 999)}",
            "notes": f"Test component for {random.choice(['industrial', 'commercial', 'residential'])} use",
        }

        if include_specifications:
            specifications = {
                "voltage": f"{random.choice([120, 240, 480, 600])}V",
                "current": f"{random.randint(5, 100)}A",
                "power": f"{random.randint(1, 50)}HP",
                "efficiency": f"{random.randint(85, 98)}%",
            }
            base_data["specific_data"] = json.dumps(specifications)

        base_data.update(overrides)
        return base_data

    @staticmethod
    def generate_cable_route_data(
        project_id: int, from_node_id: int = 1, to_node_id: int = 2, **overrides
    ) -> Dict[str, Any]:
        """Generate realistic cable route test data."""
        cable_types = ["THWN", "XHHW", "USE", "RHW", "THHN"]
        installation_methods = list(CableInstallationMethod)

        base_data = {
            "project_id": project_id,
            "name": f"Route_{from_node_id}_to_{to_node_id}",
            "from_node_id": from_node_id,
            "to_node_id": to_node_id,
            "cable_component_id": random.randint(1, 100),
            "length_m": random.uniform(10, 1000),
            "number_of_runs": random.randint(1, 4),
            "installation_method": random.choice(installation_methods).value,
            "max_ambient_temp_c": random.uniform(20, 50),
            "min_ambient_temp_c": random.uniform(-20, 10),
        }

        base_data.update(overrides)
        return base_data

    @staticmethod
    def generate_boundary_test_cases(
        field_name: str, field_type: str, valid_range: Optional[tuple] = None
    ) -> List[Dict[str, Any]]:
        """Generate boundary test cases for a specific field."""
        cases = []

        if field_type == "string":
            cases.extend(
                [
                    {
                        "value": "",
                        "expected_valid": False,
                        "description": "Empty string",
                    },
                    {"value": "A", "expected_valid": False, "description": "Too short"},
                    {
                        "value": "AB",
                        "expected_valid": False,
                        "description": "Still too short",
                    },
                    {
                        "value": "ABC",
                        "expected_valid": True,
                        "description": "Minimum valid",
                    },
                    {
                        "value": "A" * 100,
                        "expected_valid": True,
                        "description": "Maximum valid",
                    },
                    {
                        "value": "A" * 101,
                        "expected_valid": False,
                        "description": "Too long",
                    },
                ]
            )

        elif field_type == "number" and valid_range:
            min_val, max_val = valid_range
            cases.extend(
                [
                    {
                        "value": min_val - 1,
                        "expected_valid": False,
                        "description": "Below minimum",
                    },
                    {
                        "value": min_val,
                        "expected_valid": True,
                        "description": "Minimum valid",
                    },
                    {
                        "value": (min_val + max_val) / 2,
                        "expected_valid": True,
                        "description": "Middle value",
                    },
                    {
                        "value": max_val,
                        "expected_valid": True,
                        "description": "Maximum valid",
                    },
                    {
                        "value": max_val + 1,
                        "expected_valid": False,
                        "description": "Above maximum",
                    },
                    {
                        "value": 0,
                        "expected_valid": min_val <= 0 <= max_val,
                        "description": "Zero value",
                    },
                    {
                        "value": -1,
                        "expected_valid": min_val <= -1,
                        "description": "Negative value",
                    },
                ]
            )

        return cases


class MockUtilities:
    """Enhanced mock utilities for testing."""

    @staticmethod
    def create_mock_electrical_node(
        node_id: int = 1, project_id: int = 1, **overrides
    ) -> Mock:
        """Create a mock electrical node with realistic data."""
        mock_node = Mock()
        mock_node.id = node_id
        mock_node.project_id = project_id
        mock_node.name = f"Mock_Node_{node_id}"
        mock_node.node_type = ElectricalNodeType.SWITCHBOARD_INCOMING
        mock_node.voltage_v = 480.0
        mock_node.power_capacity_kva = 1000.0
        mock_node.location_description = "Mock Location"
        mock_node.is_deleted = False
        mock_node.created_at = datetime.now(timezone.utc)
        mock_node.updated_at = datetime.now(timezone.utc)

        for key, value in overrides.items():
            setattr(mock_node, key, value)

        return mock_node

    @staticmethod
    def create_mock_component(
        component_id: int = 1, category_id: int = 1, **overrides
    ) -> Mock:
        """Create a mock component with realistic data."""
        mock_component = Mock()
        mock_component.id = component_id
        mock_component.category_id = category_id
        mock_component.name = f"Mock_Component_{component_id}"
        mock_component.manufacturer = "Mock Manufacturer"
        mock_component.model = f"MOCK-{component_id}"
        mock_component.specific_data = '{"test": "data"}'
        mock_component.notes = "Mock component for testing"
        mock_component.is_deleted = False
        mock_component.created_at = datetime.now(timezone.utc)
        mock_component.updated_at = datetime.now(timezone.utc)

        for key, value in overrides.items():
            setattr(mock_component, key, value)

        return mock_component

    @staticmethod
    def create_mock_service_response(
        items: List[Any], total: int = 0, page: int = 1, per_page: int = 10
    ) -> Mock:
        """Create a mock service response for paginated results."""
        if total is None:
            total = len(items)

        mock_response = Mock()
        mock_response.items = items
        mock_response.total = total
        mock_response.page = page
        mock_response.per_page = per_page
        mock_response.total_pages = (total + per_page - 1) // per_page

        # Add component-specific attributes if needed
        if hasattr(items[0] if items else None, "name"):
            mock_response.components = items

        return mock_response

    @staticmethod
    def create_mock_calculation_result(
        calculation_type: str = "cable_sizing", success: bool = True, **overrides
    ) -> Dict[str, Any]:
        """Create mock calculation results."""
        base_results = {
            "cable_sizing": {
                "recommended_conductor_size": "4/0 AWG",
                "calculated_voltage_drop_percent": 2.1,
                "ampacity_rating": 125.0,
                "meets_requirements": True,
                "calculation_details": {
                    "resistance_per_meter": 0.000164,
                    "total_resistance": 0.0246,
                    "voltage_drop_volts": 10.08,
                },
            },
            "voltage_drop": {
                "voltage_drop_volts": 8.5,
                "voltage_drop_percent": 1.77,
                "voltage_at_load": 471.5,
                "meets_standards": True,
                "calculation_details": {
                    "resistance_per_meter": 0.000328,
                    "reactance_per_meter": 0.000164,
                    "impedance_per_meter": 0.000367,
                },
            },
        }

        result = base_results.get(calculation_type, {})
        result.update(overrides)

        if not success:
            result.update(
                {
                    "meets_requirements": False,
                    "meets_standards": False,
                    "error": "Calculation failed",
                }
            )

        return result


# Enhanced fixtures using the utilities above


@pytest.fixture
def test_data_generator():
    """Provide test data generator utility."""
    return TestDataGenerator()


@pytest.fixture
def mock_utilities():
    """Provide mock utilities."""
    return MockUtilities()


@pytest.fixture
def sample_electrical_nodes(test_data_generator, sample_project):
    """Generate multiple sample electrical nodes."""
    nodes = []
    for i in range(5):
        node_data = test_data_generator.generate_electrical_node_data(
            project_id=sample_project.id, name=f"Test_Node_{i + 1}"
        )
        nodes.append(node_data)
    return nodes


@pytest.fixture
def sample_components(test_data_generator):
    """Generate multiple sample components."""
    components = []
    for i in range(5):
        component_data = test_data_generator.generate_component_data(
            category_id=1, name=f"Test_Component_{i + 1}"
        )
        components.append(component_data)
    return components


@pytest.fixture
def boundary_test_cases(test_data_generator):
    """Generate boundary test cases for common fields."""
    return {
        "name": test_data_generator.generate_boundary_test_cases("name", "string"),
        "voltage": test_data_generator.generate_boundary_test_cases(
            "voltage", "number", (0.1, 50000)
        ),
        "power": test_data_generator.generate_boundary_test_cases(
            "power", "number", (0.001, 10000)
        ),
    }


@pytest.fixture
def mock_electrical_nodes(mock_utilities):
    """Create multiple mock electrical nodes."""
    return [
        mock_utilities.create_mock_electrical_node(node_id=i, name=f"Node_{i}")
        for i in range(1, 6)
    ]


@pytest.fixture
def mock_components(mock_utilities):
    """Create multiple mock components."""
    return [
        mock_utilities.create_mock_component(component_id=i, name=f"Component_{i}")
        for i in range(1, 6)
    ]


@pytest.fixture
def performance_monitor():
    """Enhanced performance monitoring fixture."""
    import time
    import psutil
    import os

    class PerformanceMonitor:
        def __init__(self):
            self.start_time = None
            self.end_time = None
            self.start_memory = None
            self.end_memory = None
            self.process = psutil.Process(os.getpid())

        def start(self):
            self.start_time = time.time()
            self.start_memory = self.process.memory_info().rss

        def stop(self):
            self.end_time = time.time()
            self.end_memory = self.process.memory_info().rss

        @property
        def execution_time(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None

        @property
        def memory_usage(self):
            if self.start_memory and self.end_memory:
                return self.end_memory - self.start_memory
            return None

        def get_metrics(self):
            return {
                "execution_time": self.execution_time,
                "memory_usage": self.memory_usage,
                "peak_memory": self.end_memory if self.end_memory else None,
            }

    return PerformanceMonitor()
