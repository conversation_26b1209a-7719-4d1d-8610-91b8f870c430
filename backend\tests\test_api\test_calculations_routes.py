# backend/tests/test_api/test_calculations_routes.py
"""
Tests for Calculations API Routes.

Tests the REST API endpoints for calculation operations.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from main import app
from tests.fixtures.test_data import SAMPLE_HEAT_TRACING_DATA, SAMPLE_API_REQUESTS


class TestCalculationsRoutes:
    """Test suite for calculations API routes."""

    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        self.sample_request = SAMPLE_API_REQUESTS["heat_tracing_calculation"]

    @patch('api.dependencies.get_current_user')
    def test_calculate_heat_tracing_valid_request(self, mock_get_user):
        """Test heat tracing calculation with valid request."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            json=self.sample_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "circuit_results" in data
        assert "system_summary" in data

    @patch('api.dependencies.get_current_user')
    def test_calculate_heat_tracing_invalid_request(self, mock_get_user):
        """Test heat tracing calculation with invalid request."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        invalid_request = {
            "project_data": {},  # Empty project data
            "calculation_options": {}
        }
        
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            json=invalid_request
        )
        
        assert response.status_code == 400

    @patch('api.dependencies.get_current_user')
    def test_calculate_heat_tracing_missing_auth(self, mock_get_user):
        """Test heat tracing calculation without authentication."""
        mock_get_user.side_effect = Exception("Authentication required")
        
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            json=self.sample_request
        )
        
        assert response.status_code == 401

    @patch('api.dependencies.get_current_user')
    def test_calculate_electrical_load_valid_request(self, mock_get_user):
        """Test electrical load calculation with valid request."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        electrical_request = {
            "switchboards": [
                {
                    "switchboard_id": "SB-001",
                    "name": "Main Distribution",
                    "voltage": 480,
                    "circuits": [
                        {
                            "circuit_id": "C-001",
                            "name": "Heat Tracing Panel",
                            "current": 50.0,
                            "power": 20000
                        }
                    ]
                }
            ],
            "calculation_options": {}
        }
        
        response = self.client.post(
            "/api/v1/calculations/electrical-load",
            json=electrical_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @patch('api.dependencies.get_current_user')
    def test_calculate_power_requirements_valid_request(self, mock_get_user):
        """Test power requirements calculation with valid request."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        power_request = {
            "circuit_data": {
                "heat_loss_per_meter": 25.0,
                "circuit_length": 100.0,
                "application_type": "freeze_protection"
            },
            "calculation_options": {}
        }
        
        response = self.client.post(
            "/api/v1/calculations/power-requirements",
            json=power_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "power_calculation" in data

    @patch('api.dependencies.get_current_user')
    def test_calculate_cable_selection_valid_request(self, mock_get_user):
        """Test cable selection calculation with valid request."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        cable_request = {
            "selection_data": {
                "power_per_meter": 25.0,
                "voltage": 240,
                "circuit_length": 100.0,
                "application_type": "freeze_protection"
            },
            "calculation_options": {}
        }
        
        response = self.client.post(
            "/api/v1/calculations/cable-selection",
            json=cable_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "cable_selection" in data

    @patch('api.dependencies.get_current_user')
    def test_get_calculation_history(self, mock_get_user):
        """Test getting calculation history."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/calculations/history")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "history" in data

    @patch('api.dependencies.get_current_user')
    def test_get_calculation_history_filtered(self, mock_get_user):
        """Test getting filtered calculation history."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get(
            "/api/v1/calculations/history",
            params={
                "project_id": "test_project_001",
                "calculation_type": "heat_tracing",
                "limit": 10
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @patch('api.dependencies.get_current_user')
    def test_validate_calculation_input(self, mock_get_user):
        """Test calculation input validation."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        validation_request = {
            "calculation_type": "heat_tracing",
            "input_data": self.sample_request["project_data"]
        }
        
        response = self.client.post(
            "/api/v1/calculations/validate",
            json=validation_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "validation_result" in data

    @patch('api.dependencies.get_current_user')
    def test_get_calculation_templates(self, mock_get_user):
        """Test getting calculation templates."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/calculations/templates")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "templates" in data

    @patch('api.dependencies.get_current_user')
    def test_get_calculation_template_specific(self, mock_get_user):
        """Test getting a specific calculation template."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/calculations/templates/heat_tracing")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "template" in data

    @patch('api.dependencies.get_current_user')
    def test_calculations_health_check(self, mock_get_user):
        """Test calculations service health check."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/calculations/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "health_status" in data

    def test_calculations_routes_without_auth(self):
        """Test calculations routes without authentication."""
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            json=self.sample_request
        )
        
        # Should require authentication
        assert response.status_code in [401, 403]

    @patch('api.dependencies.get_current_user')
    def test_calculate_heat_tracing_large_dataset(self, mock_get_user):
        """Test heat tracing calculation with large dataset."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        # Create large dataset
        large_request = self.sample_request.copy()
        large_circuits = []
        for i in range(100):
            circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0].copy()
            circuit["circuit_id"] = f"HT-{i:03d}"
            large_circuits.append(circuit)
        
        large_request["project_data"]["circuits"] = large_circuits
        
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            json=large_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["circuit_results"]) == 100

    @patch('api.dependencies.get_current_user')
    def test_calculate_with_performance_monitoring(self, mock_get_user):
        """Test calculation with performance monitoring."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        request_with_monitoring = self.sample_request.copy()
        request_with_monitoring["calculation_options"]["collect_performance_metrics"] = True
        
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            json=request_with_monitoring
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "performance_metrics" in data

    @patch('api.dependencies.get_current_user')
    def test_error_handling_invalid_json(self, mock_get_user):
        """Test error handling with invalid JSON."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # Unprocessable Entity

    @patch('api.dependencies.get_current_user')
    def test_error_handling_missing_fields(self, mock_get_user):
        """Test error handling with missing required fields."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        incomplete_request = {
            "project_data": {
                "circuits": []  # Empty circuits
            }
        }
        
        response = self.client.post(
            "/api/v1/calculations/heat-tracing",
            json=incomplete_request
        )
        
        assert response.status_code == 400
