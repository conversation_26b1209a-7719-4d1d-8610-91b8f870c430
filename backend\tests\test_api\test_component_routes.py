# backend/tests/test_api/test_component_routes.py
"""
Test suite for component API routes.

This module tests all component-related API endpoints including:
- Component CRUD operations
- Component category management
- Search and filtering functionality
- Error handling and validation
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

pytestmark = [
    pytest.mark.unit,
    pytest.mark.api,
    pytest.mark.integration,
    pytest.mark.component,
]


class TestComponentRoutes:
    """Test component API endpoints."""

    def test_create_component_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test creating a component successfully."""
        component_data = {
            "name": "Test Motor",
            "model": "ABC-123",
            "manufacturer": "Test Manufacturer",
            "category_id": 1,
            "description": "A test motor component",
            "specifications": {
                "power_rating": "10 HP",
                "voltage": "480V",
                "current": "15A",
                "efficiency": "95%"
            },
            "unit_price": 1500.00,
            "availability_status": "in_stock",
            "minimum_stock_level": 5,
            "current_stock_level": 25,
        }

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock the created component
            mock_component = MagicMock()
            mock_component.id = 1
            mock_component.name = component_data["name"]
            mock_component.model = component_data["model"]
            mock_service_instance.create_component.return_value = mock_component

            response = client.post("/api/v1/components/", json=component_data)

            assert response.status_code == 201
            data = response.json()
            assert data["name"] == component_data["name"]
            assert data["model"] == component_data["model"]
            mock_service_instance.create_component.assert_called_once()

    def test_create_component_duplicate_error(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test creating a component with duplicate name."""
        component_data = {
            "name": "Duplicate Motor",
            "model": "DUP-123",
            "manufacturer": "Test Manufacturer",
            "category_id": 1,
        }

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock duplicate entry error
            from core.errors.exceptions import DuplicateEntryError
            mock_service_instance.create_component.side_effect = DuplicateEntryError(
                "Component with this name already exists"
            )

            response = client.post("/api/v1/components/", json=component_data)

            assert response.status_code == 409
            assert "already exists" in response.json()["detail"].lower()

    def test_create_component_validation_error(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test creating component with invalid data."""
        invalid_data = {
            "name": "",  # Empty name should fail validation
            "model": "TEST-123",
        }

        response = client.post("/api/v1/components/", json=invalid_data)
        assert response.status_code == 422

    def test_get_component_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving a component by ID."""
        component_id = 1

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock the retrieved component
            mock_component = MagicMock()
            mock_component.id = component_id
            mock_component.name = "Test Component"
            mock_component.model = "TEST-123"
            mock_service_instance.get_component_details.return_value = mock_component

            response = client.get(f"/api/v1/components/{component_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["id"] == component_id
            mock_service_instance.get_component_details.assert_called_once_with(component_id)

    def test_get_component_not_found(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving non-existent component."""
        component_id = 999

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock component not found error
            from core.errors.exceptions import ComponentNotFoundError
            mock_service_instance.get_component_details.side_effect = ComponentNotFoundError(
                f"Component {component_id} not found"
            )

            response = client.get(f"/api/v1/components/{component_id}")

            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()

    def test_update_component_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test updating a component."""
        component_id = 1
        update_data = {
            "name": "Updated Component Name",
            "unit_price": 2000.00,
        }

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock updated component
            mock_updated = MagicMock()
            mock_updated.id = component_id
            mock_updated.name = update_data["name"]
            mock_updated.unit_price = update_data["unit_price"]
            mock_service_instance.update_component.return_value = mock_updated

            response = client.put(f"/api/v1/components/{component_id}", json=update_data)

            assert response.status_code == 200
            data = response.json()
            assert data["name"] == update_data["name"]
            mock_service_instance.update_component.assert_called_once()

    def test_delete_component_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test soft deleting a component."""
        component_id = 1

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance

            response = client.delete(f"/api/v1/components/{component_id}")

            assert response.status_code == 204
            mock_service_instance.delete_component.assert_called_once_with(component_id)

    def test_list_components_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test listing components with pagination."""
        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock component list response
            mock_response = MagicMock()
            mock_response.components = [
                MagicMock(id=1, name="Component 1"),
                MagicMock(id=2, name="Component 2"),
            ]
            mock_response.total = 2
            mock_response.page = 1
            mock_response.per_page = 10
            mock_response.total_pages = 1
            mock_service_instance.get_components_list.return_value = mock_response

            response = client.get("/api/v1/components/?page=1&per_page=10")

            assert response.status_code == 200
            data = response.json()
            assert "components" in data
            assert data["total"] == 2
            assert len(data["components"]) == 2

    def test_list_components_with_filters(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test listing components with search and category filters."""
        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock filtered component list
            mock_response = MagicMock()
            mock_response.components = [MagicMock(id=1, name="Motor ABC")]
            mock_response.total = 1
            mock_service_instance.get_components_list.return_value = mock_response

            response = client.get(
                "/api/v1/components/?page=1&per_page=10&category_id=1&search=motor&manufacturer=TestCorp"
            )

            assert response.status_code == 200
            data = response.json()
            assert data["total"] == 1
            mock_service_instance.get_components_list.assert_called_once()
            
            # Verify the service was called with correct parameters
            call_args = mock_service_instance.get_components_list.call_args
            assert call_args.kwargs["category_id"] == 1
            assert call_args.kwargs["search_term"] == "motor"
            assert call_args.kwargs["manufacturer"] == "TestCorp"


class TestComponentCategoryRoutes:
    """Test component category API endpoints."""

    def test_create_category_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test creating a component category successfully."""
        category_data = {
            "name": "Motors",
            "description": "Electric motors and drives",
            "parent_id": None,
        }

        with patch("api.v1.component_routes.get_category_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock the created category
            mock_category = MagicMock()
            mock_category.id = 1
            mock_category.name = category_data["name"]
            mock_service_instance.create_category.return_value = mock_category

            response = client.post("/api/v1/component-categories/", json=category_data)

            assert response.status_code == 201
            data = response.json()
            assert data["name"] == category_data["name"]
            mock_service_instance.create_category.assert_called_once()

    def test_get_category_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving a category by ID."""
        category_id = 1

        with patch("api.v1.component_routes.get_category_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock the retrieved category
            mock_category = MagicMock()
            mock_category.id = category_id
            mock_category.name = "Test Category"
            mock_service_instance.get_category_details.return_value = mock_category

            response = client.get(f"/api/v1/component-categories/{category_id}")

            assert response.status_code == 200
            data = response.json()
            assert data["id"] == category_id
            mock_service_instance.get_category_details.assert_called_once_with(category_id)

    def test_list_categories_success(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test listing component categories."""
        with patch("api.v1.component_routes.get_category_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock category list response
            mock_response = MagicMock()
            mock_response.categories = [
                MagicMock(id=1, name="Motors"),
                MagicMock(id=2, name="Switches"),
            ]
            mock_response.total = 2
            mock_service_instance.get_categories_list.return_value = mock_response

            response = client.get("/api/v1/component-categories/?page=1&per_page=10")

            assert response.status_code == 200
            data = response.json()
            assert "categories" in data
            assert data["total"] == 2
            assert len(data["categories"]) == 2
