# backend/tests/test_api/test_component_routes_error_handling.py
"""
Error Handling and Edge Cases Tests for Component API Routes

This module provides comprehensive error handling testing for component API endpoints,
focusing on database errors, service failures, and exceptional conditions.
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from core.errors.exceptions import (
    ComponentNotFoundError,
    DuplicateEntryError,
    DataValidationError,
    DatabaseError,
)

pytestmark = [
    pytest.mark.unit,
    pytest.mark.api,
    pytest.mark.component,
    pytest.mark.error_handling,
]


class TestComponentErrorHandling:
    """Test error handling scenarios for component operations."""

    def test_create_component_database_error(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component creation with database errors."""
        component_data = {
            "name": "Test Component",
            "category_id": 1,
            "manufacturer": "Test Manufacturer",
        }

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate database error
            mock_service_instance.create_component.side_effect = DatabaseError(
                "Database connection failed"
            )

            response = client.post("/api/v1/components/", json=component_data)
            assert response.status_code == 500
            assert "database" in response.json()["detail"].lower()

    def test_create_component_integrity_constraint_violation(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component creation with integrity constraint violations."""
        component_data = {
            "name": "Duplicate Component",
            "category_id": 999,  # Non-existent category
            "manufacturer": "Test Manufacturer",
        }

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate foreign key constraint violation
            mock_service_instance.create_component.side_effect = IntegrityError(
                "FOREIGN KEY constraint failed", None, None
            )

            response = client.post("/api/v1/components/", json=component_data)
            assert response.status_code == 400

    def test_create_component_malformed_json_data(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component creation with malformed specific_data JSON."""
        component_data = {
            "name": "Component with Bad JSON",
            "category_id": 1,
            "specific_data": "invalid json {missing quotes}",  # Malformed JSON
        }

        response = client.post("/api/v1/components/", json=component_data)
        assert response.status_code == 422

    def test_create_component_extremely_large_payload(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component creation with extremely large payload."""
        # Create a very large notes field
        large_notes = "A" * 10000  # 10KB of text

        component_data = {
            "name": "Component with Large Data",
            "category_id": 1,
            "notes": large_notes,
        }

        response = client.post("/api/v1/components/", json=component_data)
        # Should either succeed or fail with 422 if field length validation exists
        assert response.status_code in [201, 422]

    def test_get_component_service_unavailable(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component retrieval when service is unavailable."""
        component_id = 1

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate service unavailable
            mock_service_instance.get_component_details.side_effect = Exception(
                "Service temporarily unavailable"
            )

            response = client.get(f"/api/v1/components/{component_id}")
            assert response.status_code == 500

    def test_update_component_concurrent_modification(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component update with concurrent modification scenario."""
        component_id = 1
        update_data = {"name": "Updated Component Name"}

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate concurrent modification error
            mock_service_instance.update_component.side_effect = DataValidationError(
                "Component was modified by another user"
            )

            response = client.put(f"/api/v1/components/{component_id}", json=update_data)
            assert response.status_code == 400
            assert "modified" in response.json()["detail"].lower()

    def test_delete_component_with_dependencies(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component deletion when component has dependencies."""
        component_id = 1

        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate dependency constraint violation
            mock_service_instance.delete_component.side_effect = IntegrityError(
                "Cannot delete component: referenced by other entities", None, None
            )

            response = client.delete(f"/api/v1/components/{component_id}")
            assert response.status_code == 400

    def test_list_components_database_timeout(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component listing with database timeout."""
        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate database timeout
            mock_service_instance.get_components_list.side_effect = SQLAlchemyError(
                "Database query timeout"
            )

            response = client.get("/api/v1/components/?page=1&per_page=10")
            assert response.status_code == 500

    def test_search_components_with_sql_injection_attempt(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component search with potential SQL injection attempts."""
        sql_injection_attempts = [
            "'; DROP TABLE components; --",
            "1' OR '1'='1",
            "UNION SELECT * FROM users",
            "<script>alert('xss')</script>",
        ]

        for injection_attempt in sql_injection_attempts:
            with patch("api.v1.component_routes.get_component_service") as mock_service:
                mock_service_instance = MagicMock()
                mock_service.return_value = mock_service_instance
                
                # Mock safe response (service should handle sanitization)
                mock_response = MagicMock()
                mock_response.components = []
                mock_response.total = 0
                mock_service_instance.get_components_list.return_value = mock_response

                response = client.get(
                    f"/api/v1/components/?search={injection_attempt}"
                )
                # Should not crash and return safe response
                assert response.status_code == 200
                data = response.json()
                assert data["total"] == 0


class TestComponentCategoryErrorHandling:
    """Test error handling scenarios for component category operations."""

    def test_create_category_circular_dependency(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test category creation that would create circular dependency."""
        category_data = {
            "name": "Child Category",
            "parent_id": 1,  # This would create a circular reference
        }

        with patch("api.v1.component_routes.get_category_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate circular dependency error
            mock_service_instance.create_category.side_effect = DataValidationError(
                "Circular dependency detected in category hierarchy"
            )

            response = client.post("/api/v1/component-categories/", json=category_data)
            assert response.status_code == 400
            assert "circular" in response.json()["detail"].lower()

    def test_delete_category_with_subcategories(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test category deletion when category has subcategories."""
        category_id = 1

        with patch("api.v1.component_routes.get_category_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate constraint violation due to subcategories
            mock_service_instance.delete_category.side_effect = IntegrityError(
                "Cannot delete category: has subcategories", None, None
            )

            response = client.delete(f"/api/v1/component-categories/{category_id}")
            assert response.status_code == 400

    def test_update_category_invalid_parent_hierarchy(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test category update with invalid parent hierarchy."""
        category_id = 1
        update_data = {"parent_id": 999}  # Non-existent parent

        with patch("api.v1.component_routes.get_category_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Simulate invalid parent error
            mock_service_instance.update_category.side_effect = DataValidationError(
                "Parent category does not exist"
            )

            response = client.put(
                f"/api/v1/component-categories/{category_id}", json=update_data
            )
            assert response.status_code == 400


class TestComponentValidationEdgeCases:
    """Test validation edge cases for component operations."""

    def test_component_name_unicode_characters(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component creation with unicode characters in name."""
        unicode_names = [
            "Компонент",  # Cyrillic
            "コンポーネント",  # Japanese
            "Composant émetteur",  # French with accents
            "Componente ñoño",  # Spanish with ñ
            "🔌 Electric Component",  # With emoji
        ]

        for unicode_name in unicode_names:
            with patch("api.v1.component_routes.get_component_service") as mock_service:
                mock_service_instance = MagicMock()
                mock_service.return_value = mock_service_instance
                
                mock_component = MagicMock()
                mock_component.id = 1
                mock_component.name = unicode_name
                mock_service_instance.create_component.return_value = mock_component

                component_data = {
                    "name": unicode_name,
                    "category_id": 1,
                }

                response = client.post("/api/v1/components/", json=component_data)
                # Should handle unicode properly
                assert response.status_code == 201

    def test_component_specific_data_edge_cases(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component creation with edge cases in specific_data field."""
        edge_cases = [
            '{"nested": {"deeply": {"nested": "value"}}}',  # Deep nesting
            '{"array": [1, 2, 3, {"nested": "in_array"}]}',  # Arrays with objects
            '{"unicode": "测试数据"}',  # Unicode in JSON
            '{"empty_string": "", "null_value": null}',  # Edge values
            '{}',  # Empty JSON object
        ]

        for specific_data in edge_cases:
            with patch("api.v1.component_routes.get_component_service") as mock_service:
                mock_service_instance = MagicMock()
                mock_service.return_value = mock_service_instance
                
                mock_component = MagicMock()
                mock_component.id = 1
                mock_component.specific_data = specific_data
                mock_service_instance.create_component.return_value = mock_component

                component_data = {
                    "name": "Test Component",
                    "category_id": 1,
                    "specific_data": specific_data,
                }

                response = client.post("/api/v1/components/", json=component_data)
                assert response.status_code == 201
