# backend/tests/test_api/test_electrical_routes_edge_cases.py
"""
Edge Cases and Error Handling Tests for Electrical API Routes

This module provides comprehensive edge case testing for electrical API endpoints,
focusing on boundary conditions, error scenarios, and data validation edge cases.
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from core.models.project import Project
from core.models.enums import ElectricalNodeType

pytestmark = [
    pytest.mark.unit,
    pytest.mark.api,
    pytest.mark.electrical,
    pytest.mark.edge_cases,
]


class TestElectricalNodeEdgeCases:
    """Test edge cases for electrical node operations."""

    def test_create_electrical_node_boundary_values(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test electrical node creation with boundary values."""
        boundary_test_cases = [
            {
                "name": "Min Voltage Node",
                "voltage_v": 0.1,  # Minimum positive voltage
                "power_capacity_kva": 0.001,  # Minimum power
                "expected_status": 201,
            },
            {
                "name": "Max Voltage Node", 
                "voltage_v": 50000.0,  # High voltage
                "power_capacity_kva": 10000.0,  # High power
                "expected_status": 201,
            },
            {
                "name": "Zero Voltage Node",
                "voltage_v": 0.0,  # Zero voltage (should fail)
                "power_capacity_kva": 100.0,
                "expected_status": 422,
            },
            {
                "name": "Negative Voltage Node",
                "voltage_v": -240.0,  # Negative voltage (should fail)
                "power_capacity_kva": 100.0,
                "expected_status": 422,
            },
        ]

        for test_case in boundary_test_cases:
            with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
                mock_instance = MagicMock()
                mock_repo.return_value = mock_instance

                if test_case["expected_status"] == 201:
                    # Mock successful creation
                    mock_node = MagicMock()
                    mock_node.id = 1
                    mock_node.name = test_case["name"]
                    mock_instance.create.return_value = mock_node

                node_data = {
                    "project_id": sample_project.id,
                    "name": test_case["name"],
                    "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                    "voltage_v": test_case["voltage_v"],
                    "power_capacity_kva": test_case["power_capacity_kva"],
                }

                response = client.post("/api/v1/electrical/nodes", json=node_data)
                assert response.status_code == test_case["expected_status"]

    def test_create_electrical_node_extreme_string_lengths(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test electrical node creation with extreme string lengths."""
        string_test_cases = [
            {
                "name": "AB",  # Too short (min 3 chars)
                "expected_status": 422,
            },
            {
                "name": "A" * 101,  # Too long (max 100 chars)
                "expected_status": 422,
            },
            {
                "name": "Valid Node Name",  # Valid length
                "expected_status": 201,
            },
            {
                "name": "A" * 100,  # Exactly max length
                "expected_status": 201,
            },
        ]

        for test_case in string_test_cases:
            with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
                mock_instance = MagicMock()
                mock_repo.return_value = mock_instance

                if test_case["expected_status"] == 201:
                    mock_node = MagicMock()
                    mock_node.id = 1
                    mock_node.name = test_case["name"]
                    mock_instance.create.return_value = mock_node

                node_data = {
                    "project_id": sample_project.id,
                    "name": test_case["name"],
                    "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                    "voltage_v": 480.0,
                }

                response = client.post("/api/v1/electrical/nodes", json=node_data)
                assert response.status_code == test_case["expected_status"]

    def test_create_electrical_node_invalid_enum_values(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test electrical node creation with invalid enum values."""
        invalid_enum_cases = [
            "INVALID_NODE_TYPE",
            "switchboard_incoming",  # Wrong case
            "SWITCHBOARD INCOMING",  # Spaces instead of underscore
            "",  # Empty string
            None,  # Null value
            123,  # Number instead of string
        ]

        for invalid_enum in invalid_enum_cases:
            node_data = {
                "project_id": sample_project.id,
                "name": "Test Node",
                "node_type": invalid_enum,
                "voltage_v": 480.0,
            }

            response = client.post("/api/v1/electrical/nodes", json=node_data)
            assert response.status_code == 422

    def test_get_electrical_node_invalid_ids(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test retrieving electrical nodes with invalid IDs."""
        invalid_id_cases = [
            0,  # Zero ID
            -1,  # Negative ID
            999999,  # Non-existent large ID
            "abc",  # String instead of int
            None,  # Null value
        ]

        for invalid_id in invalid_id_cases:
            if isinstance(invalid_id, str) or invalid_id is None:
                # These will cause 422 (validation error) or 404
                response = client.get(f"/api/v1/electrical/nodes/{invalid_id}")
                assert response.status_code in [404, 422]
            else:
                with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
                    mock_instance = MagicMock()
                    mock_repo.return_value = mock_instance
                    mock_instance.get_by_id.return_value = None

                    response = client.get(f"/api/v1/electrical/nodes/{invalid_id}")
                    assert response.status_code == 404

    def test_list_electrical_nodes_pagination_edge_cases(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test electrical node listing with pagination edge cases."""
        pagination_cases = [
            {"skip": -1, "limit": 10, "expected_status": 422},  # Negative skip
            {"skip": 0, "limit": -1, "expected_status": 422},   # Negative limit
            {"skip": 0, "limit": 0, "expected_status": 422},    # Zero limit
            {"skip": 0, "limit": 1001, "expected_status": 422}, # Limit too high
            {"skip": 999999, "limit": 10, "expected_status": 200}, # Large skip (valid)
        ]

        for case in pagination_cases:
            with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
                mock_instance = MagicMock()
                mock_repo.return_value = mock_instance
                mock_instance.get_by_project_id.return_value = []
                mock_instance.count_by_project.return_value = 0

                response = client.get(
                    f"/api/v1/electrical/nodes?project_id={sample_project.id}"
                    f"&skip={case['skip']}&limit={case['limit']}"
                )
                assert response.status_code == case["expected_status"]


class TestCableRouteEdgeCases:
    """Test edge cases for cable route operations."""

    def test_create_cable_route_same_nodes(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test cable route creation with same source and destination nodes."""
        route_data = {
            "project_id": sample_project.id,
            "name": "Invalid Route",
            "source_node_id": 1,
            "destination_node_id": 1,  # Same as source
            "cable_type": "THWN",
            "length_meters": 150.0,
        }

        response = client.post("/api/v1/electrical/cable-routes", json=route_data)
        assert response.status_code == 422

    def test_create_cable_route_extreme_lengths(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test cable route creation with extreme length values."""
        length_cases = [
            {"length": 0.0, "expected_status": 422},      # Zero length
            {"length": -100.0, "expected_status": 422},   # Negative length
            {"length": 0.001, "expected_status": 201},    # Very small positive
            {"length": 50000.0, "expected_status": 201},  # Very large length
        ]

        for case in length_cases:
            with patch("api.v1.electrical_routes.CableRouteRepository") as mock_repo:
                mock_instance = MagicMock()
                mock_repo.return_value = mock_instance

                if case["expected_status"] == 201:
                    mock_route = MagicMock()
                    mock_route.id = 1
                    mock_route.name = "Test Route"
                    mock_instance.create.return_value = mock_route

                route_data = {
                    "project_id": sample_project.id,
                    "name": "Test Route",
                    "source_node_id": 1,
                    "destination_node_id": 2,
                    "cable_type": "THWN",
                    "length_meters": case["length"],
                }

                response = client.post("/api/v1/electrical/cable-routes", json=route_data)
                assert response.status_code == case["expected_status"]


class TestElectricalCalculationEdgeCases:
    """Test edge cases for electrical calculations."""

    def test_cable_sizing_calculation_extreme_values(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test cable sizing calculation with extreme input values."""
        extreme_cases = [
            {
                "load_current_amps": 0.001,  # Very small current
                "cable_length_meters": 0.1,  # Very short cable
                "expected_status": 200,
            },
            {
                "load_current_amps": 10000.0,  # Very high current
                "cable_length_meters": 10000.0,  # Very long cable
                "expected_status": 200,
            },
            {
                "load_current_amps": -100.0,  # Negative current (invalid)
                "cable_length_meters": 150.0,
                "expected_status": 422,
            },
        ]

        for case in extreme_cases:
            with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
                mock_instance = MagicMock()
                mock_service.return_value = mock_instance

                if case["expected_status"] == 200:
                    mock_result = {
                        "recommended_conductor_size": "4/0 AWG",
                        "meets_requirements": True,
                    }
                    mock_instance.calculate_cable_sizing.return_value = mock_result

                calc_data = {
                    "load_current_amps": case["load_current_amps"],
                    "cable_length_meters": case["cable_length_meters"],
                    "voltage_level": 480.0,
                    "installation_method": "conduit",
                    "ambient_temperature_c": 30.0,
                    "conductor_material": "copper",
                }

                response = client.post(
                    "/api/v1/electrical/calculations/cable-sizing", json=calc_data
                )
                assert response.status_code == case["expected_status"]
