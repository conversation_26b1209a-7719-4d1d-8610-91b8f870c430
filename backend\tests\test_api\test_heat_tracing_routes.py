# backend/tests/test_api/test_heat_tracing_routes.py
"""
Tests for Heat Tracing API Routes

This module contains comprehensive tests for all heat tracing API endpoints including:
- CRUD operation endpoint tests
- Request/response validation tests
- Error handling tests
- Authentication and authorization tests
"""

import json
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

# Mark all tests in this file
pytestmark = [
    pytest.mark.unit,
    pytest.mark.api,
    pytest.mark.integration,
    pytest.mark.heat_tracing,
]

from core.errors.exceptions import (
    DatabaseError,
    DataValidationError,
    DuplicateEntryError,
    NotFoundError,
)
from core.schemas.heat_tracing_schemas import (
    PipeReadSchema,
    PipeListResponseSchema,
    VesselReadSchema,
    VesselListResponseSchema,
    HeatLossCalculationResultSchema,
    StandardsValidationResultSchema,
    HeatTracingDesignResultSchema,
)


class TestPipeEndpoints:
    """Test pipe-related API endpoints."""

    def test_create_pipe_success(self, client: TestClient):
        """Test successful pipe creation."""
        pipe_data = {
            "name": "Test Pipe 001",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "nominal_diameter_mm": 100.0,
            "wall_thickness_mm": 5.0,
            "outer_diameter_mm": 110.0,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
            "fluid_type": "Process Water",
            "line_tag": "L-001",
        }

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            # Setup mock service
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response with all required fields
            mock_pipe = Mock()
            mock_pipe.id = 1
            mock_pipe.name = "Test Pipe 001"
            mock_pipe.project_id = 1
            mock_pipe.pipe_material_id = 10
            mock_pipe.insulation_material_id = 15
            mock_pipe.length_m = 50.0
            mock_pipe.insulation_thickness_mm = 50.0
            # Add all BaseSoftDeleteSchema fields:
            mock_pipe.created_at = "2024-01-15T10:30:00Z"
            mock_pipe.updated_at = "2024-01-15T10:30:00Z"
            mock_pipe.is_deleted = False
            mock_pipe.deleted_at = None
            mock_pipe.deleted_by_user_id = None
            # Add other required fields with defaults
            mock_pipe.nominal_diameter_mm = 100.0
            mock_pipe.wall_thickness_mm = 5.0
            mock_pipe.outer_diameter_mm = 110.0
            mock_pipe.fluid_type = "Process Water"
            mock_pipe.specific_heat_capacity_jkgc = 4180.0
            mock_pipe.viscosity_cp = 1.0
            mock_pipe.freezing_point_c = 0.0
            mock_pipe.safety_margin_percent = 20.0
            mock_pipe.pid = "P&ID-001"
            mock_pipe.line_tag = "L-001"
            mock_pipe.from_location = "Tank A"
            mock_pipe.to_location = "Tank B"
            mock_pipe.valve_count = 3
            mock_pipe.support_count = 5
            mock_pipe.calculated_heat_loss_wm = 25.5
            mock_pipe.required_heat_output_wm = 30.6
            mock_pipe.imported_data_revision_id = None

            mock_service_instance.create_pipe.return_value = mock_pipe

            # Make request
            response = client.post("/api/v1/heat-tracing/pipes", json=pipe_data)

            # Assertions
            assert response.status_code == 201
            response_data = response.json()
            assert response_data["name"] == "Test Pipe 001"
            assert response_data["id"] == 1

            # Verify service was called
            mock_service_instance.create_pipe.assert_called_once()

    def test_create_pipe_validation_error(self, client: TestClient):
        """Test pipe creation with validation error."""
        invalid_pipe_data = {
            "name": "",  # Empty name
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
        }

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock to raise validation error
            mock_service_instance.create_pipe.side_effect = DataValidationError(
                details={"name": "Pipe name cannot be empty"}
            )

            # Make request
            response = client.post("/api/v1/heat-tracing/pipes", json=invalid_pipe_data)

            # Assertions
            assert response.status_code == 400
            assert "validation" in response.json()["detail"].lower()

    def test_create_pipe_duplicate_error(self, client: TestClient):
        """Test pipe creation with duplicate name."""
        pipe_data = {
            "name": "Duplicate Pipe",
            "project_id": 1,
            "pipe_material_id": 10,
            "insulation_material_id": 15,
            "length_m": 50.0,
            "insulation_thickness_mm": 50.0,
        }

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock to raise duplicate error
            mock_service_instance.create_pipe.side_effect = DuplicateEntryError(
                message="A pipe with this name already exists in this project."
            )

            # Make request
            response = client.post("/api/v1/heat-tracing/pipes", json=pipe_data)

            # Assertions
            assert response.status_code == 409
            assert "already exists" in response.json()["detail"]

    def test_get_pipe_success(self, client: TestClient):
        """Test successful pipe retrieval."""
        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response with all required fields
            mock_pipe = Mock()
            mock_pipe.id = 1
            mock_pipe.name = "Test Pipe 001"
            mock_pipe.project_id = 1
            mock_pipe.pipe_material_id = 10
            mock_pipe.insulation_material_id = 15
            mock_pipe.length_m = 50.0
            mock_pipe.insulation_thickness_mm = 50.0
            # Add all BaseSoftDeleteSchema fields:
            mock_pipe.created_at = "2024-01-15T10:30:00Z"
            mock_pipe.updated_at = "2024-01-15T10:30:00Z"
            mock_pipe.is_deleted = False
            mock_pipe.deleted_at = None
            mock_pipe.deleted_by_user_id = None
            # Add other required fields with defaults
            mock_pipe.nominal_diameter_mm = 100.0
            mock_pipe.wall_thickness_mm = 5.0
            mock_pipe.outer_diameter_mm = 110.0
            mock_pipe.fluid_type = "Process Water"
            mock_pipe.specific_heat_capacity_jkgc = 4180.0
            mock_pipe.viscosity_cp = 1.0
            mock_pipe.freezing_point_c = 0.0
            mock_pipe.safety_margin_percent = 20.0
            mock_pipe.pid = "P&ID-001"
            mock_pipe.line_tag = "L-001"
            mock_pipe.from_location = "Tank A"
            mock_pipe.to_location = "Tank B"
            mock_pipe.valve_count = 3
            mock_pipe.support_count = 5
            mock_pipe.calculated_heat_loss_wm = 25.5
            mock_pipe.required_heat_output_wm = 30.6
            mock_pipe.imported_data_revision_id = None

            mock_service_instance.get_pipe_details.return_value = mock_pipe

            # Make request
            response = client.get("/api/v1/heat-tracing/pipes/1")

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["id"] == 1
            assert response_data["name"] == "Test Pipe 001"

    def test_get_pipe_not_found(self, client: TestClient):
        """Test pipe retrieval with non-existent ID."""
        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock to raise not found error
            mock_service_instance.get_pipe_details.side_effect = NotFoundError(
                code="PIPE_NOT_FOUND", detail="Pipe with ID 999 not found"
            )

            # Make request
            response = client.get("/api/v1/heat-tracing/pipes/999")

            # Assertions
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()

    def test_update_pipe_success(self, client: TestClient):
        """Test successful pipe update."""
        update_data = {"name": "Updated Pipe Name", "length_m": 75.0}

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response with all required fields
            mock_pipe = Mock()
            mock_pipe.id = 1
            mock_pipe.name = "Updated Pipe Name"
            mock_pipe.project_id = 1
            mock_pipe.pipe_material_id = 10
            mock_pipe.insulation_material_id = 15
            mock_pipe.length_m = 75.0
            mock_pipe.insulation_thickness_mm = 50.0
            # Add all BaseSoftDeleteSchema fields:
            mock_pipe.created_at = "2024-01-15T10:30:00Z"
            mock_pipe.updated_at = "2024-01-15T10:30:00Z"
            mock_pipe.is_deleted = False
            mock_pipe.deleted_at = None
            mock_pipe.deleted_by_user_id = None
            # Add other required fields with defaults
            mock_pipe.nominal_diameter_mm = 100.0
            mock_pipe.wall_thickness_mm = 5.0
            mock_pipe.outer_diameter_mm = 110.0
            mock_pipe.fluid_type = "Process Water"
            mock_pipe.specific_heat_capacity_jkgc = 4180.0
            mock_pipe.viscosity_cp = 1.0
            mock_pipe.freezing_point_c = 0.0
            mock_pipe.safety_margin_percent = 20.0
            mock_pipe.pid = "P&ID-001"
            mock_pipe.line_tag = "L-001"
            mock_pipe.from_location = "Tank A"
            mock_pipe.to_location = "Tank B"
            mock_pipe.valve_count = 3
            mock_pipe.support_count = 5
            mock_pipe.calculated_heat_loss_wm = 25.5
            mock_pipe.required_heat_output_wm = 30.6
            mock_pipe.imported_data_revision_id = None

            mock_service_instance.update_pipe.return_value = mock_pipe

            # Make request
            response = client.put("/api/v1/heat-tracing/pipes/1", json=update_data)

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["name"] == "Updated Pipe Name"
            assert response_data["length_m"] == 75.0

    def test_delete_pipe_success(self, client: TestClient):
        """Test successful pipe deletion."""
        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock (delete returns None)
            mock_service_instance.delete_pipe.return_value = None

            # Make request
            response = client.delete("/api/v1/heat-tracing/pipes/1")

            # Assertions
            assert response.status_code == 204
            assert response.content == b""

    def test_list_pipes_success(self, client: TestClient):
        """Test successful pipes list retrieval."""
        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            from core.schemas.heat_tracing_schemas import PipeSummarySchema

            # Use Mock objects instead of PipeSummarySchema to avoid validation issues
            mock_pipe1 = Mock()
            mock_pipe1.id = 1
            mock_pipe1.name = "Pipe 1"
            mock_pipe1.project_id = 1
            mock_pipe1.length_m = 50.0
            mock_pipe1.line_tag = "L-001"
            mock_pipe1.calculated_heat_loss_wm = 25.5

            mock_pipe2 = Mock()
            mock_pipe2.id = 2
            mock_pipe2.name = "Pipe 2"
            mock_pipe2.project_id = 1
            mock_pipe2.length_m = 30.0
            mock_pipe2.line_tag = "L-002"
            mock_pipe2.calculated_heat_loss_wm = 15.2

            mock_pipes = [mock_pipe1, mock_pipe2]

            # Use Mock object for list response to avoid schema validation issues
            mock_list_response = Mock()
            mock_list_response.pipes = mock_pipes
            mock_list_response.total = 2
            mock_list_response.page = 1
            mock_list_response.per_page = 10
            mock_list_response.total_pages = 1
            mock_service_instance.get_pipes_list.return_value = mock_list_response

            # Make request
            response = client.get(
                "/api/v1/heat-tracing/projects/1/pipes?page=1&per_page=10"
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["total"] == 2
            assert len(response_data["pipes"]) == 2
            assert response_data["page"] == 1


class TestVesselEndpoints:
    """Test vessel-related API endpoints."""

    def test_create_vessel_success(self, client: TestClient):
        """Test successful vessel creation."""
        vessel_data = {
            "name": "Test Vessel T-001",
            "project_id": 1,
            "material_id": 12,
            "insulation_material_id": 15,
            "dimensions_json": '{"type": "cylinder", "diameter": 2.0, "height": 3.0}',
            "surface_area_m2": 25.13,
            "insulation_thickness_mm": 75.0,
            "equipment_tag": "T-001",
        }

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response with all required fields
            mock_vessel = Mock()
            mock_vessel.id = 1
            mock_vessel.name = "Test Vessel T-001"
            mock_vessel.project_id = 1
            mock_vessel.material_id = 12
            mock_vessel.insulation_material_id = 15
            mock_vessel.dimensions_json = (
                '{"type": "cylinder", "diameter": 2.0, "height": 3.0}'
            )
            mock_vessel.surface_area_m2 = 25.13
            mock_vessel.insulation_thickness_mm = 75.0
            mock_vessel.equipment_tag = "T-001"
            # Add all BaseSoftDeleteSchema fields:
            mock_vessel.created_at = "2024-01-15T10:30:00Z"
            mock_vessel.updated_at = "2024-01-15T10:30:00Z"
            mock_vessel.is_deleted = False
            mock_vessel.deleted_at = None
            mock_vessel.deleted_by_user_id = None
            # Add other required fields with defaults
            mock_vessel.fluid_type = "Process Water"
            mock_vessel.specific_heat_capacity_jkgc = 4180.0
            mock_vessel.viscosity_cp = 1.0
            mock_vessel.freezing_point_c = 0.0
            mock_vessel.safety_margin_percent = 20.0
            mock_vessel.pid = "P&ID-001"
            mock_vessel.calculated_heat_loss_w = 1250.0
            mock_vessel.required_heat_output_w = 1500.0
            mock_vessel.imported_data_revision_id = None

            mock_service_instance.create_vessel.return_value = mock_vessel

            # Make request
            response = client.post("/api/v1/heat-tracing/vessels", json=vessel_data)

            # Assertions
            assert response.status_code == 201
            response_data = response.json()
            assert response_data["name"] == "Test Vessel T-001"
            assert response_data["id"] == 1

    def test_get_vessel_success(self, client: TestClient):
        """Test successful vessel retrieval."""
        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response with all required fields
            mock_vessel = Mock()
            mock_vessel.id = 1
            mock_vessel.name = "Test Vessel T-001"
            mock_vessel.project_id = 1
            mock_vessel.material_id = 12
            mock_vessel.insulation_material_id = 15
            mock_vessel.dimensions_json = (
                '{"type": "cylinder", "diameter": 2.0, "height": 3.0}'
            )
            mock_vessel.surface_area_m2 = 25.13
            mock_vessel.insulation_thickness_mm = 75.0
            mock_vessel.equipment_tag = "T-001"
            # Add all BaseSoftDeleteSchema fields:
            mock_vessel.created_at = "2024-01-15T10:30:00Z"
            mock_vessel.updated_at = "2024-01-15T10:30:00Z"
            mock_vessel.is_deleted = False
            mock_vessel.deleted_at = None
            mock_vessel.deleted_by_user_id = None
            # Add other required fields with defaults
            mock_vessel.fluid_type = "Process Water"
            mock_vessel.specific_heat_capacity_jkgc = 4180.0
            mock_vessel.viscosity_cp = 1.0
            mock_vessel.freezing_point_c = 0.0
            mock_vessel.safety_margin_percent = 20.0
            mock_vessel.pid = "P&ID-001"
            mock_vessel.calculated_heat_loss_w = 1250.0
            mock_vessel.required_heat_output_w = 1500.0
            mock_vessel.imported_data_revision_id = None

            mock_service_instance.get_vessel_details.return_value = mock_vessel

            # Make request
            response = client.get("/api/v1/heat-tracing/vessels/1")

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["id"] == 1
            assert response_data["name"] == "Test Vessel T-001"


class TestCalculationEndpoints:
    """Test calculation-related API endpoints."""

    def test_calculate_pipe_heat_loss_success(self, client: TestClient):
        """Test successful pipe heat loss calculation."""
        calculation_data = {
            "pipe_diameter": 0.11,
            "pipe_length": 50.0,
            "fluid_temperature": 60.0,
            "ambient_temperature": -10.0,
            "insulation_thickness": 0.05,
            "insulation_type": "mineral_wool",
            "wind_speed": 5.0,
            "pipe_material": "carbon_steel",
        }

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_result = HeatLossCalculationResultSchema(
                heat_loss_rate=25.5,
                total_heat_loss=1275.0,
                surface_temperature=15.0,
                required_power=1530.0,
                safety_factor=1.2,
                calculation_metadata={"method": "steady_state"},
            )
            mock_service_instance.calculate_pipe_heat_loss.return_value = mock_result

            # Make request
            response = client.post(
                "/api/v1/heat-tracing/pipes/1/calculate-heat-loss",
                json=calculation_data,
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["heat_loss_rate"] == 25.5
            assert response_data["total_heat_loss"] == 1275.0

    def test_validate_standards_compliance_success(self, client: TestClient):
        """Test successful standards compliance validation."""
        validation_data = {
            "heat_loss_result": {
                "heat_loss_rate": 25.5,
                "total_heat_loss": 1275.0,
                "surface_temperature": 15.0,
                "required_power": 1530.0,
                "safety_factor": 1.2,
            },
            "design_parameters": {"fluid_temperature": 60.0},
            "project_standards": ["TR_50410"],
            "hazardous_area_zone": "Zone_1",
            "gas_group": "IIA",
            "temperature_class": "T3",
        }

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_result = StandardsValidationResultSchema(
                is_compliant=True,
                standard="TR 50410 / IEC 60079-30-1",
                violations=[],
                warnings=[],
                applied_factors={"safety_factor": 1.2},
                metadata={"validation_method": "automated"},
            )
            mock_service_instance.validate_standards_compliance.return_value = (
                mock_result
            )

            # Make request
            response = client.post(
                "/api/v1/heat-tracing/validate-standards-compliance",
                json=validation_data,
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["is_compliant"] is True
            assert response_data["standard"] == "TR 50410 / IEC 60079-30-1"


class TestDesignWorkflowEndpoints:
    """Test design workflow API endpoints."""

    def test_execute_design_workflow_success(self, client: TestClient):
        """Test successful design workflow execution."""
        design_data = {
            "project_id": 1,
            "pipe_ids": [1, 2],
            "vessel_ids": None,
            "design_parameters": {
                "fluid_temperature": 60.0,
                "ambient_temperature": -10.0,
            },
            "standards_context": {
                "heat_loss_result": {
                    "heat_loss_rate": 25.5,
                    "total_heat_loss": 1275.0,
                    "surface_temperature": 15.0,
                    "required_power": 1530.0,
                    "safety_factor": 1.2,
                },
                "design_parameters": {},
                "project_standards": ["TR_50410"],
                "hazardous_area_zone": "Zone_1",
                "gas_group": "IIA",
                "temperature_class": "T3",
            },
            "auto_assign_circuits": True,
            "optimization_enabled": True,
        }

        with patch(
            "api.v1.heat_tracing_routes.get_heat_tracing_service"
        ) as mock_service:
            mock_service_instance = Mock()
            mock_service.return_value = mock_service_instance

            # Setup mock response
            mock_result = HeatTracingDesignResultSchema(
                project_id=1,
                designed_pipes=[],
                designed_vessels=[],
                created_circuits=[],
                calculation_results=[],
                validation_results=[],
                design_summary={
                    "total_pipes_designed": 2,
                    "total_vessels_designed": 0,
                    "total_circuits_created": 2,
                    "total_power_requirement_w": 3060.0,
                },
                warnings=[],
                errors=[],
            )
            mock_service_instance.execute_heat_tracing_design.return_value = mock_result

            # Make request
            response = client.post(
                "/api/v1/heat-tracing/projects/1/design-workflow", json=design_data
            )

            # Assertions
            assert response.status_code == 200
            response_data = response.json()
            assert response_data["project_id"] == 1
            assert response_data["design_summary"]["total_pipes_designed"] == 2
