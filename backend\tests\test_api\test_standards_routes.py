# backend/tests/test_api/test_standards_routes.py
"""
Tests for Standards API Routes.

Tests the REST API endpoints for standards operations.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from main import app
from tests.fixtures.test_data import SAMPLE_STANDARDS_DATA, SAMPLE_API_REQUESTS


class TestStandardsRoutes:
    """Test suite for standards API routes."""

    def setup_method(self):
        """Set up test fixtures."""
        self.client = TestClient(app)
        self.sample_request = SAMPLE_API_REQUESTS["standards_validation"]

    @patch('api.dependencies.get_current_user')
    def test_get_available_standards(self, mock_get_user):
        """Test getting available standards."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/standards/available")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "standards_by_type" in data
        assert "total_standards" in data
        assert "supported_types" in data

    @patch('api.dependencies.get_current_user')
    def test_get_standards_recommendations(self, mock_get_user):
        """Test getting standards recommendations."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get(
            "/api/v1/standards/recommendations",
            params={
                "application_type": "heat_tracing",
                "location": "North America",
                "industry": "petrochemical",
                "hazardous_area": False,
                "offshore": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "recommendations" in data
        assert "application_type" in data

    @patch('api.dependencies.get_current_user')
    def test_validate_design_against_standards(self, mock_get_user):
        """Test design validation against standards."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.post(
            "/api/v1/standards/validate",
            json=self.sample_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "validation_summary" in data
        assert "individual_results" in data
        assert "compliance_report" in data

    @patch('api.dependencies.get_current_user')
    def test_validate_against_specific_standard(self, mock_get_user):
        """Test validation against a specific standard."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        design_data = SAMPLE_STANDARDS_DATA["design_data"]
        
        response = self.client.post(
            "/api/v1/standards/validate/IEEE-515-2017",
            json=design_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["standard_id"] == "IEEE-515-2017"
        assert "validation_result" in data
        assert "compliance_summary" in data

    @patch('api.dependencies.get_current_user')
    def test_calculate_standards_parameters(self, mock_get_user):
        """Test standards parameter calculations."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        calculation_request = {
            "input_data": SAMPLE_STANDARDS_DATA["input_data"],
            "standard_ids": ["IEEE-515-2017", "IEC-62395"],
            "calculation_options": {
                "include_safety_factors": True
            }
        }
        
        response = self.client.post(
            "/api/v1/standards/calculate",
            json=calculation_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "calculation_summary" in data
        assert "individual_results" in data
        assert "consolidated_parameters" in data

    @patch('api.dependencies.get_current_user')
    def test_calculate_with_specific_standard(self, mock_get_user):
        """Test calculations with a specific standard."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        input_data = SAMPLE_STANDARDS_DATA["input_data"]
        
        response = self.client.post(
            "/api/v1/standards/calculate/IEEE-515-2017",
            json=input_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["standard_id"] == "IEEE-515-2017"
        assert "calculation_result" in data
        assert "calculated_values" in data

    @patch('api.dependencies.get_current_user')
    def test_get_compliance_summary(self, mock_get_user):
        """Test getting compliance summary."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/standards/compliance/summary")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "summary" in data

    @patch('api.dependencies.get_current_user')
    def test_get_compliance_summary_filtered(self, mock_get_user):
        """Test getting filtered compliance summary."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get(
            "/api/v1/standards/compliance/summary",
            params={
                "project_id": "test_project_001",
                "time_period": "last_30_days"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @patch('api.dependencies.get_current_user')
    def test_get_standard_information(self, mock_get_user):
        """Test getting specific standard information."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/standards/standards/IEEE-515-2017/info")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "standard_info" in data
        assert "rules" in data
        assert "parameters" in data

    @patch('api.dependencies.get_current_user')
    def test_get_standard_information_not_found(self, mock_get_user):
        """Test getting information for non-existent standard."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/standards/standards/NONEXISTENT-STANDARD/info")
        
        assert response.status_code == 404

    @patch('api.dependencies.get_current_user')
    def test_standards_health_check(self, mock_get_user):
        """Test standards service health check."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get("/api/v1/standards/health")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "health_status" in data

    @patch('api.dependencies.get_current_user')
    def test_validate_design_invalid_input(self, mock_get_user):
        """Test design validation with invalid input."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        invalid_request = {
            "design_data": {},  # Empty design data
            "standard_ids": ["IEEE-515-2017"]
        }
        
        response = self.client.post(
            "/api/v1/standards/validate",
            json=invalid_request
        )
        
        assert response.status_code == 400

    @patch('api.dependencies.get_current_user')
    def test_validate_design_unknown_standard(self, mock_get_user):
        """Test design validation with unknown standard."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        request_with_unknown = self.sample_request.copy()
        request_with_unknown["standard_ids"] = ["UNKNOWN-STANDARD-123"]
        
        response = self.client.post(
            "/api/v1/standards/validate",
            json=request_with_unknown
        )
        
        # Should handle gracefully
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @patch('api.dependencies.get_current_user')
    def test_calculate_parameters_invalid_input(self, mock_get_user):
        """Test parameter calculations with invalid input."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        invalid_request = {
            "input_data": {},  # Empty input data
            "standard_ids": ["IEEE-515-2017"]
        }
        
        response = self.client.post(
            "/api/v1/standards/calculate",
            json=invalid_request
        )
        
        # Should handle gracefully
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    @patch('api.dependencies.get_current_user')
    def test_validate_design_multiple_standards(self, mock_get_user):
        """Test design validation against multiple standards."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        multi_standard_request = self.sample_request.copy()
        multi_standard_request["standard_ids"] = [
            "IEEE-515-2017", 
            "IEC-60079-30-1", 
            "IEC-62395"
        ]
        
        response = self.client.post(
            "/api/v1/standards/validate",
            json=multi_standard_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["individual_results"]) == 3

    @patch('api.dependencies.get_current_user')
    def test_calculate_parameters_multiple_standards(self, mock_get_user):
        """Test parameter calculations with multiple standards."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        multi_standard_request = {
            "input_data": SAMPLE_STANDARDS_DATA["input_data"],
            "standard_ids": ["IEEE-515-2017", "IEC-62395", "API-RP-14F"],
            "calculation_options": {}
        }
        
        response = self.client.post(
            "/api/v1/standards/calculate",
            json=multi_standard_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert len(data["individual_results"]) == 3

    @patch('api.dependencies.get_current_user')
    def test_get_recommendations_hazardous_area(self, mock_get_user):
        """Test getting recommendations for hazardous area application."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get(
            "/api/v1/standards/recommendations",
            params={
                "application_type": "heat_tracing",
                "location": "International",
                "industry": "oil_and_gas",
                "hazardous_area": True,
                "offshore": False
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # Should recommend IEC standards for hazardous areas
        recommended = data["recommendations"]["recommended_standards"]
        assert any("IEC" in std for std in recommended)

    @patch('api.dependencies.get_current_user')
    def test_get_recommendations_offshore(self, mock_get_user):
        """Test getting recommendations for offshore application."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.get(
            "/api/v1/standards/recommendations",
            params={
                "application_type": "heat_tracing",
                "location": "Gulf of Mexico",
                "industry": "petroleum",
                "hazardous_area": True,
                "offshore": True
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # Should recommend API standards for offshore
        recommended = data["recommendations"]["recommended_standards"]
        assert any("API" in std for std in recommended)

    def test_standards_routes_without_auth(self):
        """Test standards routes without authentication."""
        response = self.client.get("/api/v1/standards/available")
        
        # Should require authentication
        assert response.status_code in [401, 403]

    @patch('api.dependencies.get_current_user')
    def test_error_handling_invalid_json(self, mock_get_user):
        """Test error handling with invalid JSON."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        response = self.client.post(
            "/api/v1/standards/validate",
            data="invalid json",
            headers={"Content-Type": "application/json"}
        )
        
        assert response.status_code == 422  # Unprocessable Entity

    @patch('api.dependencies.get_current_user')
    def test_performance_multiple_standards_validation(self, mock_get_user):
        """Test performance with multiple standards validation."""
        mock_get_user.return_value = {"id": "test_user", "email": "<EMAIL>"}
        
        # Request validation against all available standards
        all_standards_request = self.sample_request.copy()
        all_standards_request["standard_ids"] = None  # Use all standards
        
        response = self.client.post(
            "/api/v1/standards/validate",
            json=all_standards_request
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        # Should validate against multiple standards
        assert data["validation_summary"]["total_standards_checked"] > 1
