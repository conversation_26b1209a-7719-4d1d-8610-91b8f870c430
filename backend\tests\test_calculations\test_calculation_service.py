# backend/tests/test_calculations/test_calculation_service.py
"""
Tests for Calculation Service.

Tests the main calculation service that orchestrates all calculation operations.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock

from core.calculations.calculation_service import CalculationService
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import SAMPLE_HEAT_TRACING_DATA, SAMPLE_ELECTRICAL_DATA


class TestCalculationService:
    """Test suite for CalculationService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = CalculationService()

    def test_initialization(self):
        """Test service initialization."""
        assert self.service is not None
        assert hasattr(self.service, 'calculate_heat_tracing_system')
        assert hasattr(self.service, 'calculate_electrical_system')

    @pytest.mark.asyncio
    async def test_calculate_heat_tracing_system_valid_input(self):
        """Test heat tracing system calculation with valid input."""
        project_data = SAMPLE_HEAT_TRACING_DATA
        
        result = await self.service.calculate_heat_tracing_system(
            project_data["circuits"],
            project_data["design_parameters"]
        )
        
        assert "success" in result
        assert result["success"] is True
        assert "circuit_results" in result
        assert "system_summary" in result
        assert len(result["circuit_results"]) == len(project_data["circuits"])

    @pytest.mark.asyncio
    async def test_calculate_heat_tracing_system_single_circuit(self):
        """Test heat tracing calculation for single circuit."""
        circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0]
        design_params = SAMPLE_HEAT_TRACING_DATA["design_parameters"]
        
        result = await self.service.calculate_heat_tracing_system([circuit], design_params)
        
        circuit_result = result["circuit_results"][0]
        assert circuit_result["circuit_id"] == circuit["circuit_id"]
        assert "heat_loss_calculation" in circuit_result
        assert "power_calculation" in circuit_result
        assert "cable_selection" in circuit_result

    @pytest.mark.asyncio
    async def test_calculate_electrical_system_valid_input(self):
        """Test electrical system calculation with valid input."""
        electrical_data = SAMPLE_ELECTRICAL_DATA
        
        result = await self.service.calculate_electrical_system(
            electrical_data["switchboards"]
        )
        
        assert "success" in result
        assert result["success"] is True
        assert "switchboard_results" in result
        assert "system_summary" in result

    @pytest.mark.asyncio
    async def test_calculate_heat_tracing_system_invalid_input(self):
        """Test heat tracing calculation with invalid input."""
        invalid_circuits = [
            {
                "circuit_id": "INVALID",
                "pipe_diameter": -0.1,  # Invalid negative
                "pipe_length": 0,  # Invalid zero
            }
        ]
        
        with pytest.raises(InvalidInputError):
            await self.service.calculate_heat_tracing_system(
                invalid_circuits, 
                SAMPLE_HEAT_TRACING_DATA["design_parameters"]
            )

    @pytest.mark.asyncio
    async def test_calculate_heat_tracing_system_empty_circuits(self):
        """Test heat tracing calculation with empty circuits list."""
        with pytest.raises(InvalidInputError, match="No circuits provided"):
            await self.service.calculate_heat_tracing_system(
                [], 
                SAMPLE_HEAT_TRACING_DATA["design_parameters"]
            )

    @pytest.mark.asyncio
    async def test_calculate_system_summary(self):
        """Test system summary calculation."""
        project_data = SAMPLE_HEAT_TRACING_DATA
        
        result = await self.service.calculate_heat_tracing_system(
            project_data["circuits"],
            project_data["design_parameters"]
        )
        
        summary = result["system_summary"]
        assert "total_circuits" in summary
        assert "total_power_requirement" in summary
        assert "total_heat_loss" in summary
        assert "average_power_density" in summary
        assert summary["total_circuits"] == len(project_data["circuits"])

    @pytest.mark.asyncio
    async def test_calculate_with_different_safety_factors(self):
        """Test calculations with different safety factors."""
        circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0]
        
        # Test with standard safety factor
        standard_params = {"safety_factor": 1.3}
        standard_result = await self.service.calculate_heat_tracing_system([circuit], standard_params)
        
        # Test with higher safety factor
        high_params = {"safety_factor": 1.5}
        high_result = await self.service.calculate_heat_tracing_system([circuit], high_params)
        
        standard_power = standard_result["circuit_results"][0]["power_calculation"]["design_power_requirement"]
        high_power = high_result["circuit_results"][0]["power_calculation"]["design_power_requirement"]
        
        assert high_power > standard_power

    @pytest.mark.asyncio
    async def test_calculate_with_optimization_options(self):
        """Test calculations with optimization options."""
        project_data = SAMPLE_HEAT_TRACING_DATA
        
        calculation_options = {
            "optimize_cable_selection": True,
            "minimize_power_consumption": True,
            "consider_installation_cost": True,
        }
        
        result = await self.service.calculate_heat_tracing_system(
            project_data["circuits"],
            project_data["design_parameters"],
            calculation_options
        )
        
        assert "optimization_results" in result
        assert result["optimization_results"]["optimization_applied"] is True

    @pytest.mark.asyncio
    async def test_calculate_parallel_processing(self):
        """Test parallel processing of multiple circuits."""
        # Create multiple circuits for parallel processing
        circuits = []
        for i in range(10):
            circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0].copy()
            circuit["circuit_id"] = f"HT-{i:03d}"
            circuits.append(circuit)
        
        import time
        start_time = time.time()
        
        result = await self.service.calculate_heat_tracing_system(
            circuits,
            SAMPLE_HEAT_TRACING_DATA["design_parameters"]
        )
        
        end_time = time.time()
        calculation_time = end_time - start_time
        
        assert len(result["circuit_results"]) == 10
        # Should complete reasonably quickly with parallel processing
        assert calculation_time < 10.0

    @pytest.mark.asyncio
    async def test_calculate_with_caching(self):
        """Test calculation caching functionality."""
        circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0]
        design_params = SAMPLE_HEAT_TRACING_DATA["design_parameters"]
        
        # First calculation
        result1 = await self.service.calculate_heat_tracing_system([circuit], design_params)
        
        # Second calculation with same data (should use cache)
        result2 = await self.service.calculate_heat_tracing_system([circuit], design_params)
        
        # Results should be identical
        assert result1["circuit_results"][0]["heat_loss_calculation"] == result2["circuit_results"][0]["heat_loss_calculation"]

    @pytest.mark.asyncio
    async def test_calculate_with_validation_errors(self):
        """Test calculation with validation errors."""
        invalid_circuit = {
            "circuit_id": "INVALID",
            "pipe_diameter": "not_a_number",  # Invalid type
            "pipe_length": 100.0,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
        }
        
        with pytest.raises(InvalidInputError):
            await self.service.calculate_heat_tracing_system(
                [invalid_circuit],
                SAMPLE_HEAT_TRACING_DATA["design_parameters"]
            )

    @pytest.mark.asyncio
    async def test_calculate_load_analysis(self):
        """Test electrical load analysis calculation."""
        electrical_data = SAMPLE_ELECTRICAL_DATA
        
        result = await self.service.calculate_electrical_load_analysis(
            electrical_data["switchboards"]
        )
        
        assert "load_summary" in result
        assert "diversity_analysis" in result
        assert "capacity_utilization" in result

    @pytest.mark.asyncio
    async def test_calculate_cost_estimation(self):
        """Test cost estimation calculation."""
        project_data = SAMPLE_HEAT_TRACING_DATA
        
        cost_parameters = {
            "cable_cost_per_meter": 50.0,  # $/m
            "installation_cost_per_meter": 25.0,  # $/m
            "control_panel_cost": 5000.0,  # $
            "engineering_cost_percentage": 15.0,  # %
        }
        
        result = await self.service.calculate_cost_estimation(
            project_data["circuits"],
            cost_parameters
        )
        
        assert "material_costs" in result
        assert "installation_costs" in result
        assert "total_project_cost" in result

    @pytest.mark.asyncio
    async def test_calculate_energy_analysis(self):
        """Test energy consumption analysis."""
        project_data = SAMPLE_HEAT_TRACING_DATA
        
        energy_parameters = {
            "electricity_rate": 0.12,  # $/kWh
            "operating_hours_per_day": 12,
            "operating_days_per_year": 120,
        }
        
        result = await self.service.calculate_energy_analysis(
            project_data["circuits"],
            energy_parameters
        )
        
        assert "annual_energy_consumption" in result
        assert "annual_energy_cost" in result
        assert "peak_demand" in result

    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self):
        """Test error handling and recovery mechanisms."""
        # Mix of valid and invalid circuits
        mixed_circuits = [
            SAMPLE_HEAT_TRACING_DATA["circuits"][0],  # Valid
            {
                "circuit_id": "INVALID",
                "pipe_diameter": -0.1,  # Invalid
            }
        ]
        
        # Should handle partial failures gracefully
        result = await self.service.calculate_heat_tracing_system(
            mixed_circuits,
            SAMPLE_HEAT_TRACING_DATA["design_parameters"],
            {"continue_on_error": True}
        )
        
        assert "circuit_results" in result
        assert "calculation_errors" in result
        assert len(result["calculation_errors"]) > 0

    @patch('core.calculations.calculation_service.logger')
    @pytest.mark.asyncio
    async def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during calculations."""
        project_data = SAMPLE_HEAT_TRACING_DATA
        
        await self.service.calculate_heat_tracing_system(
            project_data["circuits"],
            project_data["design_parameters"]
        )
        
        # Verify logging calls were made
        assert mock_logger.info.called
        assert mock_logger.debug.called

    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """Test performance monitoring and metrics collection."""
        project_data = SAMPLE_HEAT_TRACING_DATA
        
        result = await self.service.calculate_heat_tracing_system(
            project_data["circuits"],
            project_data["design_parameters"],
            {"collect_performance_metrics": True}
        )
        
        assert "performance_metrics" in result
        assert "calculation_time_seconds" in result["performance_metrics"]
        assert "memory_usage_mb" in result["performance_metrics"]
