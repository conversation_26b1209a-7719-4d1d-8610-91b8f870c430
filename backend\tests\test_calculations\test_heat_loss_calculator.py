# backend/tests/test_calculations/test_heat_loss_calculator.py
"""
Tests for Heat Loss Calculator.

Tests the core heat loss calculation functionality including
thermal resistance calculations and heat transfer modeling.
"""

import pytest
import math
from unittest.mock import Mock, patch

from core.calculations.heat_loss import HeatLossCalculator
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import SAMPLE_HEAT_TRACING_DATA


class TestHeatLossCalculator:
    """Test suite for HeatLossCalculator."""

    def setup_method(self):
        """Set up test fixtures."""
        self.calculator = HeatLossCalculator()
        self.sample_circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0]

    def test_initialization(self):
        """Test calculator initialization."""
        assert self.calculator is not None
        assert hasattr(self.calculator, 'calculate_heat_loss')
        assert hasattr(self.calculator, 'calculate_thermal_resistance')

    def test_calculate_thermal_resistance_valid_input(self):
        """Test thermal resistance calculation with valid input."""
        pipe_data = {
            "diameter": 0.1,  # m
            "insulation_thickness": 0.05,  # m
            "insulation_conductivity": 0.04,  # W/m·K
        }
        
        result = self.calculator.calculate_thermal_resistance(pipe_data)
        
        assert "insulation_resistance" in result
        assert "convection_resistance" in result
        assert "total_resistance" in result
        assert result["insulation_resistance"] > 0
        assert result["total_resistance"] > 0

    def test_calculate_thermal_resistance_invalid_diameter(self):
        """Test thermal resistance calculation with invalid diameter."""
        pipe_data = {
            "diameter": -0.1,  # Invalid negative diameter
            "insulation_thickness": 0.05,
            "insulation_conductivity": 0.04,
        }
        
        with pytest.raises(InvalidInputError, match="Pipe diameter must be positive"):
            self.calculator.calculate_thermal_resistance(pipe_data)

    def test_calculate_thermal_resistance_zero_thickness(self):
        """Test thermal resistance calculation with zero insulation thickness."""
        pipe_data = {
            "diameter": 0.1,
            "insulation_thickness": 0.0,  # Zero thickness
            "insulation_conductivity": 0.04,
        }
        
        with pytest.raises(InvalidInputError, match="Insulation thickness must be positive"):
            self.calculator.calculate_thermal_resistance(pipe_data)

    def test_calculate_heat_loss_valid_input(self):
        """Test heat loss calculation with valid input."""
        circuit_data = {
            "pipe_diameter": 0.1,
            "insulation_thickness": 0.05,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "wind_speed": 5.0,
            "pipe_length": 100.0,
        }
        
        result = self.calculator.calculate_heat_loss(circuit_data)
        
        assert "heat_loss_per_meter" in result
        assert "total_heat_loss" in result
        assert "thermal_resistance" in result
        assert result["heat_loss_per_meter"] > 0
        assert result["total_heat_loss"] > 0
        assert result["total_heat_loss"] == result["heat_loss_per_meter"] * 100.0

    def test_calculate_heat_loss_zero_temperature_difference(self):
        """Test heat loss calculation with zero temperature difference."""
        circuit_data = {
            "pipe_diameter": 0.1,
            "insulation_thickness": 0.05,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 20.0,
            "ambient_temperature": 20.0,  # Same as maintain temperature
            "wind_speed": 5.0,
            "pipe_length": 100.0,
        }
        
        result = self.calculator.calculate_heat_loss(circuit_data)
        
        assert result["heat_loss_per_meter"] == 0.0
        assert result["total_heat_loss"] == 0.0

    def test_calculate_heat_loss_missing_required_fields(self):
        """Test heat loss calculation with missing required fields."""
        circuit_data = {
            "pipe_diameter": 0.1,
            # Missing other required fields
        }
        
        with pytest.raises(InvalidInputError, match="Missing required fields"):
            self.calculator.calculate_heat_loss(circuit_data)

    def test_calculate_convection_coefficient(self):
        """Test convection coefficient calculation."""
        # Test natural convection
        h_natural = self.calculator._calculate_convection_coefficient(0.1, 0.0)
        assert h_natural > 0
        
        # Test forced convection
        h_forced = self.calculator._calculate_convection_coefficient(0.1, 10.0)
        assert h_forced > h_natural

    def test_calculate_radiation_coefficient(self):
        """Test radiation coefficient calculation."""
        surface_temp = 80.0  # °C
        ambient_temp = 20.0  # °C
        
        h_rad = self.calculator._calculate_radiation_coefficient(surface_temp, ambient_temp)
        assert h_rad > 0

    def test_calculate_heat_loss_with_radiation(self):
        """Test heat loss calculation including radiation effects."""
        circuit_data = {
            "pipe_diameter": 0.1,
            "insulation_thickness": 0.05,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 100.0,  # High temperature for radiation
            "ambient_temperature": 20.0,
            "wind_speed": 5.0,
            "pipe_length": 100.0,
            "include_radiation": True,
            "surface_emissivity": 0.9,
        }
        
        result = self.calculator.calculate_heat_loss(circuit_data)
        
        assert "radiation_heat_loss" in result
        assert result["radiation_heat_loss"] > 0

    def test_calculate_heat_loss_multiple_layers(self):
        """Test heat loss calculation with multiple insulation layers."""
        circuit_data = {
            "pipe_diameter": 0.1,
            "insulation_layers": [
                {"thickness": 0.03, "conductivity": 0.04},
                {"thickness": 0.02, "conductivity": 0.06},
            ],
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "wind_speed": 5.0,
            "pipe_length": 100.0,
        }
        
        result = self.calculator.calculate_heat_loss(circuit_data)
        
        assert "heat_loss_per_meter" in result
        assert result["heat_loss_per_meter"] > 0

    def test_calculate_heat_loss_with_safety_factors(self):
        """Test heat loss calculation with safety factors."""
        circuit_data = {
            "pipe_diameter": 0.1,
            "insulation_thickness": 0.05,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "wind_speed": 5.0,
            "pipe_length": 100.0,
            "safety_factor": 1.3,
        }
        
        result = self.calculator.calculate_heat_loss(circuit_data)
        
        assert "design_heat_loss_per_meter" in result
        assert result["design_heat_loss_per_meter"] == result["heat_loss_per_meter"] * 1.3

    def test_calculate_heat_loss_performance(self):
        """Test heat loss calculation performance with large dataset."""
        import time
        
        # Create large dataset
        circuits = []
        for i in range(100):
            circuits.append({
                "pipe_diameter": 0.1,
                "insulation_thickness": 0.05,
                "insulation_conductivity": 0.04,
                "maintain_temperature": 60.0,
                "ambient_temperature": 0.0,
                "wind_speed": 5.0,
                "pipe_length": 100.0,
            })
        
        start_time = time.time()
        
        for circuit in circuits:
            self.calculator.calculate_heat_loss(circuit)
        
        end_time = time.time()
        calculation_time = end_time - start_time
        
        # Should complete 100 calculations in reasonable time
        assert calculation_time < 5.0  # 5 seconds max

    def test_calculate_heat_loss_edge_cases(self):
        """Test heat loss calculation edge cases."""
        # Very small pipe
        small_pipe_data = {
            "pipe_diameter": 0.001,  # 1mm
            "insulation_thickness": 0.001,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "wind_speed": 5.0,
            "pipe_length": 1.0,
        }
        
        result = self.calculator.calculate_heat_loss(small_pipe_data)
        assert result["heat_loss_per_meter"] > 0
        
        # Very large pipe
        large_pipe_data = {
            "pipe_diameter": 2.0,  # 2m
            "insulation_thickness": 0.2,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "wind_speed": 5.0,
            "pipe_length": 1000.0,
        }
        
        result = self.calculator.calculate_heat_loss(large_pipe_data)
        assert result["heat_loss_per_meter"] > 0

    def test_calculate_heat_loss_validation_errors(self):
        """Test various validation errors in heat loss calculation."""
        base_data = {
            "pipe_diameter": 0.1,
            "insulation_thickness": 0.05,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "wind_speed": 5.0,
            "pipe_length": 100.0,
        }
        
        # Test negative conductivity
        invalid_data = base_data.copy()
        invalid_data["insulation_conductivity"] = -0.04
        with pytest.raises(InvalidInputError):
            self.calculator.calculate_heat_loss(invalid_data)
        
        # Test negative wind speed
        invalid_data = base_data.copy()
        invalid_data["wind_speed"] = -5.0
        with pytest.raises(InvalidInputError):
            self.calculator.calculate_heat_loss(invalid_data)
        
        # Test negative length
        invalid_data = base_data.copy()
        invalid_data["pipe_length"] = -100.0
        with pytest.raises(InvalidInputError):
            self.calculator.calculate_heat_loss(invalid_data)

    @patch('core.calculations.heat_loss.logger')
    def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during calculations."""
        circuit_data = {
            "pipe_diameter": 0.1,
            "insulation_thickness": 0.05,
            "insulation_conductivity": 0.04,
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
            "wind_speed": 5.0,
            "pipe_length": 100.0,
        }
        
        self.calculator.calculate_heat_loss(circuit_data)
        
        # Verify logging calls were made
        assert mock_logger.info.called
        assert mock_logger.debug.called
