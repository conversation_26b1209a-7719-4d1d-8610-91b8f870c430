# backend/tests/test_calculations/test_power_calculator.py
"""
Tests for Power Calculator.

Tests the power requirement calculation functionality including
cable selection and electrical parameter calculations.
"""

import pytest
import math
from unittest.mock import Mock, patch

from core.calculations.power import PowerCalculator
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import SAMPLE_HEAT_TRACING_DATA


class TestPowerCalculator:
    """Test suite for PowerCalculator."""

    def setup_method(self):
        """Set up test fixtures."""
        self.calculator = PowerCalculator()
        self.sample_circuit = SAMPLE_HEAT_TRACING_DATA["circuits"][0]

    def test_initialization(self):
        """Test calculator initialization."""
        assert self.calculator is not None
        assert hasattr(self.calculator, 'calculate_power_requirements')
        assert hasattr(self.calculator, 'select_cable')

    def test_calculate_power_requirements_valid_input(self):
        """Test power requirements calculation with valid input."""
        circuit_data = {
            "heat_loss_per_meter": 25.0,  # W/m
            "circuit_length": 100.0,  # m
            "application_type": "freeze_protection",
            "safety_factor": 1.3,
            "diversity_factor": 0.8,
        }
        
        result = self.calculator.calculate_power_requirements(circuit_data)
        
        assert "base_power_requirement" in result
        assert "design_power_requirement" in result
        assert "power_per_meter" in result
        assert result["base_power_requirement"] == 2500.0  # 25 * 100
        assert result["design_power_requirement"] == 3250.0  # 2500 * 1.3

    def test_calculate_power_requirements_different_applications(self):
        """Test power requirements for different application types."""
        base_data = {
            "heat_loss_per_meter": 25.0,
            "circuit_length": 100.0,
            "safety_factor": 1.3,
        }
        
        # Test freeze protection
        freeze_data = base_data.copy()
        freeze_data["application_type"] = "freeze_protection"
        freeze_result = self.calculator.calculate_power_requirements(freeze_data)
        
        # Test process temperature
        process_data = base_data.copy()
        process_data["application_type"] = "process_temperature"
        process_result = self.calculator.calculate_power_requirements(process_data)
        
        # Test critical service
        critical_data = base_data.copy()
        critical_data["application_type"] = "critical_service"
        critical_result = self.calculator.calculate_power_requirements(critical_data)
        
        # Critical service should have higher safety factor
        assert critical_result["design_power_requirement"] >= process_result["design_power_requirement"]
        assert process_result["design_power_requirement"] >= freeze_result["design_power_requirement"]

    def test_calculate_electrical_parameters(self):
        """Test electrical parameter calculations."""
        power_data = {
            "total_power": 3250.0,  # W
            "voltage": 240,  # V
            "power_factor": 0.95,
            "circuit_type": "single_phase",
        }
        
        result = self.calculator.calculate_electrical_parameters(power_data)
        
        assert "operating_current" in result
        assert "apparent_power" in result
        assert "power_factor" in result
        
        expected_current = 3250.0 / (240 * 0.95)
        assert abs(result["operating_current"] - expected_current) < 0.01

    def test_calculate_electrical_parameters_three_phase(self):
        """Test electrical parameter calculations for three-phase circuits."""
        power_data = {
            "total_power": 10000.0,  # W
            "voltage": 480,  # V (line-to-line)
            "power_factor": 0.9,
            "circuit_type": "three_phase",
        }
        
        result = self.calculator.calculate_electrical_parameters(power_data)
        
        expected_current = 10000.0 / (math.sqrt(3) * 480 * 0.9)
        assert abs(result["operating_current"] - expected_current) < 0.01

    def test_select_cable_self_regulating(self):
        """Test cable selection for self-regulating cables."""
        selection_data = {
            "power_per_meter": 25.0,  # W/m
            "voltage": 240,  # V
            "circuit_length": 100.0,  # m
            "application_type": "freeze_protection",
            "installation_method": "direct_attach",
            "ambient_temperature": -20.0,  # °C
        }
        
        result = self.calculator.select_cable(selection_data)
        
        assert "cable_type" in result
        assert "cable_model" in result
        assert "power_output" in result
        assert "maximum_length" in result
        assert result["cable_type"] == "self_regulating"

    def test_select_cable_constant_wattage(self):
        """Test cable selection for constant wattage cables."""
        selection_data = {
            "power_per_meter": 50.0,  # High power density
            "voltage": 240,
            "circuit_length": 200.0,
            "application_type": "process_temperature",
            "installation_method": "direct_attach",
            "ambient_temperature": 0.0,
        }
        
        result = self.calculator.select_cable(selection_data)
        
        assert result["cable_type"] in ["constant_wattage_parallel", "constant_wattage_series"]

    def test_calculate_cable_derating(self):
        """Test cable derating calculations."""
        derating_data = {
            "base_power_output": 25.0,  # W/m
            "ambient_temperature": 40.0,  # °C
            "installation_method": "in_conduit",
            "grouping_factor": 0.8,  # Multiple cables
            "insulation_factor": 0.9,
        }
        
        result = self.calculator.calculate_cable_derating(derating_data)
        
        assert "derated_power_output" in result
        assert "derating_factors" in result
        assert result["derated_power_output"] < derating_data["base_power_output"]

    def test_calculate_voltage_drop(self):
        """Test voltage drop calculations."""
        voltage_data = {
            "current": 15.0,  # A
            "circuit_length": 100.0,  # m
            "cable_resistance": 0.5,  # Ω/km
            "voltage": 240,  # V
            "power_factor": 0.95,
        }
        
        result = self.calculator.calculate_voltage_drop(voltage_data)
        
        assert "voltage_drop" in result
        assert "voltage_drop_percentage" in result
        assert "voltage_at_load" in result
        
        # Voltage drop should be reasonable
        assert result["voltage_drop_percentage"] < 10.0  # Less than 10%

    def test_calculate_circuit_protection(self):
        """Test circuit protection calculations."""
        protection_data = {
            "operating_current": 15.0,  # A
            "starting_current": 45.0,  # A (3x operating)
            "cable_ampacity": 20.0,  # A
            "application_type": "freeze_protection",
        }
        
        result = self.calculator.calculate_circuit_protection(protection_data)
        
        assert "breaker_rating" in result
        assert "fuse_rating" in result
        assert "overload_setting" in result
        
        # Breaker should be sized appropriately
        assert result["breaker_rating"] >= protection_data["operating_current"]
        assert result["breaker_rating"] <= protection_data["cable_ampacity"]

    def test_calculate_power_requirements_invalid_input(self):
        """Test power requirements calculation with invalid input."""
        # Test negative heat loss
        invalid_data = {
            "heat_loss_per_meter": -25.0,  # Invalid negative
            "circuit_length": 100.0,
            "application_type": "freeze_protection",
        }
        
        with pytest.raises(InvalidInputError, match="Heat loss per meter must be positive"):
            self.calculator.calculate_power_requirements(invalid_data)

    def test_calculate_power_requirements_zero_length(self):
        """Test power requirements calculation with zero circuit length."""
        circuit_data = {
            "heat_loss_per_meter": 25.0,
            "circuit_length": 0.0,  # Zero length
            "application_type": "freeze_protection",
        }
        
        with pytest.raises(InvalidInputError, match="Circuit length must be positive"):
            self.calculator.calculate_power_requirements(circuit_data)

    def test_calculate_electrical_parameters_invalid_voltage(self):
        """Test electrical parameters calculation with invalid voltage."""
        power_data = {
            "total_power": 3250.0,
            "voltage": 0,  # Invalid zero voltage
            "power_factor": 0.95,
            "circuit_type": "single_phase",
        }
        
        with pytest.raises(InvalidInputError, match="Voltage must be positive"):
            self.calculator.calculate_electrical_parameters(power_data)

    def test_calculate_electrical_parameters_invalid_power_factor(self):
        """Test electrical parameters calculation with invalid power factor."""
        power_data = {
            "total_power": 3250.0,
            "voltage": 240,
            "power_factor": 1.5,  # Invalid > 1.0
            "circuit_type": "single_phase",
        }
        
        with pytest.raises(InvalidInputError, match="Power factor must be between 0 and 1"):
            self.calculator.calculate_electrical_parameters(power_data)

    def test_select_cable_missing_data(self):
        """Test cable selection with missing required data."""
        incomplete_data = {
            "power_per_meter": 25.0,
            # Missing other required fields
        }
        
        with pytest.raises(InvalidInputError, match="Missing required fields"):
            self.calculator.select_cable(incomplete_data)

    def test_calculate_power_requirements_with_diversity(self):
        """Test power requirements calculation with diversity factor."""
        circuit_data = {
            "heat_loss_per_meter": 25.0,
            "circuit_length": 100.0,
            "application_type": "freeze_protection",
            "safety_factor": 1.3,
            "diversity_factor": 0.8,
        }
        
        result = self.calculator.calculate_power_requirements(circuit_data)
        
        # Should apply diversity factor to reduce total power
        expected_diversified = 2500.0 * 1.3 * 0.8  # base * safety * diversity
        assert abs(result["diversified_power_requirement"] - expected_diversified) < 0.01

    def test_calculate_startup_requirements(self):
        """Test startup power requirements calculation."""
        startup_data = {
            "steady_state_power": 3250.0,  # W
            "cable_type": "self_regulating",
            "ambient_temperature": -20.0,  # °C
            "circuit_length": 100.0,  # m
        }
        
        result = self.calculator.calculate_startup_requirements(startup_data)
        
        assert "startup_power" in result
        assert "startup_current" in result
        assert "startup_duration" in result
        
        # Startup power should be higher than steady state
        assert result["startup_power"] > startup_data["steady_state_power"]

    def test_calculate_energy_consumption(self):
        """Test energy consumption calculations."""
        consumption_data = {
            "power_requirement": 3250.0,  # W
            "operating_hours_per_day": 12,
            "operating_days_per_year": 120,  # Winter season
            "efficiency_factor": 0.95,
        }
        
        result = self.calculator.calculate_energy_consumption(consumption_data)
        
        assert "daily_energy_consumption" in result
        assert "annual_energy_consumption" in result
        assert "energy_cost_estimate" in result
        
        expected_daily = 3250.0 * 12 / 1000  # kWh
        assert abs(result["daily_energy_consumption"] - expected_daily) < 0.01

    @patch('core.calculations.power.logger')
    def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during calculations."""
        circuit_data = {
            "heat_loss_per_meter": 25.0,
            "circuit_length": 100.0,
            "application_type": "freeze_protection",
            "safety_factor": 1.3,
        }
        
        self.calculator.calculate_power_requirements(circuit_data)
        
        # Verify logging calls were made
        assert mock_logger.info.called
        assert mock_logger.debug.called
