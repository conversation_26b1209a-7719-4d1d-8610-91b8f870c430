# backend/tests/test_import_export/test_import_service.py
"""
Tests for Import Service.

Tests the main import service that orchestrates all import operations.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch, AsyncMock

from core.import_export.import_service import ImportService
from core.errors.exceptions import InvalidInputError, FileProcessingError
from tests.fixtures.test_data import (
    SAMPLE_CSV_DATA, 
    SAMPLE_JSON_DATA,
    get_sample_csv_file,
    get_sample_json_file,
    cleanup_temp_file
)


class TestImportService:
    """Test suite for ImportService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = ImportService()

    def test_initialization(self):
        """Test service initialization."""
        assert self.service is not None
        assert hasattr(self.service, 'import_project_data')
        assert hasattr(self.service, 'validate_import_data')

    @pytest.mark.asyncio
    async def test_import_csv_file_valid(self):
        """Test importing a valid CSV file."""
        csv_file = get_sample_csv_file()
        
        try:
            result = await self.service.import_project_data(
                file_path=csv_file,
                file_type="csv",
                import_type="circuits"
            )
            
            assert "success" in result
            assert result["success"] is True
            assert "imported_data" in result
            assert "import_summary" in result
            assert len(result["imported_data"]["circuits"]) == 3
            
        finally:
            cleanup_temp_file(csv_file)

    @pytest.mark.asyncio
    async def test_import_json_file_valid(self):
        """Test importing a valid JSON file."""
        json_file = get_sample_json_file()
        
        try:
            result = await self.service.import_project_data(
                file_path=json_file,
                file_type="json",
                import_type="project"
            )
            
            assert result["success"] is True
            assert "project_info" in result["imported_data"]
            assert "circuits" in result["imported_data"]
            
        finally:
            cleanup_temp_file(json_file)

    @pytest.mark.asyncio
    async def test_import_excel_file_valid(self):
        """Test importing a valid Excel file."""
        # Create temporary Excel file
        import pandas as pd
        
        temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
        temp_file.close()
        
        # Create sample Excel data
        df = pd.DataFrame({
            'Circuit ID': ['HT-001', 'HT-002'],
            'Circuit Name': ['Main Line', 'Secondary Line'],
            'Power (W)': [2500, 1500]
        })
        df.to_excel(temp_file.name, index=False)
        
        try:
            result = await self.service.import_project_data(
                file_path=temp_file.name,
                file_type="excel",
                import_type="circuits"
            )
            
            assert result["success"] is True
            assert len(result["imported_data"]["circuits"]) == 2
            
        finally:
            cleanup_temp_file(temp_file.name)

    @pytest.mark.asyncio
    async def test_import_nonexistent_file(self):
        """Test importing a nonexistent file."""
        with pytest.raises(FileProcessingError, match="File not found"):
            await self.service.import_project_data(
                file_path="nonexistent_file.csv",
                file_type="csv",
                import_type="circuits"
            )

    @pytest.mark.asyncio
    async def test_import_unsupported_file_type(self):
        """Test importing an unsupported file type."""
        temp_file = tempfile.NamedTemporaryFile(suffix='.txt', delete=False)
        temp_file.write(b"Some text content")
        temp_file.close()
        
        try:
            with pytest.raises(InvalidInputError, match="Unsupported file type"):
                await self.service.import_project_data(
                    file_path=temp_file.name,
                    file_type="txt",
                    import_type="circuits"
                )
                
        finally:
            cleanup_temp_file(temp_file.name)

    @pytest.mark.asyncio
    async def test_import_with_validation_errors(self):
        """Test importing data with validation errors."""
        # Create CSV with invalid data
        invalid_csv_content = """Circuit ID,Power (W),Voltage (V)
HT-001,-2500,240
HT-002,abc,480
HT-003,3000,999"""
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        temp_file.write(invalid_csv_content)
        temp_file.close()
        
        try:
            result = await self.service.import_project_data(
                file_path=temp_file.name,
                file_type="csv",
                import_type="circuits",
                validation_options={"strict_validation": True}
            )
            
            assert "validation_errors" in result
            assert len(result["validation_errors"]) > 0
            
        finally:
            cleanup_temp_file(temp_file.name)

    @pytest.mark.asyncio
    async def test_import_with_data_transformation(self):
        """Test importing data with transformation rules."""
        csv_file = get_sample_csv_file()
        
        transformation_rules = {
            "field_mappings": {
                "Circuit ID": "circuit_id",
                "Circuit Name": "circuit_name",
                "Pipe Diameter (m)": "pipe_diameter"
            },
            "unit_conversions": {
                "pipe_diameter": {"from": "m", "to": "mm", "factor": 1000}
            }
        }
        
        try:
            result = await self.service.import_project_data(
                file_path=csv_file,
                file_type="csv",
                import_type="circuits",
                transformation_rules=transformation_rules
            )
            
            assert result["success"] is True
            first_circuit = result["imported_data"]["circuits"][0]
            assert "circuit_id" in first_circuit
            assert first_circuit["pipe_diameter"] == 100.0  # Converted from 0.1m to 100mm
            
        finally:
            cleanup_temp_file(csv_file)

    @pytest.mark.asyncio
    async def test_import_with_duplicate_detection(self):
        """Test importing data with duplicate detection."""
        # Create CSV with duplicate circuit IDs
        duplicate_csv_content = """Circuit ID,Circuit Name,Power (W)
HT-001,Main Line,2500
HT-002,Secondary Line,1500
HT-001,Duplicate Line,3000"""
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        temp_file.write(duplicate_csv_content)
        temp_file.close()
        
        try:
            result = await self.service.import_project_data(
                file_path=temp_file.name,
                file_type="csv",
                import_type="circuits",
                import_options={"detect_duplicates": True, "duplicate_key": "Circuit ID"}
            )
            
            assert "duplicate_records" in result
            assert len(result["duplicate_records"]) > 0
            
        finally:
            cleanup_temp_file(temp_file.name)

    @pytest.mark.asyncio
    async def test_import_large_file(self):
        """Test importing a large file."""
        # Create large CSV file
        large_csv_content = "Circuit ID,Power (W)\n"
        for i in range(5000):
            large_csv_content += f"HT-{i:05d},{1000 + i}\n"
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        temp_file.write(large_csv_content)
        temp_file.close()
        
        try:
            import time
            start_time = time.time()
            
            result = await self.service.import_project_data(
                file_path=temp_file.name,
                file_type="csv",
                import_type="circuits"
            )
            
            end_time = time.time()
            import_time = end_time - start_time
            
            assert result["success"] is True
            assert len(result["imported_data"]["circuits"]) == 5000
            # Should import reasonably quickly
            assert import_time < 30.0  # 30 seconds max
            
        finally:
            cleanup_temp_file(temp_file.name)

    @pytest.mark.asyncio
    async def test_import_with_progress_tracking(self):
        """Test importing with progress tracking."""
        csv_file = get_sample_csv_file()
        
        progress_callback = Mock()
        
        try:
            result = await self.service.import_project_data(
                file_path=csv_file,
                file_type="csv",
                import_type="circuits",
                progress_callback=progress_callback
            )
            
            assert result["success"] is True
            # Progress callback should have been called
            assert progress_callback.called
            
        finally:
            cleanup_temp_file(csv_file)

    @pytest.mark.asyncio
    async def test_validate_import_data(self):
        """Test import data validation."""
        import_data = {
            "circuits": [
                {
                    "circuit_id": "HT-001",
                    "circuit_name": "Main Line",
                    "pipe_diameter": 0.1,
                    "pipe_length": 100.0,
                    "power_density": 25.0
                }
            ]
        }
        
        validation_result = await self.service.validate_import_data(
            import_data, 
            "circuits"
        )
        
        assert validation_result["is_valid"] is True
        assert len(validation_result["errors"]) == 0

    @pytest.mark.asyncio
    async def test_validate_import_data_with_errors(self):
        """Test import data validation with errors."""
        invalid_import_data = {
            "circuits": [
                {
                    "circuit_id": "",  # Empty ID
                    "pipe_diameter": -0.1,  # Negative diameter
                    "pipe_length": 0,  # Zero length
                }
            ]
        }
        
        validation_result = await self.service.validate_import_data(
            invalid_import_data, 
            "circuits"
        )
        
        assert validation_result["is_valid"] is False
        assert len(validation_result["errors"]) > 0

    @pytest.mark.asyncio
    async def test_import_with_custom_parser(self):
        """Test importing with custom parser configuration."""
        csv_file = get_sample_csv_file()
        
        parser_config = {
            "delimiter": ",",
            "quote_char": '"',
            "skip_initial_space": True,
            "convert_types": True
        }
        
        try:
            result = await self.service.import_project_data(
                file_path=csv_file,
                file_type="csv",
                import_type="circuits",
                parser_config=parser_config
            )
            
            assert result["success"] is True
            
        finally:
            cleanup_temp_file(csv_file)

    @pytest.mark.asyncio
    async def test_import_with_error_recovery(self):
        """Test importing with error recovery options."""
        # Create CSV with mixed valid and invalid data
        mixed_csv_content = """Circuit ID,Power (W),Voltage (V)
HT-001,2500,240
HT-002,invalid_power,240
HT-003,3000,480
HT-004,-1000,240"""
        
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        temp_file.write(mixed_csv_content)
        temp_file.close()
        
        try:
            result = await self.service.import_project_data(
                file_path=temp_file.name,
                file_type="csv",
                import_type="circuits",
                import_options={"continue_on_error": True, "max_errors": 10}
            )
            
            assert "imported_data" in result
            assert "import_errors" in result
            assert len(result["import_errors"]) > 0
            # Should have imported valid records
            assert len(result["imported_data"]["circuits"]) >= 2
            
        finally:
            cleanup_temp_file(temp_file.name)

    @pytest.mark.asyncio
    async def test_get_import_template(self):
        """Test getting import template for specific data type."""
        template = await self.service.get_import_template("circuits")
        
        assert "template_structure" in template
        assert "required_fields" in template
        assert "optional_fields" in template
        assert "field_descriptions" in template
        assert "sample_data" in template

    @pytest.mark.asyncio
    async def test_get_supported_formats(self):
        """Test getting supported import formats."""
        formats = await self.service.get_supported_formats()
        
        assert "csv" in formats
        assert "json" in formats
        assert "excel" in formats
        
        # Check format details
        csv_format = formats["csv"]
        assert "extensions" in csv_format
        assert "description" in csv_format
        assert "supported_options" in csv_format

    @patch('core.import_export.import_service.logger')
    @pytest.mark.asyncio
    async def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during import."""
        csv_file = get_sample_csv_file()
        
        try:
            await self.service.import_project_data(
                file_path=csv_file,
                file_type="csv",
                import_type="circuits"
            )
            
            # Verify logging calls were made
            assert mock_logger.info.called
            assert mock_logger.debug.called
            
        finally:
            cleanup_temp_file(csv_file)
