# backend/tests/test_integration/test_electrical_integration_enhanced.py
"""
Enhanced Integration tests for electrical system functionality.

This module tests the integration between electrical components,
ensuring that the electrical system works correctly as a whole.
"""

import pytest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from core.models.project import Project
from core.models.enums import ElectricalNodeType

pytestmark = [
    pytest.mark.integration,
    pytest.mark.electrical,
    pytest.mark.enhanced,
]


class TestElectricalSystemIntegration:
    """Test electrical system integration scenarios."""

    def test_create_complete_electrical_system(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test creating a complete electrical system with nodes and connections."""
        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_node_repo, \
             patch("api.v1.electrical_routes.CableRouteRepository") as mock_cable_repo:
            
            # Setup mocks
            mock_node_instance = MagicMock()
            mock_node_repo.return_value = mock_node_instance
            mock_cable_instance = MagicMock()
            mock_cable_repo.return_value = mock_cable_instance
            
            # Step 1: Create main distribution panel
            main_panel_data = {
                "project_id": sample_project.id,
                "name": "Main Distribution Panel",
                "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                "voltage_v": 480.0,
                "power_capacity_kva": 2000.0,
                "location_description": "Electrical Room A",
            }
            
            mock_main_panel = MagicMock()
            mock_main_panel.id = 1
            mock_main_panel.name = "Main Distribution Panel"
            mock_node_instance.create.return_value = mock_main_panel
            
            response = client.post("/api/v1/electrical/nodes", json=main_panel_data)
            assert response.status_code == 201
            main_panel_id = response.json()["id"]
            
            # Step 2: Create sub-distribution panels
            sub_panels = []
            for i in range(3):
                sub_panel_data = {
                    "project_id": sample_project.id,
                    "name": f"Sub Panel {i+1}",
                    "node_type": ElectricalNodeType.SWITCHBOARD_OUTGOING.value,
                    "voltage_v": 480.0,
                    "power_capacity_kva": 500.0,
                    "location_description": f"Area {i+1}",
                }
                
                mock_sub_panel = MagicMock()
                mock_sub_panel.id = i + 2
                mock_sub_panel.name = f"Sub Panel {i+1}"
                mock_node_instance.create.return_value = mock_sub_panel
                
                response = client.post("/api/v1/electrical/nodes", json=sub_panel_data)
                assert response.status_code == 201
                sub_panels.append(response.json()["id"])
            
            # Step 3: Create cable routes connecting main to sub panels
            for i, sub_panel_id in enumerate(sub_panels):
                cable_data = {
                    "project_id": sample_project.id,
                    "name": f"Main to Sub {i+1}",
                    "from_node_id": main_panel_id,
                    "to_node_id": sub_panel_id,
                    "cable_component_id": 1,
                    "length_m": 50.0 + (i * 25),
                    "number_of_runs": 1,
                }
                
                mock_cable = MagicMock()
                mock_cable.id = i + 1
                mock_cable.name = f"Main to Sub {i+1}"
                mock_cable_instance.create.return_value = mock_cable
                
                response = client.post("/api/v1/electrical/cable-routes", json=cable_data)
                assert response.status_code == 201
            
            # Step 4: Verify system integrity
            mock_node_instance.get_by_project_id.return_value = [mock_main_panel] + [
                MagicMock(id=i+2, name=f"Sub Panel {i+1}") for i in range(3)
            ]
            mock_node_instance.count_by_project.return_value = 4
            
            response = client.get(f"/api/v1/electrical/nodes?project_id={sample_project.id}")
            assert response.status_code == 200
            nodes = response.json()["items"]
            assert len(nodes) == 4  # 1 main + 3 sub panels

    def test_electrical_calculations_integration(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test integration of electrical calculations with system data."""
        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance
            
            # Test cable sizing calculation
            calc_data = {
                "load_current_amps": 125.0,
                "cable_length_meters": 150.0,
                "voltage_level": 480.0,
                "installation_method": "conduit",
                "ambient_temperature_c": 30.0,
                "conductor_material": "copper",
            }
            
            mock_result = {
                "recommended_conductor_size": "4/0 AWG",
                "calculated_voltage_drop_percent": 2.1,
                "ampacity_rating": 125.0,
                "meets_requirements": True,
                "calculation_details": {
                    "resistance_per_meter": 0.000164,
                    "total_resistance": 0.0246,
                    "voltage_drop_volts": 10.08,
                },
            }
            mock_instance.calculate_cable_sizing.return_value = mock_result
            
            response = client.post(
                "/api/v1/electrical/calculations/cable-sizing", json=calc_data
            )
            assert response.status_code == 200
            result = response.json()
            assert result["meets_requirements"] is True
            assert "calculation_details" in result

    def test_electrical_system_validation_workflow(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test complete electrical system validation workflow."""
        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_node_repo, \
             patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            
            # Setup mocks
            mock_node_instance = MagicMock()
            mock_node_repo.return_value = mock_node_instance
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Step 1: Create electrical nodes
            nodes_data = [
                {
                    "project_id": sample_project.id,
                    "name": "Source Node",
                    "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                    "voltage_v": 480.0,
                    "power_capacity_kva": 1000.0,
                },
                {
                    "project_id": sample_project.id,
                    "name": "Load Node",
                    "node_type": ElectricalNodeType.LOAD_CENTER.value,
                    "voltage_v": 480.0,
                    "power_capacity_kva": 200.0,
                },
            ]
            
            created_nodes = []
            for i, node_data in enumerate(nodes_data):
                mock_node = MagicMock()
                mock_node.id = i + 1
                mock_node.name = node_data["name"]
                mock_node_instance.create.return_value = mock_node
                
                response = client.post("/api/v1/electrical/nodes", json=node_data)
                assert response.status_code == 201
                created_nodes.append(response.json())
            
            # Step 2: Validate system configuration
            mock_validation_result = {
                "is_valid": True,
                "warnings": [],
                "errors": [],
                "recommendations": [
                    "Consider adding surge protection",
                    "Verify grounding requirements",
                ],
            }
            mock_service_instance.validate_electrical_system.return_value = mock_validation_result
            
            validation_data = {
                "project_id": sample_project.id,
                "check_voltage_compatibility": True,
                "check_power_balance": True,
                "check_safety_requirements": True,
            }
            
            response = client.post(
                "/api/v1/electrical/validation/system", json=validation_data
            )
            assert response.status_code == 200
            validation = response.json()
            assert validation["is_valid"] is True
            assert len(validation["recommendations"]) > 0

    def test_electrical_load_analysis_integration(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test electrical load analysis integration."""
        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance
            
            # Mock load analysis result
            mock_analysis = {
                "total_connected_load_kva": 1500.0,
                "demand_load_kva": 1200.0,
                "diversity_factor": 0.8,
                "load_breakdown": {
                    "lighting": 300.0,
                    "motors": 600.0,
                    "hvac": 400.0,
                    "other": 200.0,
                },
                "peak_demand_time": "14:00",
                "load_factor": 0.75,
                "recommendations": [
                    "Consider load balancing across phases",
                    "Monitor peak demand periods",
                ],
            }
            mock_instance.analyze_electrical_loads.return_value = mock_analysis
            
            analysis_data = {
                "project_id": sample_project.id,
                "include_future_loads": True,
                "diversity_factors": {
                    "lighting": 0.9,
                    "motors": 0.8,
                    "hvac": 0.85,
                },
            }
            
            response = client.post(
                "/api/v1/electrical/analysis/loads", json=analysis_data
            )
            assert response.status_code == 200
            analysis = response.json()
            assert analysis["total_connected_load_kva"] > 0
            assert "load_breakdown" in analysis
            assert len(analysis["recommendations"]) > 0


class TestCrossModuleIntegration:
    """Test integration between different modules."""

    def test_electrical_component_integration(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test integration between electrical and component modules."""
        with patch("api.v1.component_routes.get_component_service") as mock_comp_service, \
             patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_elec_repo:
            
            # Setup component service mock
            mock_comp_instance = MagicMock()
            mock_comp_service.return_value = mock_comp_instance
            
            # Setup electrical repository mock
            mock_elec_instance = MagicMock()
            mock_elec_repo.return_value = mock_elec_instance
            
            # Step 1: Create electrical component
            component_data = {
                "name": "480V Circuit Breaker",
                "category_id": 1,
                "manufacturer": "Schneider Electric",
                "model": "QO3100VH",
                "specific_data": json.dumps({
                    "voltage_rating": 480,
                    "current_rating": 100,
                    "interrupt_rating": 10000,
                    "poles": 3,
                }),
            }
            
            mock_component = MagicMock()
            mock_component.id = 1
            mock_component.name = "480V Circuit Breaker"
            mock_comp_instance.create_component.return_value = mock_component
            
            response = client.post("/api/v1/components/", json=component_data)
            assert response.status_code == 201
            component_id = response.json()["id"]
            
            # Step 2: Create electrical node using the component
            node_data = {
                "project_id": sample_project.id,
                "name": "Breaker Panel A",
                "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                "voltage_v": 480.0,
                "power_capacity_kva": 500.0,
                "component_id": component_id,
            }
            
            mock_node = MagicMock()
            mock_node.id = 1
            mock_node.name = "Breaker Panel A"
            mock_node.component_id = component_id
            mock_elec_instance.create.return_value = mock_node
            
            response = client.post("/api/v1/electrical/nodes", json=node_data)
            assert response.status_code == 201
            
            # Step 3: Verify component is linked to electrical node
            node_response = response.json()
            assert node_response["component_id"] == component_id

    def test_project_electrical_integration(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test integration between project and electrical modules."""
        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo, \
             patch("api.v1.project_routes.get_project_service") as mock_proj_service:
            
            # Setup mocks
            mock_elec_instance = MagicMock()
            mock_repo.return_value = mock_elec_instance
            mock_proj_instance = MagicMock()
            mock_proj_service.return_value = mock_proj_instance
            
            # Create electrical nodes for the project
            nodes_data = [
                {
                    "project_id": sample_project.id,
                    "name": f"Node {i+1}",
                    "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                    "voltage_v": 480.0,
                }
                for i in range(3)
            ]
            
            created_nodes = []
            for i, node_data in enumerate(nodes_data):
                mock_node = MagicMock()
                mock_node.id = i + 1
                mock_node.name = node_data["name"]
                mock_node.project_id = sample_project.id
                mock_elec_instance.create.return_value = mock_node
                
                response = client.post("/api/v1/electrical/nodes", json=node_data)
                assert response.status_code == 201
                created_nodes.append(response.json())
            
            # Verify project has electrical nodes
            mock_elec_instance.get_by_project_id.return_value = [
                MagicMock(id=i+1, name=f"Node {i+1}", project_id=sample_project.id)
                for i in range(3)
            ]
            mock_elec_instance.count_by_project.return_value = 3
            
            response = client.get(f"/api/v1/electrical/nodes?project_id={sample_project.id}")
            assert response.status_code == 200
            nodes = response.json()["items"]
            assert len(nodes) == 3
            assert all(node["project_id"] == sample_project.id for node in nodes)
