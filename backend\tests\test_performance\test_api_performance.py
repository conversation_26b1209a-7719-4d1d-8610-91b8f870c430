# backend/tests/test_performance/test_api_performance.py
"""
Performance Benchmark Tests for API Endpoints

This module provides performance testing for API endpoints to ensure
they meet response time requirements and handle load appropriately.
"""

import pytest
import time
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from core.models.project import Project

pytestmark = [
    pytest.mark.performance,
    pytest.mark.api,
    pytest.mark.benchmark,
]


class TestAPIPerformance:
    """Performance tests for API endpoints."""

    def test_electrical_node_creation_performance(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        performance_monitor,
    ):
        """Test electrical node creation performance."""
        from core.models.enums import ElectricalNodeType
        
        node_data = {
            "project_id": sample_project.id,
            "name": "Performance Test Node",
            "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
            "voltage_v": 480.0,
            "power_capacity_kva": 1000.0,
        }

        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance
            
            mock_node = MagicMock()
            mock_node.id = 1
            mock_node.name = node_data["name"]
            mock_instance.create.return_value = mock_node

            performance_monitor.start()
            response = client.post("/api/v1/electrical/nodes", json=node_data)
            performance_monitor.stop()

            assert response.status_code == 201
            
            # Performance assertions
            execution_time = performance_monitor.execution_time
            assert execution_time < 1.0, f"Node creation took {execution_time:.3f}s, expected < 1.0s"

    def test_electrical_node_list_performance(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        performance_monitor,
        mock_electrical_nodes,
    ):
        """Test electrical node listing performance with large dataset."""
        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance
            
            # Simulate large dataset
            mock_instance.get_by_project_id.return_value = mock_electrical_nodes * 100  # 500 nodes
            mock_instance.count_by_project.return_value = 500

            performance_monitor.start()
            response = client.get(
                f"/api/v1/electrical/nodes?project_id={sample_project.id}&skip=0&limit=50"
            )
            performance_monitor.stop()

            assert response.status_code == 200
            
            # Performance assertions
            execution_time = performance_monitor.execution_time
            assert execution_time < 2.0, f"Node listing took {execution_time:.3f}s, expected < 2.0s"

    def test_component_search_performance(
        self,
        client: TestClient,
        db_session: Session,
        performance_monitor,
        mock_components,
    ):
        """Test component search performance."""
        with patch("api.v1.component_routes.get_component_service") as mock_service:
            mock_service_instance = MagicMock()
            mock_service.return_value = mock_service_instance
            
            # Mock large search results
            mock_response = MagicMock()
            mock_response.components = mock_components * 50  # 250 components
            mock_response.total = 250
            mock_service_instance.get_components_list.return_value = mock_response

            performance_monitor.start()
            response = client.get(
                "/api/v1/components/?search=motor&page=1&per_page=20"
            )
            performance_monitor.stop()

            assert response.status_code == 200
            
            # Performance assertions
            execution_time = performance_monitor.execution_time
            assert execution_time < 1.5, f"Component search took {execution_time:.3f}s, expected < 1.5s"

    def test_cable_sizing_calculation_performance(
        self,
        client: TestClient,
        db_session: Session,
        performance_monitor,
    ):
        """Test cable sizing calculation performance."""
        calc_data = {
            "load_current_amps": 100.0,
            "cable_length_meters": 150.0,
            "voltage_level": 480.0,
            "installation_method": "conduit",
            "ambient_temperature_c": 30.0,
            "conductor_material": "copper",
            "insulation_type": "THWN",
            "voltage_drop_limit_percent": 3.0,
            "safety_factor": 1.25,
        }

        with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
            mock_instance = MagicMock()
            mock_service.return_value = mock_instance
            
            mock_result = {
                "recommended_conductor_size": "4/0 AWG",
                "calculated_voltage_drop_percent": 2.1,
                "meets_requirements": True,
            }
            mock_instance.calculate_cable_sizing.return_value = mock_result

            performance_monitor.start()
            response = client.post(
                "/api/v1/electrical/calculations/cable-sizing", json=calc_data
            )
            performance_monitor.stop()

            assert response.status_code == 200
            
            # Performance assertions
            execution_time = performance_monitor.execution_time
            assert execution_time < 0.5, f"Calculation took {execution_time:.3f}s, expected < 0.5s"

    @pytest.mark.parametrize("concurrent_requests", [5, 10, 20])
    def test_concurrent_request_performance(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        concurrent_requests: int,
    ):
        """Test API performance under concurrent load."""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            start_time = time.time()
            try:
                with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
                    mock_instance = MagicMock()
                    mock_repo.return_value = mock_instance
                    mock_instance.get_by_project_id.return_value = []
                    mock_instance.count_by_project.return_value = 0

                    response = client.get(
                        f"/api/v1/electrical/nodes?project_id={sample_project.id}"
                    )
                    end_time = time.time()
                    
                    results.put({
                        "status_code": response.status_code,
                        "response_time": end_time - start_time,
                        "success": response.status_code == 200,
                    })
            except Exception as e:
                results.put({
                    "status_code": 500,
                    "response_time": time.time() - start_time,
                    "success": False,
                    "error": str(e),
                })

        # Create and start threads
        threads = []
        start_time = time.time()
        
        for _ in range(concurrent_requests):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time

        # Collect results
        response_times = []
        success_count = 0
        
        while not results.empty():
            result = results.get()
            response_times.append(result["response_time"])
            if result["success"]:
                success_count += 1

        # Performance assertions
        assert success_count == concurrent_requests, f"Only {success_count}/{concurrent_requests} requests succeeded"
        
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        assert avg_response_time < 2.0, f"Average response time {avg_response_time:.3f}s too high"
        assert max_response_time < 5.0, f"Max response time {max_response_time:.3f}s too high"
        assert total_time < 10.0, f"Total execution time {total_time:.3f}s too high"

    def test_memory_usage_during_large_operations(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        performance_monitor,
    ):
        """Test memory usage during large data operations."""
        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance
            
            # Create large mock dataset
            large_dataset = []
            for i in range(1000):
                mock_node = MagicMock()
                mock_node.id = i
                mock_node.name = f"Node_{i}"
                mock_node.project_id = sample_project.id
                large_dataset.append(mock_node)
            
            mock_instance.get_by_project_id.return_value = large_dataset
            mock_instance.count_by_project.return_value = 1000

            performance_monitor.start()
            response = client.get(
                f"/api/v1/electrical/nodes?project_id={sample_project.id}&skip=0&limit=1000"
            )
            performance_monitor.stop()

            assert response.status_code == 200
            
            # Memory usage assertions
            memory_usage = performance_monitor.memory_usage
            if memory_usage:
                # Memory usage should be reasonable (less than 50MB for this operation)
                assert memory_usage < 50 * 1024 * 1024, f"Memory usage {memory_usage / 1024 / 1024:.1f}MB too high"


class TestDatabasePerformance:
    """Performance tests for database operations."""

    def test_bulk_insert_performance(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        performance_monitor,
    ):
        """Test performance of bulk insert operations."""
        from core.models.enums import ElectricalNodeType
        
        # Prepare bulk data
        bulk_data = []
        for i in range(100):
            node_data = {
                "project_id": sample_project.id,
                "name": f"Bulk_Node_{i}",
                "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                "voltage_v": 480.0,
            }
            bulk_data.append(node_data)

        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance
            
            # Mock bulk creation
            mock_nodes = [MagicMock(id=i, name=f"Bulk_Node_{i}") for i in range(100)]
            mock_instance.create.side_effect = mock_nodes

            performance_monitor.start()
            
            # Simulate bulk creation by making multiple requests
            responses = []
            for data in bulk_data[:10]:  # Test with smaller subset for API
                response = client.post("/api/v1/electrical/nodes", json=data)
                responses.append(response)
            
            performance_monitor.stop()

            # All requests should succeed
            assert all(r.status_code == 201 for r in responses)
            
            # Performance assertion
            execution_time = performance_monitor.execution_time
            avg_time_per_request = execution_time / len(responses)
            assert avg_time_per_request < 0.5, f"Average time per request {avg_time_per_request:.3f}s too high"

    def test_complex_query_performance(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
        performance_monitor,
    ):
        """Test performance of complex database queries."""
        with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
            mock_instance = MagicMock()
            mock_repo.return_value = mock_instance
            
            # Mock complex query result
            mock_instance.get_by_project_id.return_value = []
            mock_instance.count_by_project.return_value = 0

            performance_monitor.start()
            
            # Make request with complex filtering
            response = client.get(
                f"/api/v1/electrical/nodes?project_id={sample_project.id}"
                "&skip=0&limit=50&sort=name&order=asc"
            )
            
            performance_monitor.stop()

            assert response.status_code == 200
            
            # Performance assertion
            execution_time = performance_monitor.execution_time
            assert execution_time < 1.0, f"Complex query took {execution_time:.3f}s, expected < 1.0s"
