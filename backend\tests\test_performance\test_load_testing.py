# backend/tests/test_performance/test_load_testing.py
"""
Advanced Load Testing for Ultimate Electrical Designer Backend

This module provides comprehensive load testing capabilities to ensure
the system can handle realistic production workloads and stress conditions.
"""

import pytest
import asyncio
import time
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from core.models.project import Project

pytestmark = [
    pytest.mark.performance,
    pytest.mark.load_testing,
    pytest.mark.stress_testing,
]


class LoadTestMetrics:
    """Collect and analyze load test metrics."""
    
    def __init__(self):
        self.response_times = []
        self.success_count = 0
        self.error_count = 0
        self.errors = []
        self.start_time = None
        self.end_time = None
    
    def add_result(self, response_time: float, success: bool, error: str = None):
        """Add a test result."""
        self.response_times.append(response_time)
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
            if error:
                self.errors.append(error)
    
    def start_test(self):
        """Mark test start time."""
        self.start_time = time.time()
    
    def end_test(self):
        """Mark test end time."""
        self.end_time = time.time()
    
    @property
    def total_requests(self):
        return self.success_count + self.error_count
    
    @property
    def success_rate(self):
        if self.total_requests == 0:
            return 0
        return (self.success_count / self.total_requests) * 100
    
    @property
    def average_response_time(self):
        if not self.response_times:
            return 0
        return sum(self.response_times) / len(self.response_times)
    
    @property
    def max_response_time(self):
        return max(self.response_times) if self.response_times else 0
    
    @property
    def min_response_time(self):
        return min(self.response_times) if self.response_times else 0
    
    @property
    def total_duration(self):
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return 0
    
    @property
    def requests_per_second(self):
        if self.total_duration > 0:
            return self.total_requests / self.total_duration
        return 0
    
    def get_percentile(self, percentile: float):
        """Get response time percentile."""
        if not self.response_times:
            return 0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * percentile / 100)
        return sorted_times[min(index, len(sorted_times) - 1)]


class TestAdvancedLoadTesting:
    """Advanced load testing scenarios."""

    def test_electrical_nodes_concurrent_creation(
        self,
        client: TestClient,
        db_session: Session,
        sample_project: Project,
    ):
        """Test concurrent creation of electrical nodes."""
        from core.models.enums import ElectricalNodeType
        
        metrics = LoadTestMetrics()
        concurrent_users = 10
        requests_per_user = 5
        
        def create_electrical_node(user_id: int, request_id: int):
            """Create an electrical node for load testing."""
            start_time = time.time()
            try:
                with patch("api.v1.electrical_routes.ElectricalNodeRepository") as mock_repo:
                    mock_instance = MagicMock()
                    mock_repo.return_value = mock_instance
                    
                    mock_node = MagicMock()
                    mock_node.id = user_id * 100 + request_id
                    mock_node.name = f"Load_Test_Node_{user_id}_{request_id}"
                    mock_instance.create.return_value = mock_node
                    
                    node_data = {
                        "project_id": sample_project.id,
                        "name": f"Load_Test_Node_{user_id}_{request_id}",
                        "node_type": ElectricalNodeType.SWITCHBOARD_INCOMING.value,
                        "voltage_v": 480.0,
                        "power_capacity_kva": 1000.0,
                    }
                    
                    response = client.post("/api/v1/electrical/nodes", json=node_data)
                    end_time = time.time()
                    
                    success = response.status_code == 201
                    error = None if success else f"HTTP {response.status_code}"
                    
                    return {
                        "response_time": end_time - start_time,
                        "success": success,
                        "error": error,
                        "user_id": user_id,
                        "request_id": request_id,
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    "response_time": end_time - start_time,
                    "success": False,
                    "error": str(e),
                    "user_id": user_id,
                    "request_id": request_id,
                }
        
        # Execute load test
        metrics.start_test()
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = []
            
            for user_id in range(concurrent_users):
                for request_id in range(requests_per_user):
                    future = executor.submit(create_electrical_node, user_id, request_id)
                    futures.append(future)
            
            # Collect results
            for future in as_completed(futures):
                result = future.result()
                metrics.add_result(
                    result["response_time"],
                    result["success"],
                    result["error"]
                )
        
        metrics.end_test()
        
        # Performance assertions
        assert metrics.success_rate >= 95, f"Success rate {metrics.success_rate:.1f}% too low"
        assert metrics.average_response_time < 2.0, f"Average response time {metrics.average_response_time:.3f}s too high"
        assert metrics.max_response_time < 5.0, f"Max response time {metrics.max_response_time:.3f}s too high"
        assert metrics.requests_per_second > 5, f"Throughput {metrics.requests_per_second:.1f} req/s too low"
        
        # Print detailed metrics
        print(f"\n📊 Load Test Results:")
        print(f"   Total Requests: {metrics.total_requests}")
        print(f"   Success Rate: {metrics.success_rate:.1f}%")
        print(f"   Average Response Time: {metrics.average_response_time:.3f}s")
        print(f"   95th Percentile: {metrics.get_percentile(95):.3f}s")
        print(f"   Max Response Time: {metrics.max_response_time:.3f}s")
        print(f"   Throughput: {metrics.requests_per_second:.1f} req/s")

    def test_component_search_load(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test component search under load."""
        metrics = LoadTestMetrics()
        concurrent_users = 15
        search_terms = [
            "motor", "breaker", "transformer", "switch", "relay",
            "cable", "panel", "meter", "sensor", "controller"
        ]
        
        def search_components(user_id: int):
            """Perform component search for load testing."""
            start_time = time.time()
            try:
                with patch("api.v1.component_routes.get_component_service") as mock_service:
                    mock_service_instance = MagicMock()
                    mock_service.return_value = mock_service_instance
                    
                    # Mock large search results
                    mock_response = MagicMock()
                    mock_response.components = [MagicMock(id=i, name=f"Component_{i}") for i in range(50)]
                    mock_response.total = 50
                    mock_service_instance.get_components_list.return_value = mock_response
                    
                    search_term = search_terms[user_id % len(search_terms)]
                    response = client.get(f"/api/v1/components/?search={search_term}&page=1&per_page=20")
                    
                    end_time = time.time()
                    success = response.status_code == 200
                    error = None if success else f"HTTP {response.status_code}"
                    
                    return {
                        "response_time": end_time - start_time,
                        "success": success,
                        "error": error,
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    "response_time": end_time - start_time,
                    "success": False,
                    "error": str(e),
                }
        
        # Execute load test
        metrics.start_test()
        
        with ThreadPoolExecutor(max_workers=concurrent_users) as executor:
            futures = [executor.submit(search_components, i) for i in range(concurrent_users * 3)]
            
            for future in as_completed(futures):
                result = future.result()
                metrics.add_result(
                    result["response_time"],
                    result["success"],
                    result["error"]
                )
        
        metrics.end_test()
        
        # Performance assertions
        assert metrics.success_rate >= 98, f"Search success rate {metrics.success_rate:.1f}% too low"
        assert metrics.average_response_time < 1.5, f"Search response time {metrics.average_response_time:.3f}s too high"

    def test_calculation_engine_stress(
        self,
        client: TestClient,
        db_session: Session,
    ):
        """Test calculation engine under stress."""
        metrics = LoadTestMetrics()
        concurrent_calculations = 20
        
        def perform_calculation(calc_id: int):
            """Perform electrical calculation for stress testing."""
            start_time = time.time()
            try:
                with patch("api.v1.electrical_routes.ElectricalService") as mock_service:
                    mock_instance = MagicMock()
                    mock_service.return_value = mock_instance
                    
                    # Mock complex calculation result
                    mock_result = {
                        "recommended_conductor_size": "4/0 AWG",
                        "calculated_voltage_drop_percent": 2.1,
                        "meets_requirements": True,
                        "calculation_details": {
                            "resistance_per_meter": 0.000164,
                            "total_resistance": 0.0246,
                            "voltage_drop_volts": 10.08,
                        },
                    }
                    mock_instance.calculate_cable_sizing.return_value = mock_result
                    
                    calc_data = {
                        "load_current_amps": 100.0 + (calc_id % 50),
                        "cable_length_meters": 150.0 + (calc_id % 100),
                        "voltage_level": 480.0,
                        "installation_method": "conduit",
                        "ambient_temperature_c": 30.0,
                        "conductor_material": "copper",
                    }
                    
                    response = client.post(
                        "/api/v1/electrical/calculations/cable-sizing", 
                        json=calc_data
                    )
                    
                    end_time = time.time()
                    success = response.status_code == 200
                    error = None if success else f"HTTP {response.status_code}"
                    
                    return {
                        "response_time": end_time - start_time,
                        "success": success,
                        "error": error,
                    }
            except Exception as e:
                end_time = time.time()
                return {
                    "response_time": end_time - start_time,
                    "success": False,
                    "error": str(e),
                }
        
        # Execute stress test
        metrics.start_test()
        
        with ThreadPoolExecutor(max_workers=concurrent_calculations) as executor:
            futures = [executor.submit(perform_calculation, i) for i in range(concurrent_calculations * 2)]
            
            for future in as_completed(futures):
                result = future.result()
                metrics.add_result(
                    result["response_time"],
                    result["success"],
                    result["error"]
                )
        
        metrics.end_test()
        
        # Performance assertions for calculations
        assert metrics.success_rate >= 99, f"Calculation success rate {metrics.success_rate:.1f}% too low"
        assert metrics.average_response_time < 0.8, f"Calculation time {metrics.average_response_time:.3f}s too high"
        assert metrics.get_percentile(99) < 2.0, f"99th percentile {metrics.get_percentile(99):.3f}s too high"
