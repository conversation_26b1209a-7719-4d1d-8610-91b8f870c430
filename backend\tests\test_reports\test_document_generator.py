# backend/tests/test_reports/test_document_generator.py
"""
Tests for Document Generator.

Tests the document generation functionality including
PDF, HTML, and Excel report generation.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch
from pathlib import Path

from core.reports.document_generator import DocumentGenerator
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import SAMPLE_REPORT_DATA


class TestDocumentGenerator:
    """Test suite for DocumentGenerator."""

    def setup_method(self):
        """Set up test fixtures."""
        self.generator = DocumentGenerator()
        self.sample_data = SAMPLE_REPORT_DATA

    def test_initialization(self):
        """Test generator initialization."""
        assert self.generator is not None
        assert hasattr(self.generator, 'generate_heat_tracing_calculation_report')
        assert hasattr(self.generator, 'generate_cable_schedule_report')

    def test_generate_pdf_report_valid_input(self):
        """Test PDF report generation with valid input."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "test_report.pdf")
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="pdf",
                output_path=output_path
            )
            
            assert result_path == output_path
            assert os.path.exists(result_path)
            assert os.path.getsize(result_path) > 0

    def test_generate_html_report_valid_input(self):
        """Test HTML report generation with valid input."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "test_report.html")
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="html",
                output_path=output_path
            )
            
            assert result_path == output_path
            assert os.path.exists(result_path)
            
            # Check HTML content
            with open(result_path, 'r', encoding='utf-8') as f:
                content = f.read()
                assert "<html>" in content
                assert self.sample_data["project_data"]["project_name"] in content

    def test_generate_excel_report_valid_input(self):
        """Test Excel report generation with valid input."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "test_report.xlsx")
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="excel",
                output_path=output_path
            )
            
            assert result_path == output_path
            assert os.path.exists(result_path)
            assert os.path.getsize(result_path) > 0

    def test_generate_cable_schedule_report(self):
        """Test cable schedule report generation."""
        cable_data = [
            {
                "cable_id": "CB-001",
                "cable_type": "Self-regulating",
                "power_output": "25 W/m",
                "voltage": "240V",
                "length": "100m"
            }
        ]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "cable_schedule.xlsx")
            
            result_path = self.generator.generate_cable_schedule_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                cable_data=cable_data,
                output_format="excel",
                output_path=output_path
            )
            
            assert os.path.exists(result_path)

    def test_generate_dashboard_report(self):
        """Test dashboard report generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "dashboard.html")
            
            result_path = self.generator.generate_dashboard_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                output_path=output_path,
                include_charts=True
            )
            
            assert os.path.exists(result_path)
            
            # Check for dashboard elements
            with open(result_path, 'r', encoding='utf-8') as f:
                content = f.read()
                assert "dashboard" in content.lower()
                assert "chart" in content.lower()

    def test_generate_multi_format_report_package(self):
        """Test multi-format report package generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            formats = ["pdf", "html", "excel"]
            
            result_paths = self.generator.generate_multi_format_report_package(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_directory=temp_dir,
                formats=formats
            )
            
            assert len(result_paths) == len(formats)
            for format_type, file_path in result_paths.items():
                assert format_type in formats
                assert os.path.exists(file_path)

    def test_generate_report_with_custom_template(self):
        """Test report generation with custom template."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "custom_report.html")
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="html",
                output_path=output_path,
                template_name="detailed_report"
            )
            
            assert os.path.exists(result_path)

    def test_generate_report_invalid_output_format(self):
        """Test report generation with invalid output format."""
        with pytest.raises(InvalidInputError, match="Unsupported output format"):
            self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="invalid_format"
            )

    def test_generate_report_missing_required_data(self):
        """Test report generation with missing required data."""
        with pytest.raises(InvalidInputError, match="Missing required data"):
            self.generator.generate_heat_tracing_calculation_report(
                project_data={},  # Empty project data
                circuits_data=[],  # Empty circuits data
                calculations_data=[],
                design_parameters={}
            )

    def test_validate_report_data_valid(self):
        """Test report data validation with valid data."""
        validation_result = self.generator.validate_report_data(
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"]
        )
        
        assert validation_result["is_valid"] is True
        assert len(validation_result["errors"]) == 0

    def test_validate_report_data_invalid(self):
        """Test report data validation with invalid data."""
        invalid_project_data = {}  # Missing required fields
        invalid_circuits_data = [{"invalid": "data"}]
        
        validation_result = self.generator.validate_report_data(
            project_data=invalid_project_data,
            circuits_data=invalid_circuits_data,
            calculations_data=[]
        )
        
        assert validation_result["is_valid"] is False
        assert len(validation_result["errors"]) > 0

    def test_generate_report_with_charts(self):
        """Test report generation with charts and visualizations."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "report_with_charts.html")
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="html",
                output_path=output_path,
                include_charts=True
            )
            
            assert os.path.exists(result_path)
            
            # Check for chart elements
            with open(result_path, 'r', encoding='utf-8') as f:
                content = f.read()
                assert "chart" in content.lower() or "graph" in content.lower()

    def test_generate_report_with_images(self):
        """Test report generation with embedded images."""
        # Create a sample image file
        with tempfile.TemporaryDirectory() as temp_dir:
            image_path = os.path.join(temp_dir, "sample_image.png")
            
            # Create a simple PNG image (1x1 pixel)
            import base64
            png_data = base64.b64decode(
                "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAGA6VP8IQAAAABJRU5ErkJggg=="
            )
            with open(image_path, 'wb') as f:
                f.write(png_data)
            
            output_path = os.path.join(temp_dir, "report_with_images.html")
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="html",
                output_path=output_path,
                include_images=True,
                image_paths=[image_path]
            )
            
            assert os.path.exists(result_path)

    def test_generate_report_performance(self):
        """Test report generation performance with large dataset."""
        # Create large dataset
        large_circuits_data = []
        large_calculations_data = []
        
        for i in range(100):
            circuit = self.sample_data["circuits_data"][0].copy()
            circuit["circuit_id"] = f"HT-{i:03d}"
            large_circuits_data.append(circuit)
            
            calculation = self.sample_data["calculations_data"][0].copy()
            calculation["circuit_id"] = f"HT-{i:03d}"
            large_calculations_data.append(calculation)
        
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "large_report.html")
            
            import time
            start_time = time.time()
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=large_circuits_data,
                calculations_data=large_calculations_data,
                design_parameters={"safety_factor": 1.3},
                output_format="html",
                output_path=output_path
            )
            
            end_time = time.time()
            generation_time = end_time - start_time
            
            assert os.path.exists(result_path)
            # Should generate reasonably quickly
            assert generation_time < 30.0  # 30 seconds max

    def test_get_available_templates(self):
        """Test getting available report templates."""
        templates = self.generator.get_available_templates()
        
        assert isinstance(templates, list)
        assert len(templates) > 0
        
        # Check template structure
        template = templates[0]
        assert "template_id" in template
        assert "name" in template
        assert "description" in template
        assert "supported_formats" in template

    def test_generate_report_with_watermark(self):
        """Test report generation with watermark."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "watermarked_report.pdf")
            
            result_path = self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="pdf",
                output_path=output_path,
                watermark_text="DRAFT"
            )
            
            assert os.path.exists(result_path)

    @patch('core.reports.document_generator.logger')
    def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during generation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            output_path = os.path.join(temp_dir, "test_report.html")
            
            self.generator.generate_heat_tracing_calculation_report(
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="html",
                output_path=output_path
            )
            
            # Verify logging calls were made
            assert mock_logger.info.called
            assert mock_logger.debug.called
