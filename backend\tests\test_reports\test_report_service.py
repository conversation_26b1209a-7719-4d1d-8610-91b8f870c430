# backend/tests/test_reports/test_report_service.py
"""
Tests for Report Service.

Tests the main report service that orchestrates all reporting operations.
"""

import pytest
import tempfile
import os
from unittest.mock import Mock, patch
from pathlib import Path

from core.reports.report_service import ReportService
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import SAMPLE_REPORT_DATA


class TestReportService:
    """Test suite for ReportService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = ReportService()
        self.sample_data = SAMPLE_REPORT_DATA

    def test_initialization(self):
        """Test service initialization."""
        assert self.service is not None
        assert hasattr(self.service, 'generate_calculation_report')
        assert hasattr(self.service, 'generate_cable_schedule')
        assert hasattr(self.service, 'generate_dashboard')

    def test_generate_calculation_report_valid_input(self):
        """Test calculation report generation with valid input."""
        result = self.service.generate_calculation_report(
            project_id="test_project_001",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="pdf"
        )
        
        assert result["success"] is True
        assert result["report_type"] == "calculation_report"
        assert result["project_id"] == "test_project_001"
        assert result["output_format"] == "pdf"
        assert "file_path" in result
        assert os.path.exists(result["file_path"])

    def test_generate_calculation_report_html_format(self):
        """Test calculation report generation in HTML format."""
        result = self.service.generate_calculation_report(
            project_id="test_project_002",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="html"
        )
        
        assert result["success"] is True
        assert result["output_format"] == "html"
        assert result["file_path"].endswith(".html")

    def test_generate_calculation_report_excel_format(self):
        """Test calculation report generation in Excel format."""
        result = self.service.generate_calculation_report(
            project_id="test_project_003",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="excel"
        )
        
        assert result["success"] is True
        assert result["output_format"] == "excel"
        assert result["file_path"].endswith(".xlsx")

    def test_generate_cable_schedule_valid_input(self):
        """Test cable schedule generation with valid input."""
        cable_data = [
            {
                "cable_id": "CB-001",
                "cable_type": "Self-regulating",
                "power_output": "25 W/m",
                "voltage": "240V",
                "length": "100m"
            }
        ]
        
        result = self.service.generate_cable_schedule(
            project_id="test_project_004",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            cable_data=cable_data,
            output_format="excel"
        )
        
        assert result["success"] is True
        assert result["report_type"] == "cable_schedule"
        assert "file_path" in result

    def test_generate_dashboard_valid_input(self):
        """Test dashboard generation with valid input."""
        result = self.service.generate_dashboard(
            project_id="test_project_005",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            include_charts=True
        )
        
        assert result["success"] is True
        assert result["report_type"] == "dashboard"
        assert result["output_format"] == "html"
        assert result["include_charts"] is True

    def test_generate_report_package_valid_input(self):
        """Test report package generation with valid input."""
        result = self.service.generate_report_package(
            project_id="test_project_006",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            formats=["pdf", "html", "excel"],
            include_dashboard=True
        )
        
        assert result["success"] is True
        assert result["report_type"] == "report_package"
        assert "generated_files" in result
        assert len(result["generated_files"]) >= 3  # At least 3 formats

    def test_export_data_csv_format(self):
        """Test data export in CSV format."""
        export_data = {
            "circuits": self.sample_data["circuits_data"],
            "calculations": self.sample_data["calculations_data"]
        }
        
        result = self.service.export_data(
            project_id="test_project_007",
            data=export_data,
            export_type="calculation_report",
            format_type="csv"
        )
        
        assert result["success"] is True
        assert result["format"] == "csv"
        assert result["file_path"].endswith(".csv")
        assert os.path.exists(result["file_path"])

    def test_export_data_json_format(self):
        """Test data export in JSON format."""
        export_data = {
            "circuits": self.sample_data["circuits_data"],
            "calculations": self.sample_data["calculations_data"]
        }
        
        result = self.service.export_data(
            project_id="test_project_008",
            data=export_data,
            export_type="calculation_report",
            format_type="json"
        )
        
        assert result["success"] is True
        assert result["format"] == "json"
        assert result["file_path"].endswith(".json")

    def test_export_data_excel_format(self):
        """Test data export in Excel format."""
        export_data = {
            "circuits": self.sample_data["circuits_data"],
            "calculations": self.sample_data["calculations_data"]
        }
        
        result = self.service.export_data(
            project_id="test_project_009",
            data=export_data,
            export_type="calculation_report",
            format_type="excel"
        )
        
        assert result["success"] is True
        assert result["format"] == "excel"
        assert result["file_path"].endswith(".xlsx")

    def test_generate_calculation_report_with_caching(self):
        """Test calculation report generation with caching."""
        # First generation
        result1 = self.service.generate_calculation_report(
            project_id="test_project_cache",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="pdf",
            use_cache=True
        )
        
        # Second generation with same data (should use cache)
        result2 = self.service.generate_calculation_report(
            project_id="test_project_cache",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="pdf",
            use_cache=True
        )
        
        assert result1["success"] is True
        assert result2["success"] is True
        # Cache keys should be the same
        assert result1["cache_key"] == result2["cache_key"]

    def test_generate_calculation_report_invalid_format(self):
        """Test calculation report generation with invalid format."""
        with pytest.raises(CalculationError):
            self.service.generate_calculation_report(
                project_id="test_project_invalid",
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="invalid_format"
            )

    def test_generate_calculation_report_missing_data(self):
        """Test calculation report generation with missing data."""
        with pytest.raises(CalculationError):
            self.service.generate_calculation_report(
                project_id="test_project_missing",
                project_data={},  # Empty project data
                circuits_data=[],  # Empty circuits data
                calculations_data=[],
                design_parameters={},
                output_format="pdf"
            )

    def test_export_data_invalid_format(self):
        """Test data export with invalid format."""
        export_data = {"circuits": self.sample_data["circuits_data"]}
        
        with pytest.raises(CalculationError):
            self.service.export_data(
                project_id="test_project_invalid_export",
                data=export_data,
                export_type="calculation_report",
                format_type="invalid_format"
            )

    def test_export_data_with_filters(self):
        """Test data export with filters."""
        export_data = {
            "circuits": self.sample_data["circuits_data"],
            "calculations": self.sample_data["calculations_data"]
        }
        
        filters = {
            "circuit_id": "HT-001",
            "power_range": {"min": 1000, "max": 5000}
        }
        
        result = self.service.export_data(
            project_id="test_project_filtered",
            data=export_data,
            export_type="calculation_report",
            format_type="json",
            filters=filters
        )
        
        assert result["success"] is True
        assert result["filters_applied"] is True

    def test_get_generation_history(self):
        """Test getting generation history."""
        # Generate a few reports first
        for i in range(3):
            self.service.generate_calculation_report(
                project_id=f"test_project_history_{i}",
                project_data=self.sample_data["project_data"],
                circuits_data=self.sample_data["circuits_data"],
                calculations_data=self.sample_data["calculations_data"],
                design_parameters={"safety_factor": 1.3},
                output_format="pdf"
            )
        
        history = self.service.get_generation_history()
        
        assert isinstance(history, list)
        assert len(history) >= 3

    def test_get_generation_history_filtered(self):
        """Test getting filtered generation history."""
        # Generate reports for specific project
        project_id = "test_project_specific"
        self.service.generate_calculation_report(
            project_id=project_id,
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="pdf"
        )
        
        history = self.service.get_generation_history(
            project_id=project_id,
            report_type="calculation_report",
            limit=10
        )
        
        assert isinstance(history, list)
        # All entries should be for the specific project
        for entry in history:
            assert entry.get("project_id") == project_id

    def test_clear_cache_all(self):
        """Test clearing all cache."""
        # Generate some reports to populate cache
        self.service.generate_calculation_report(
            project_id="test_project_cache_clear",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="pdf",
            use_cache=True
        )
        
        cleared_count = self.service.clear_cache()
        
        assert isinstance(cleared_count, int)
        assert cleared_count >= 0

    def test_clear_cache_specific_project(self):
        """Test clearing cache for specific project."""
        project_id = "test_project_cache_specific"
        
        # Generate report to populate cache
        self.service.generate_calculation_report(
            project_id=project_id,
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="pdf",
            use_cache=True
        )
        
        cleared_count = self.service.clear_cache(project_id=project_id)
        
        assert isinstance(cleared_count, int)

    def test_get_available_templates(self):
        """Test getting available templates."""
        templates = self.service.get_available_templates()
        
        assert isinstance(templates, list)
        assert len(templates) > 0
        
        # Check template structure
        template = templates[0]
        assert "template_id" in template
        assert "name" in template
        assert "description" in template

    def test_generate_report_performance(self):
        """Test report generation performance."""
        import time
        
        start_time = time.time()
        
        result = self.service.generate_calculation_report(
            project_id="test_project_performance",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="html"
        )
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        assert result["success"] is True
        assert result["generation_time_seconds"] > 0
        # Should generate reasonably quickly
        assert generation_time < 30.0  # 30 seconds max

    @patch('core.reports.report_service.logger')
    def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during operations."""
        self.service.generate_calculation_report(
            project_id="test_project_logging",
            project_data=self.sample_data["project_data"],
            circuits_data=self.sample_data["circuits_data"],
            calculations_data=self.sample_data["calculations_data"],
            design_parameters={"safety_factor": 1.3},
            output_format="pdf"
        )
        
        # Verify logging calls were made
        assert mock_logger.info.called
        assert mock_logger.debug.called
