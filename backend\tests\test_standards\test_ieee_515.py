# backend/tests/test_standards/test_ieee_515.py
"""
Tests for IEEE 515 Standard.

Tests the IEEE 515-2017 standard implementation for electrical
resistance trace heating validation and calculations.
"""

import pytest
from unittest.mock import Mock, patch

from core.standards.ieee.ieee_515 import IEEE515
from core.standards.general.base_standard import Valida<PERSON><PERSON><PERSON><PERSON>, ComplianceLevel
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import SAMPLE_STANDARDS_DATA


class TestIEEE515:
    """Test suite for IEEE515 standard."""

    def setup_method(self):
        """Set up test fixtures."""
        self.standard = IEEE515()
        self.sample_design_data = SAMPLE_STANDARDS_DATA["design_data"]
        self.sample_input_data = SAMPLE_STANDARDS_DATA["input_data"]

    def test_initialization(self):
        """Test standard initialization."""
        assert self.standard is not None
        assert self.standard.standard_id == "IEEE-515-2017"
        assert self.standard.title.startswith("IEEE Standard for the Testing")
        assert self.standard.version == "2017"
        assert len(self.standard.rules) > 0
        assert len(self.standard.parameters) > 0

    def test_validate_design_valid_input(self):
        """Test design validation with valid input."""
        result = self.standard.validate_design(self.sample_design_data)
        
        assert isinstance(result, ValidationResult)
        assert result.compliance_level in [ComplianceLevel.COMPLIANT, ComplianceLevel.WARNING]
        assert len(result.applied_rules) > 0

    def test_validate_design_voltage_limits(self):
        """Test design validation for voltage limits."""
        # Test valid voltage
        valid_data = self.sample_design_data.copy()
        valid_data["voltage"] = 240
        result = self.standard.validate_design(valid_data)
        assert result.compliance_level != ComplianceLevel.NON_COMPLIANT
        
        # Test invalid voltage (too high)
        invalid_data = self.sample_design_data.copy()
        invalid_data["voltage"] = 1000  # Above IEEE 515 limit
        result = self.standard.validate_design(invalid_data)
        assert len(result.violations) > 0

    def test_validate_design_power_density_limits(self):
        """Test design validation for power density limits."""
        # Test valid power density
        valid_data = self.sample_design_data.copy()
        valid_data["power_density"] = 25.0
        result = self.standard.validate_design(valid_data)
        assert result.compliance_level != ComplianceLevel.NON_COMPLIANT
        
        # Test invalid power density (too high)
        invalid_data = self.sample_design_data.copy()
        invalid_data["power_density"] = 150.0  # Above IEEE 515 limit
        result = self.standard.validate_design(invalid_data)
        assert len(result.violations) > 0

    def test_validate_design_temperature_limits(self):
        """Test design validation for temperature limits."""
        # Test valid temperature
        valid_data = self.sample_design_data.copy()
        valid_data["maintain_temperature"] = 60.0
        result = self.standard.validate_design(valid_data)
        assert result.compliance_level != ComplianceLevel.NON_COMPLIANT
        
        # Test invalid temperature (too high)
        invalid_data = self.sample_design_data.copy()
        invalid_data["maintain_temperature"] = 300.0  # Above IEEE 515 limit
        result = self.standard.validate_design(invalid_data)
        assert len(result.violations) > 0

    def test_validate_design_hazardous_area(self):
        """Test design validation for hazardous area applications."""
        hazardous_data = self.sample_design_data.copy()
        hazardous_data["application_type"] = "hazardous_area"
        hazardous_data["hazardous_class"] = "Class I, Division 1"
        
        result = self.standard.validate_design(hazardous_data)
        
        # Should have additional rules applied for hazardous areas
        hazardous_rules = [rule for rule in result.applied_rules if "HAZARDOUS" in rule["rule_id"]]
        assert len(hazardous_rules) > 0

    def test_validate_design_missing_required_fields(self):
        """Test design validation with missing required fields."""
        incomplete_data = {
            "voltage": 240,
            # Missing other required fields
        }
        
        with pytest.raises(InvalidInputError, match="Missing required fields"):
            self.standard.validate_design(incomplete_data)

    def test_get_applicable_rules_standard_application(self):
        """Test getting applicable rules for standard application."""
        rules = self.standard.get_applicable_rules(self.sample_design_data)
        
        assert isinstance(rules, list)
        assert len(rules) > 0
        assert "IEEE515_VOLTAGE_LIMITS" in rules
        assert "IEEE515_POWER_DENSITY" in rules
        assert "IEEE515_TEMPERATURE_LIMITS" in rules

    def test_get_applicable_rules_hazardous_area(self):
        """Test getting applicable rules for hazardous area application."""
        hazardous_data = self.sample_design_data.copy()
        hazardous_data["application_type"] = "hazardous_area"
        
        rules = self.standard.get_applicable_rules(hazardous_data)
        
        assert "IEEE515_HAZARDOUS_AREA" in rules
        assert "IEEE515_EXPLOSION_PROOF" in rules

    def test_get_applicable_rules_outdoor_installation(self):
        """Test getting applicable rules for outdoor installation."""
        outdoor_data = self.sample_design_data.copy()
        outdoor_data["outdoor_installation"] = True
        
        rules = self.standard.get_applicable_rules(outdoor_data)
        
        assert "IEEE515_OUTDOOR_INSTALLATION" in rules

    def test_calculate_parameters_power_requirements(self):
        """Test parameter calculations for power requirements."""
        result = self.standard.calculate_parameters(self.sample_input_data)
        
        assert "ieee515_power_density" in result
        power_density = result["ieee515_power_density"]
        assert power_density["value"] > 0
        assert power_density["unit"] == "W/m"
        assert "safety factor" in power_density["description"]

    def test_calculate_parameters_electrical_parameters(self):
        """Test parameter calculations for electrical parameters."""
        electrical_input = {
            "voltage": 240,
            "power_per_meter": 32.5,
            "circuit_length": 100.0,
        }
        
        result = self.standard.calculate_parameters(electrical_input)
        
        assert "ieee515_operating_current" in result
        assert "ieee515_derated_current" in result
        
        operating_current = result["ieee515_operating_current"]["value"]
        derated_current = result["ieee515_derated_current"]["value"]
        assert derated_current > operating_current  # Derated should be higher

    def test_calculate_parameters_temperature_parameters(self):
        """Test parameter calculations for temperature parameters."""
        temperature_input = {
            "maintain_temperature": 60.0,
            "ambient_temperature": 0.0,
        }
        
        result = self.standard.calculate_parameters(temperature_input)
        
        assert "ieee515_temperature_differential" in result
        temp_diff = result["ieee515_temperature_differential"]
        assert temp_diff["value"] > 60.0  # Should include safety margin

    def test_calculate_parameters_cable_selection(self):
        """Test parameter calculations for cable selection."""
        cable_input = {
            "power_density": 25.0,
        }
        
        result = self.standard.calculate_parameters(cable_input)
        
        assert "ieee515_recommended_cable_type" in result
        cable_type = result["ieee515_recommended_cable_type"]["value"]
        assert cable_type in ["Self-regulating", "Constant wattage parallel", "Constant wattage series"]

    def test_calculate_parameters_missing_input(self):
        """Test parameter calculations with missing input data."""
        incomplete_input = {}
        
        result = self.standard.calculate_parameters(incomplete_input)
        
        # Should return empty result without errors
        assert isinstance(result, dict)

    def test_voltage_validation_standard_voltages(self):
        """Test voltage validation for standard voltages."""
        standard_voltages = [120, 208, 240, 277, 480, 600]
        
        for voltage in standard_voltages:
            test_data = self.sample_design_data.copy()
            test_data["voltage"] = voltage
            result = self.standard.validate_design(test_data)
            
            # Should not have voltage-related violations
            voltage_violations = [v for v in result.violations if "voltage" in v["description"].lower()]
            assert len(voltage_violations) == 0

    def test_voltage_validation_non_standard_voltages(self):
        """Test voltage validation for non-standard voltages."""
        non_standard_voltages = [110, 220, 380, 415]
        
        for voltage in non_standard_voltages:
            test_data = self.sample_design_data.copy()
            test_data["voltage"] = voltage
            result = self.standard.validate_design(test_data)
            
            # Should have warnings for non-standard voltages
            assert len(result.warnings) > 0

    def test_power_density_recommendations(self):
        """Test power density recommendations."""
        # Test high power density
        high_power_data = self.sample_design_data.copy()
        high_power_data["power_density"] = 60.0
        result = self.standard.validate_design(high_power_data)
        
        # Should have recommendations for high power density
        high_power_recommendations = [r for r in result.recommendations if "power density" in r["description"].lower()]
        assert len(high_power_recommendations) > 0
        
        # Test low power density
        low_power_data = self.sample_design_data.copy()
        low_power_data["power_density"] = 8.0
        result = self.standard.validate_design(low_power_data)
        
        # Should have recommendations for low power density
        low_power_recommendations = [r for r in result.recommendations if "power density" in r["description"].lower()]
        assert len(low_power_recommendations) > 0

    def test_safety_requirements_validation(self):
        """Test safety requirements validation."""
        safety_data = self.sample_design_data.copy()
        safety_data["application_type"] = "hazardous_area"
        # Missing hazardous class
        
        result = self.standard.validate_design(safety_data)
        
        # Should have violations for missing hazardous area classification
        safety_violations = [v for v in result.violations if "hazardous" in v["description"].lower()]
        assert len(safety_violations) > 0

    def test_installation_requirements_validation(self):
        """Test installation requirements validation."""
        buried_data = self.sample_design_data.copy()
        buried_data["installation_type"] = "buried"
        
        result = self.standard.validate_design(buried_data)
        
        # Should have recommendations for buried installation
        buried_recommendations = [r for r in result.recommendations if "buried" in r["description"].lower()]
        assert len(buried_recommendations) > 0

    def test_standard_info(self):
        """Test getting standard information."""
        info = self.standard.get_standard_info()
        
        assert info["standard_id"] == "IEEE-515-2017"
        assert info["title"] == self.standard.title
        assert info["version"] == "2017"
        assert info["standard_type"] == "ieee"
        assert info["rules_count"] > 0
        assert info["parameters_count"] > 0

    def test_get_all_rules(self):
        """Test getting all rules."""
        rules = self.standard.get_all_rules()
        
        assert isinstance(rules, dict)
        assert len(rules) > 0
        assert "IEEE515_VOLTAGE_LIMITS" in rules
        assert "IEEE515_POWER_DENSITY" in rules

    def test_get_all_parameters(self):
        """Test getting all parameters."""
        parameters = self.standard.get_all_parameters()
        
        assert isinstance(parameters, dict)
        assert len(parameters) > 0
        assert "power_safety_factor" in parameters
        assert "current_derating_factor" in parameters

    def test_get_specific_rule(self):
        """Test getting a specific rule."""
        rule = self.standard.get_rule("IEEE515_VOLTAGE_LIMITS")
        
        assert rule is not None
        assert "description" in rule
        assert "min_voltage" in rule
        assert "max_voltage" in rule

    def test_get_specific_parameter(self):
        """Test getting a specific parameter."""
        parameter = self.standard.get_parameter("power_safety_factor")
        
        assert parameter is not None
        assert "value" in parameter
        assert "unit" in parameter
        assert "description" in parameter

    @patch('core.standards.ieee.ieee_515.logger')
    def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during validation."""
        self.standard.validate_design(self.sample_design_data)
        
        # Verify logging calls were made
        assert mock_logger.info.called
