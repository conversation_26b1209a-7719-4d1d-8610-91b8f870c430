# backend/tests/test_standards/test_standards_service.py
"""
Tests for Standards Service.

Tests the main standards service that orchestrates all standards operations.
"""

import pytest
from unittest.mock import Mock, patch

from core.standards.standards_service import StandardsService
from core.standards.general.base_standard import Validation<PERSON><PERSON><PERSON>, ComplianceLevel
from core.errors.exceptions import InvalidInputError, CalculationError
from tests.fixtures.test_data import SAMPLE_STANDARDS_DATA


class TestStandardsService:
    """Test suite for StandardsService."""

    def setup_method(self):
        """Set up test fixtures."""
        self.service = StandardsService()
        self.sample_design_data = SAMPLE_STANDARDS_DATA["design_data"]
        self.sample_input_data = SAMPLE_STANDARDS_DATA["input_data"]

    def test_initialization(self):
        """Test service initialization."""
        assert self.service is not None
        assert hasattr(self.service, 'validate_design_against_standards')
        assert hasattr(self.service, 'calculate_standards_parameters')
        assert len(self.service.standards_registry) > 0
        assert len(self.service.standards_interfaces) > 0

    def test_get_available_standards(self):
        """Test getting available standards."""
        standards = self.service.get_available_standards()
        
        assert isinstance(standards, dict)
        assert "ieee" in standards
        assert "iec" in standards
        assert "api" in standards
        assert "nfpa" in standards
        assert "iso" in standards
        
        # Check IEEE standards
        ieee_standards = standards["ieee"]
        assert len(ieee_standards) > 0
        assert any(std["standard_id"] == "IEEE-515-2017" for std in ieee_standards)

    def test_validate_design_against_single_standard(self):
        """Test design validation against a single standard."""
        result = self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=["IEEE-515-2017"]
        )
        
        assert "validation_summary" in result
        assert "individual_results" in result
        assert "compliance_report" in result
        assert "IEEE-515-2017" in result["individual_results"]
        
        ieee_result = result["individual_results"]["IEEE-515-2017"]
        assert "is_compliant" in ieee_result
        assert "compliance_level" in ieee_result

    def test_validate_design_against_multiple_standards(self):
        """Test design validation against multiple standards."""
        standard_ids = ["IEEE-515-2017", "IEC-60079-30-1", "IEC-62395"]
        
        result = self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=standard_ids
        )
        
        assert len(result["individual_results"]) == len(standard_ids)
        assert result["validation_summary"]["total_standards_checked"] == len(standard_ids)

    def test_validate_design_against_all_standards(self):
        """Test design validation against all available standards."""
        result = self.service.validate_design_against_standards(
            design_data=self.sample_design_data
            # No standard_ids specified - should use all
        )
        
        assert len(result["individual_results"]) > 0
        assert result["validation_summary"]["total_standards_checked"] > 0

    def test_validate_design_with_validation_options(self):
        """Test design validation with validation options."""
        validation_options = {
            "strict_mode": True,
            "include_warnings": True,
            "detailed_analysis": True
        }
        
        result = self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=["IEEE-515-2017"],
            validation_options=validation_options
        )
        
        assert "validation_summary" in result
        assert "compliance_report" in result

    def test_calculate_standards_parameters_single_standard(self):
        """Test parameter calculations using a single standard."""
        result = self.service.calculate_standards_parameters(
            input_data=self.sample_input_data,
            standard_ids=["IEEE-515-2017"]
        )
        
        assert "calculation_summary" in result
        assert "individual_results" in result
        assert "consolidated_parameters" in result
        assert "IEEE-515-2017" in result["individual_results"]
        
        ieee_result = result["individual_results"]["IEEE-515-2017"]
        assert "success" in ieee_result
        assert "calculated_values" in ieee_result

    def test_calculate_standards_parameters_multiple_standards(self):
        """Test parameter calculations using multiple standards."""
        standard_ids = ["IEEE-515-2017", "IEC-62395"]
        
        result = self.service.calculate_standards_parameters(
            input_data=self.sample_input_data,
            standard_ids=standard_ids
        )
        
        assert len(result["individual_results"]) == len(standard_ids)
        assert result["calculation_summary"]["total_standards_used"] == len(standard_ids)

    def test_calculate_standards_parameters_with_options(self):
        """Test parameter calculations with calculation options."""
        calculation_options = {
            "use_conservative_values": True,
            "include_safety_margins": True,
            "detailed_calculations": True
        }
        
        result = self.service.calculate_standards_parameters(
            input_data=self.sample_input_data,
            standard_ids=["IEEE-515-2017"],
            calculation_options=calculation_options
        )
        
        assert "calculation_summary" in result
        assert "individual_results" in result

    def test_get_standards_recommendations_heat_tracing(self):
        """Test getting standards recommendations for heat tracing application."""
        project_data = {
            "location": "North America",
            "industry": "petrochemical",
            "hazardous_area": False,
            "offshore": False
        }
        
        recommendations = self.service.get_standards_recommendations(
            project_data=project_data,
            application_type="heat_tracing"
        )
        
        assert "recommended_standards" in recommendations
        assert "optional_standards" in recommendations
        assert "reasoning" in recommendations
        
        # Should recommend IEEE 515 for heat tracing
        assert "IEEE-515-2017" in recommendations["recommended_standards"]

    def test_get_standards_recommendations_hazardous_area(self):
        """Test getting standards recommendations for hazardous area application."""
        project_data = {
            "location": "International",
            "industry": "oil_and_gas",
            "hazardous_area": True,
            "offshore": False
        }
        
        recommendations = self.service.get_standards_recommendations(
            project_data=project_data,
            application_type="heat_tracing"
        )
        
        # Should recommend IEC standards for hazardous areas
        assert "IEC-60079-30-1" in recommendations["recommended_standards"]

    def test_get_standards_recommendations_offshore(self):
        """Test getting standards recommendations for offshore application."""
        project_data = {
            "location": "Gulf of Mexico",
            "industry": "petroleum",
            "hazardous_area": True,
            "offshore": True
        }
        
        recommendations = self.service.get_standards_recommendations(
            project_data=project_data,
            application_type="heat_tracing"
        )
        
        # Should recommend API standards for offshore
        assert "API-RP-14F" in recommendations["recommended_standards"]
        assert "API-RP-14FZ" in recommendations["recommended_standards"]

    def test_get_compliance_summary(self):
        """Test getting compliance summary."""
        # Generate some validation history first
        self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=["IEEE-515-2017"]
        )
        
        summary = self.service.get_compliance_summary()
        
        assert "summary_metadata" in summary
        assert "validation_statistics" in summary
        assert "calculation_statistics" in summary
        assert "standards_usage" in summary

    def test_get_compliance_summary_filtered(self):
        """Test getting compliance summary filtered by project."""
        project_id = "test_project_123"
        
        summary = self.service.get_compliance_summary(
            project_id=project_id,
            time_period="last_30_days"
        )
        
        assert summary["summary_metadata"]["project_id"] == project_id
        assert summary["summary_metadata"]["time_period"] == "last_30_days"

    def test_validate_design_invalid_input(self):
        """Test design validation with invalid input."""
        invalid_design_data = {}  # Empty design data
        
        with pytest.raises(CalculationError):
            self.service.validate_design_against_standards(
                design_data=invalid_design_data,
                standard_ids=["IEEE-515-2017"]
            )

    def test_validate_design_unknown_standard(self):
        """Test design validation with unknown standard."""
        result = self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=["UNKNOWN-STANDARD-123"]
        )
        
        # Should handle unknown standards gracefully
        assert "UNKNOWN-STANDARD-123" in result["individual_results"]
        unknown_result = result["individual_results"]["UNKNOWN-STANDARD-123"]
        assert unknown_result["success"] is False

    def test_calculate_parameters_invalid_input(self):
        """Test parameter calculations with invalid input."""
        invalid_input_data = {}  # Empty input data
        
        result = self.service.calculate_standards_parameters(
            input_data=invalid_input_data,
            standard_ids=["IEEE-515-2017"]
        )
        
        # Should handle gracefully and return results
        assert "calculation_summary" in result
        assert "individual_results" in result

    def test_cross_standard_conflict_detection(self):
        """Test cross-standard conflict detection."""
        # Use design data that might cause conflicts between standards
        conflicting_data = self.sample_design_data.copy()
        conflicting_data["voltage"] = 230  # Non-standard in IEEE but common in IEC
        
        result = self.service.validate_design_against_standards(
            design_data=conflicting_data,
            standard_ids=["IEEE-515-2017", "IEC-62395"]
        )
        
        assert "cross_standard_conflicts" in result
        # May or may not have conflicts depending on implementation

    def test_consolidated_parameters(self):
        """Test parameter consolidation across standards."""
        result = self.service.calculate_standards_parameters(
            input_data=self.sample_input_data,
            standard_ids=["IEEE-515-2017", "IEC-62395"]
        )
        
        consolidated = result["consolidated_parameters"]
        assert isinstance(consolidated, dict)
        
        # Check that parameters from multiple standards are consolidated
        for param_name, param_values in consolidated.items():
            assert isinstance(param_values, list)
            if len(param_values) > 1:
                # Should have values from multiple standards
                standards = [pv["standard"] for pv in param_values]
                assert len(set(standards)) > 1

    def test_parameter_comparisons(self):
        """Test parameter comparisons across standards."""
        result = self.service.calculate_standards_parameters(
            input_data=self.sample_input_data,
            standard_ids=["IEEE-515-2017", "IEC-62395"]
        )
        
        comparisons = result["parameter_comparisons"]
        assert isinstance(comparisons, dict)
        assert "parameter_variations" in comparisons
        assert "conflicting_values" in comparisons
        assert "consensus_values" in comparisons

    def test_design_recommendations_generation(self):
        """Test design recommendations generation."""
        result = self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=["IEEE-515-2017"]
        )
        
        recommendations = result["recommendations"]
        assert isinstance(recommendations, list)
        
        # Check recommendation structure
        if len(recommendations) > 0:
            recommendation = recommendations[0]
            assert "source_standard" in recommendation
            assert "recommendation" in recommendation
            assert "priority" in recommendation
            assert "category" in recommendation

    def test_performance_with_multiple_standards(self):
        """Test performance with multiple standards validation."""
        import time
        
        all_standard_ids = list(self.service.standards_registry.keys())
        
        start_time = time.time()
        
        result = self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=all_standard_ids
        )
        
        end_time = time.time()
        validation_time = end_time - start_time
        
        assert result["validation_summary"]["total_standards_checked"] == len(all_standard_ids)
        # Should complete reasonably quickly
        assert validation_time < 30.0  # 30 seconds max

    @patch('core.standards.standards_service.logger')
    def test_logging_functionality(self, mock_logger):
        """Test that appropriate logging occurs during operations."""
        self.service.validate_design_against_standards(
            design_data=self.sample_design_data,
            standard_ids=["IEEE-515-2017"]
        )
        
        # Verify logging calls were made
        assert mock_logger.info.called
        assert mock_logger.debug.called
