# backend/tests/test_utils.py
"""
Test suite for core utilities.

This module tests all the utility functions to ensure they work correctly
and handle edge cases properly.
"""

import pytest
import json
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from unittest.mock import patch

from core.utils import (
    # UUID utilities
    generate_uuid7,
    generate_uuid7_str,
    is_valid_uuid,
    uuid_to_str,
    str_to_uuid,
    
    # DateTime utilities
    utcnow_aware,
    format_datetime,
    parse_datetime,
    convert_timezone,
    calculate_time_difference,
    
    # String utilities
    slugify,
    sanitize_text,
    hash_string,
    truncate_string,
    pad_string,
    
    # File I/O utilities
    FileIOError,
    safe_read_file,
    safe_write_file,
    read_json_file,
    write_json_file,
    temporary_file,
    temporary_directory,
    
    # JSON validation utilities
    validate_json_data,
    JSONValidationError,
    
    # Pagination utilities
    PaginationParams,
    parse_pagination_params,
)


class TestUUIDUtils:
    """Test UUID utility functions."""
    
    def test_generate_uuid7(self):
        """Test UUID7 generation."""
        uuid_obj = generate_uuid7()
        assert uuid_obj is not None
        assert len(str(uuid_obj)) == 36  # Standard UUID string length
    
    def test_generate_uuid7_str(self):
        """Test UUID7 string generation."""
        uuid_str = generate_uuid7_str()
        assert isinstance(uuid_str, str)
        assert len(uuid_str) == 36
        assert is_valid_uuid(uuid_str)
    
    def test_is_valid_uuid(self):
        """Test UUID validation."""
        valid_uuid = "123e4567-e89b-12d3-a456-************"
        invalid_uuid = "not-a-uuid"
        
        assert is_valid_uuid(valid_uuid) is True
        assert is_valid_uuid(invalid_uuid) is False
        assert is_valid_uuid("") is False
        assert is_valid_uuid(None) is False
    
    def test_uuid_conversion(self):
        """Test UUID string conversion."""
        uuid_obj = generate_uuid7()
        uuid_str = uuid_to_str(uuid_obj)
        converted_back = str_to_uuid(uuid_str)
        
        assert uuid_str == str(uuid_obj)
        assert converted_back == uuid_obj
    
    def test_invalid_uuid_conversion(self):
        """Test invalid UUID conversion raises error."""
        with pytest.raises(ValueError):
            str_to_uuid("invalid-uuid")


class TestDateTimeUtils:
    """Test datetime utility functions."""
    
    def test_utcnow_aware(self):
        """Test UTC now with timezone awareness."""
        dt = utcnow_aware()
        assert dt.tzinfo is not None
        assert dt.tzinfo == timezone.utc
    
    def test_format_datetime(self):
        """Test datetime formatting."""
        dt = datetime(2023, 12, 25, 15, 30, 45, tzinfo=timezone.utc)
        formatted = format_datetime(dt)
        
        assert isinstance(formatted, str)
        assert "2023-12-25" in formatted
        assert "15:30:45" in formatted
    
    def test_parse_datetime(self):
        """Test datetime parsing."""
        datetime_str = "2023-12-25T15:30:45Z"
        parsed = parse_datetime(datetime_str)
        
        assert isinstance(parsed, datetime)
        assert parsed.year == 2023
        assert parsed.month == 12
        assert parsed.day == 25
    
    def test_calculate_time_difference(self):
        """Test time difference calculation."""
        start = datetime(2023, 1, 1, 12, 0, 0)
        end = datetime(2023, 1, 1, 13, 0, 0)
        
        diff_seconds = calculate_time_difference(start, end, "seconds")
        diff_minutes = calculate_time_difference(start, end, "minutes")
        diff_hours = calculate_time_difference(start, end, "hours")
        
        assert diff_seconds == 3600
        assert diff_minutes == 60
        assert diff_hours == 1


class TestStringUtils:
    """Test string utility functions."""
    
    def test_slugify(self):
        """Test string slugification."""
        assert slugify("Hello World!") == "hello-world"
        assert slugify("Café & Restaurant") == "cafe-restaurant"
        assert slugify("Test   Multiple   Spaces") == "test-multiple-spaces"
        assert slugify("") == ""
    
    def test_slugify_with_options(self):
        """Test slugify with custom options."""
        text = "Hello World Test"
        assert slugify(text, max_length=10) == "hello-worl"
        assert slugify(text, separator="_") == "hello_world_test"
        assert slugify(text, lowercase=False) == "Hello-World-Test"
    
    def test_sanitize_text(self):
        """Test text sanitization."""
        html_text = "<p>Hello <b>World</b>!</p>"
        sanitized = sanitize_text(html_text)
        assert "<p>" not in sanitized
        assert "<b>" not in sanitized
        assert "Hello World!" in sanitized
    
    def test_hash_string(self):
        """Test string hashing."""
        text = "Hello World"
        hash_md5 = hash_string(text, "md5")
        hash_sha256 = hash_string(text, "sha256")
        
        assert len(hash_md5) == 32  # MD5 hex length
        assert len(hash_sha256) == 64  # SHA256 hex length
        assert hash_md5 != hash_sha256
    
    def test_truncate_string(self):
        """Test string truncation."""
        text = "This is a very long string that needs to be truncated"
        truncated = truncate_string(text, 20)
        
        assert len(truncated) <= 20
        assert truncated.endswith("...")
    
    def test_pad_string(self):
        """Test string padding."""
        text = "Hello"
        
        left_padded = pad_string(text, 10, align="left")
        right_padded = pad_string(text, 10, align="right")
        center_padded = pad_string(text, 10, align="center")
        
        assert len(left_padded) == 10
        assert len(right_padded) == 10
        assert len(center_padded) == 10
        assert left_padded.startswith("Hello")
        assert right_padded.endswith("Hello")


class TestFileIOUtils:
    """Test file I/O utility functions."""
    
    def test_temporary_file(self):
        """Test temporary file creation."""
        with temporary_file(suffix=".txt") as temp_path:
            assert temp_path.exists() is False  # File not created yet
            
            # Write to temporary file
            safe_write_file(temp_path, "Hello World")
            assert temp_path.exists()
            
            # Read from temporary file
            content = safe_read_file(temp_path)
            assert content == "Hello World"
        
        # File should be deleted after context
        assert temp_path.exists() is False
    
    def test_temporary_directory(self):
        """Test temporary directory creation."""
        with temporary_directory() as temp_dir:
            assert temp_dir.exists()
            assert temp_dir.is_dir()
            
            # Create file in temporary directory
            test_file = temp_dir / "test.txt"
            safe_write_file(test_file, "Test content")
            assert test_file.exists()
        
        # Directory should be deleted after context
        assert temp_dir.exists() is False
    
    def test_json_file_operations(self):
        """Test JSON file read/write operations."""
        test_data = {
            "name": "Test Project",
            "version": "1.0.0",
            "settings": {
                "debug": True,
                "max_users": 100
            }
        }
        
        with temporary_file(suffix=".json") as temp_path:
            # Write JSON file
            write_json_file(temp_path, test_data)
            assert temp_path.exists()
            
            # Read JSON file
            loaded_data = read_json_file(temp_path)
            assert loaded_data == test_data
    
    def test_file_io_error_handling(self):
        """Test file I/O error handling."""
        non_existent_file = Path("/non/existent/path/file.txt")
        
        with pytest.raises(FileIOError):
            safe_read_file(non_existent_file)
        
        with pytest.raises(FileIOError):
            read_json_file(non_existent_file)


class TestJSONValidation:
    """Test JSON validation utilities."""
    
    def test_validate_json_data_success(self):
        """Test successful JSON validation."""
        from pydantic import BaseModel
        
        class TestSchema(BaseModel):
            name: str
            age: int
            active: bool = True
        
        valid_data = {"name": "John", "age": 30}
        result = validate_json_data(valid_data, TestSchema)
        
        assert result.name == "John"
        assert result.age == 30
        assert result.active is True  # Default value
    
    def test_validate_json_data_failure(self):
        """Test JSON validation failure."""
        from pydantic import BaseModel
        
        class TestSchema(BaseModel):
            name: str
            age: int
        
        invalid_data = {"name": "John"}  # Missing required 'age' field
        
        with pytest.raises(JSONValidationError) as exc_info:
            validate_json_data(invalid_data, TestSchema)
        
        assert "validation failed" in str(exc_info.value)
        assert exc_info.value.validation_errors is not None


class TestPaginationUtils:
    """Test pagination utility functions."""
    
    def test_pagination_params_validation(self):
        """Test pagination parameter validation."""
        # Valid parameters
        params = PaginationParams(page=2, per_page=20)
        assert params.page == 2
        assert params.per_page == 20
        
        # Invalid parameters should be corrected
        params = PaginationParams(page=0, per_page=-5)
        assert params.page == 1  # Corrected to minimum
        assert params.per_page == 10  # Corrected to default
        
        # Exceeding max_per_page
        params = PaginationParams(per_page=200, max_per_page=100)
        assert params.per_page == 100  # Capped at max
    
    def test_parse_pagination_params(self):
        """Test pagination parameter parsing."""
        params = parse_pagination_params(page=3, per_page=25)
        assert params.page == 3
        assert params.per_page == 25
        
        # Test with None values (should use defaults)
        params = parse_pagination_params()
        assert params.page == 1
        assert params.per_page == 10


class TestIntegration:
    """Integration tests for utility combinations."""
    
    def test_uuid_datetime_combination(self):
        """Test using UUID and datetime utilities together."""
        uuid_str = generate_uuid7_str()
        timestamp = utcnow_aware()
        formatted_time = format_datetime(timestamp)
        
        # Create a record-like structure
        record = {
            "id": uuid_str,
            "created_at": formatted_time,
            "slug": slugify(f"record-{uuid_str[:8]}")
        }
        
        assert is_valid_uuid(record["id"])
        assert "T" in record["created_at"]  # ISO format
        assert record["slug"].startswith("record-")
    
    def test_file_json_validation_combination(self):
        """Test file I/O with JSON validation."""
        from pydantic import BaseModel
        
        class ConfigSchema(BaseModel):
            app_name: str
            debug: bool
            max_connections: int = 100
        
        config_data = {
            "app_name": "Test App",
            "debug": True
        }
        
        with temporary_file(suffix=".json") as temp_path:
            # Write and validate JSON
            write_json_file(temp_path, config_data)
            loaded_data = read_json_file(temp_path)
            
            # Validate with Pydantic schema
            validated_config = validate_json_data(loaded_data, ConfigSchema)
            
            assert validated_config.app_name == "Test App"
            assert validated_config.debug is True
            assert validated_config.max_connections == 100  # Default value
